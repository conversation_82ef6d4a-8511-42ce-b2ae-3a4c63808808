name: Prisma Schema and Seed Test

on:
  push:
    branches: [main, master, develop]
  pull_request:
    branches: [main, master, develop]
  workflow_dispatch:

jobs:
  test-schema-and-seed:
    runs-on: ubuntu-latest

    defaults:
      run:
        working-directory: ./app

    services:
      postgres:
        image: postgres:16
        env:
          POSTGRES_USER: root
          POSTGRES_PASSWORD: root
          POSTGRES_DB: mathlete
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt

      - name: Create .env file
        run: cp .env.template .env

      - name: Generate Prisma client
        run: prisma generate

      - name: Apply database migrations
        run: prisma migrate deploy

      - name: Run seed script
        run: python seed.py

      - name: Run all py tests
        run: pytest tests/ -v
