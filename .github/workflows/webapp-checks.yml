name: <PERSON>app Checks

on:
  pull_request:
    paths:
      - 'webapp/**'
  push:
    branches:
      - main

defaults:
  run:
    working-directory: webapp

jobs:
  lint:
    name: lint
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup Bun
        uses: oven-sh/setup-bun@v2
      - name: Authenticate with private NPM package
        run: |
          cat > bunfig.toml << EOL
          [install.scopes]
          "@tiptap-pro" = { url = "https://registry.tiptap.dev/", token = "${{ secrets.TIPTAP_PRO_TOKEN }}" }
          EOL
      - name: Install
        run: bun install
      - name: Lint
        run: bun lint

  types:
    name: types
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup Bun
        uses: oven-sh/setup-bun@v2
      - name: Authenticate with private NPM package
        run: |
          cat > bunfig.toml << EOL
          [install.scopes]
          "@tiptap-pro" = { url = "https://registry.tiptap.dev/", token = "${{ secrets.TIPTAP_PRO_TOKEN }}" }
          EOL
      - name: Install
        run: bun install
      - name: Build Prisma types
        working-directory: app
        run: bun add prisma && bun run prisma generate --generator client-js
      - name: Type Check
        run: bun types

