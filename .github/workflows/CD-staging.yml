name: Staging CD

on:
  workflow_dispatch:
jobs:
  deploy:
    permissions:
      contents: "read"
      id-token: "write"

    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: "actions/checkout@v4"

      - name: Copy environment variables from templates
        run: |
          cp ./app/.env.template ./.env

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install dependencies
        run: pip install prisma

      - name: Generate Prisma client
        run: prisma generate --schema=./app/prisma/schema.prisma

      - name: Fill environment variables from Github variables & secrets
        env:
          # Database connection parameters
          DATABASE_USER: ${{ vars.DATABASE_USER }}
          DATABASE_PASSWORD: ${{ secrets.DATABASE_PASSWORD}}
          DATABASE_NAME: ${{ vars.DATABASE_NAME }}
          DATABASE_HOST: ${{ vars.DATABASE_HOST }}
          DATABASE_PORT: ${{ vars.DATABASE_PORT }}
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          TOGETHER_API_KEY: ${{ secrets.TOGETHER_API_KEY }}
        run: |
          # Replace placeholders in ./app/.env file with staging values
          sed -i "s|DATABASE_USER=.*|DATABASE_USER=\"$DATABASE_USER\"|" ./.env
          sed -i "s|DATABASE_PASSWORD=.*|DATABASE_PASSWORD=\"$DATABASE_PASSWORD\"|" ./.env
          sed -i "s|DATABASE_NAME=.*|DATABASE_NAME=\"$DATABASE_NAME\"|" ./.env
          sed -i "s|DATABASE_HOST=.*|DATABASE_HOST=\"$DATABASE_HOST\"|" ./.env
          sed -i "s|DATABASE_PORT=.*|DATABASE_PORT=\"$DATABASE_PORT\"|" ./.env
          sed -i "s|OPENAI_API_KEY=.*|OPENAI_API_KEY=\"$OPENAI_API_KEY\"|" ./.env
          sed -i "s|TOGETHER_API_KEY=.*|TOGETHER_API_KEY=\"$TOGETHER_API_KEY\"|" ./.env

          DB_URL_TO_WRITE="postgresql://${DATABASE_USER}:${DATABASE_PASSWORD}@postgres:${DATABASE_PORT}/${DATABASE_NAME}"
          # no use " in the DB_URL_TO_WRITE
          sed -i "s|^DATABASE_URL=.*|DATABASE_URL=$DB_URL_TO_WRITE|" ./.env

      - name: Backend - Build Docker image
        run: |
          docker build -t ${{ vars.DOCKER_REPO_REGION }}-docker.pkg.dev/${{ vars.PROJECT_ID_PRODUCTION }}/${{ vars.DOCKER_REPO_NAME }}/mathlate-backend:${{ github.sha }} ./app

      - name: Frontend - Build Docker image
        env:
          TIPTAP_PRO_TOKEN: ${{ secrets.TIPTAP_NPM_TOKEN }}
        run: |
          docker build --memory=4g \
            --build-arg TIPTAP_PRO_TOKEN=$TIPTAP_PRO_TOKEN \
            -t ${{ vars.DOCKER_REPO_REGION }}-docker.pkg.dev/${{ vars.PROJECT_ID_PRODUCTION }}/${{ vars.DOCKER_REPO_NAME }}/mathlate-frontend:${{ github.sha }} \
            ./webapp

      - id: "auth"
        name: "GCP auth"
        uses: "google-github-actions/auth@v2"
        with:
          service_account: "github-actions-mathviewer@${{ vars.PROJECT_ID_PRODUCTION }}.iam.gserviceaccount.com"
          workload_identity_provider: "projects/************/locations/global/workloadIdentityPools/github-actions-pool/providers/mathviewer"

      - name: "Set up Cloud SDK"
        uses: "google-github-actions/setup-gcloud@v2"

      - name: "Configure Docker to use gcloud as credential helper"
        run: |
          gcloud auth configure-docker ${{ vars.DOCKER_REPO_REGION }}-docker.pkg.dev

      - name: Push Docker images to Artifact Registry
        run: |
          docker push ${{ vars.DOCKER_REPO_REGION }}-docker.pkg.dev/${{ vars.PROJECT_ID_PRODUCTION }}/${{ vars.DOCKER_REPO_NAME }}/mathlate-backend:${{ github.sha }}
          docker push ${{ vars.DOCKER_REPO_REGION }}-docker.pkg.dev/${{ vars.PROJECT_ID_PRODUCTION }}/${{ vars.DOCKER_REPO_NAME }}/mathlate-frontend:${{ github.sha }}

      - name: "Environment variables - Fill version for Docker compose"
        run: |
          echo "BACKEND_DOCKER_IMAGE=${{ vars.DOCKER_REPO_REGION }}-docker.pkg.dev/${{ vars.PROJECT_ID_PRODUCTION }}/${{ vars.DOCKER_REPO_NAME }}/mathlate-backend:${{ github.sha }}" >> .env
          echo "FRONTEND_DOCKER_IMAGE=${{ vars.DOCKER_REPO_REGION }}-docker.pkg.dev/${{ vars.PROJECT_ID_PRODUCTION }}/${{ vars.DOCKER_REPO_NAME }}/mathlate-frontend:${{ github.sha }}" >> .env

      - name: "Copy Docker compose YAML and configurations to VM"
        run: |
          gcloud compute scp --recurse ./app ./compose-prod.yaml ./nginx.conf .env ${{ vars.INSTANCE_ID_PRODUCTION }}:/home/<USER>/ \
            --zone=europe-west1-d \
            --project=${{ vars.PROJECT_ID_PRODUCTION }}

      - name: "Start PostgreSQL service for migration"
        run: |
          gcloud compute ssh ${{ vars.INSTANCE_ID_PRODUCTION }} --zone=europe-west1-d --project=${{ vars.PROJECT_ID_PRODUCTION }} --command=" \
            cd /home/<USER>/ && \
            sudo docker compose -f compose-prod.yaml up -d postgres \
          "

      - name: "Run database migration using migrator container"
        run: |
          gcloud compute ssh ${{ vars.INSTANCE_ID_PRODUCTION }} --zone=europe-west1-d --project=${{ vars.PROJECT_ID_PRODUCTION }} --command=" \
            cd /home/<USER>/ && \
            sudo docker compose -f compose-prod.yaml run --rm --entrypoint bash migrator -c ' \
              apt-get update -y && apt-get install -y openssl && \
              npm install -g prisma && \
              PRISMA_LOG_LEVEL=info npx prisma migrate status --preview-feature && \
              PRISMA_LOG_LEVEL=info npx prisma migrate deploy --preview-feature \
            '
          "

      - name: "Restart mathlete service on VM"
        run: |
          gcloud compute ssh ${{ vars.INSTANCE_ID_PRODUCTION }} --zone=europe-west1-d --project=${{ vars.PROJECT_ID_PRODUCTION }} --command="sudo docker compose -f /home/<USER>/compose-prod.yaml down && sudo docker compose -f /home/<USER>/compose-prod.yaml up -d"
