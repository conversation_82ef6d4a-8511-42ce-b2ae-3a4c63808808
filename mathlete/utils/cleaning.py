import re


def normalize_latex(text):
    """Convert LaTeX commands to readable text while preserving math structure"""
    # Fractions
    text = re.sub(r"\\frac\{([^}]+)\}\{([^}]+)\}", r"(\1/\2)", text)

    text = re.sub(r"\\left\|", "|", text)
    text = re.sub(r"\\right\|", "|", text)

    # Greek letters
    text = re.sub(
        r"\\(alpha|beta|gamma|delta|epsilon|zeta|eta|theta|kappa|lambda|mu|nu|xi|pi|rho|sigma|tau|phi|chi|psi|omega)\b",
        lambda m: m.group(1),
        text,
        flags=re.IGNORECASE,
    )

    # Math operators
    text = re.sub(r"\\times|\\cdot", "*", text)
    text = re.sub(r"\\pm", "±", text)
    text = re.sub(r"\\div", "/", text)
    text = re.sub(r"\\sqrt\{([^}]+)\}", r"√(\1)", text)
    text = re.sub(r"\\leq|\\le", "≤", text)
    text = re.sub(r"\\geq|\\ge", "≥", text)

    # Exponents and subscripts
    text = re.sub(r"\^\{([^}]+)\}", r"^\1", text)
    text = re.sub(r"_\{([^}]+)\}", r"_\1", text)

    # Integrals and sums
    text = re.sub(r"\\int(?:_\{([^}]+)\})?\^\{([^}]+)\}", r"∫[\1 to \2]", text)
    text = re.sub(r"\\sum_\{([^}]+)\}\^\{([^}]+)\}", r"Σ[\1 to \2]", text)

    # Matrices
    text = re.sub(
        r"\\begin\{matrix\}(.*?)\\end\{matrix\}", r"[matrix:\1]", text, flags=re.DOTALL
    )

    # Vectors
    text = re.sub(r"\\vec\{([^}]+)\}", r"vec_\1", text)

    return text


def disambiguate_symbols(text):
    """Replace symbolic notation with explicit words where needed"""
    # Arrows
    text = re.sub(r"\\rightarrow|\\to|\\Rightarrow", " implies ", text)
    text = re.sub(r"\\leftrightarrow|\\Leftrightarrow", " iff ", text)

    # Set theory
    text = re.sub(r"\\in\b", " in ", text)
    text = re.sub(r"\\subset\b", " subset ", text)
    text = re.sub(r"\\subseteq\b", " subseteq ", text)
    text = re.sub(r"\\cup\b", " union ", text)
    text = re.sub(r"\\cap\b", " intersect ", text)

    # Quantifiers
    text = re.sub(r"\\forall\b", " for all ", text)
    text = re.sub(r"\\exists\b", " there exists ", text)

    # Logic
    text = re.sub(r"\\land\b", " and ", text)
    text = re.sub(r"\\lor\b", " or ", text)
    text = re.sub(r"\\neg\b", " not ", text)

    return text


def clean_whitespace(text):
    """Remove formatting noise"""
    # Remove LaTeX comments
    text = re.sub(r"(?m)^%.*$", "", text)

    # Remove leftover LaTeX commands
    text = re.sub(r"\\[a-z]+\{.*?\}", "", text)

    # Remove excessive whitespace
    text = " ".join(text.split())

    # Remove dollar signs (simple math mode indicators)
    text = text.replace("$", " ")

    return text.strip()


def clean_lean_code(lean_code: str, escape=True) -> str:
    """
    Clean the Lean code by either escaping or removing problematic patterns like $.input.

    :param lean_code: The Lean code to clean.
    :param escape: If True, escape problematic sequences. If False, remove them.
    :return: Cleaned Lean code.
    """
    # Define the regex pattern for problematic sequences (like $.input)
    problematic_pattern = r"\$\.(input)"

    if escape:
        # Escape problematic sequences by adding a backslash before the problematic part
        cleaned_code = re.sub(problematic_pattern, r"\\$\.\1", lean_code)
    else:
        # Remove problematic sequences
        cleaned_code = re.sub(problematic_pattern, "", lean_code)

    return cleaned_code


def preprocess_math_text(text):
    text = normalize_latex(text)
    text = disambiguate_symbols(text)
    text = clean_whitespace(text)
    text = clean_lean_code(text)

    return text
