from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import uuid4

import numpy as np
import pandas as pd
from pydantic import BaseModel, ConfigDict, Field


class ExamInfo(BaseModel):
    exam: Optional[str] = None
    exam_category: List[str] = Field(default_factory=list)


class Metadata(BaseModel):
    question_type: Optional[str] = None
    problem_type: Optional[str] = None
    difficulty_level: Optional[int] = None
    mathematical_techniques: Optional[List[str]] = None
    mathematical_results: Optional[List[str]] = None
    tags: Optional[List[str]] = None


class MathProblem(BaseModel):
    uuid: str
    informal_statement: str
    formal_statement: str
    informal_proof: str
    lean4_solution: Optional[str] = None
    exam_info: Optional["ExamInfo"] = None
    title: Optional[str] = None
    metadata: Optional["Metadata"] = None
    embedding: Optional[np.ndarray] = None

    model_config = ConfigDict(arbitrary_types_allowed=True)

    def to_dict(self) -> Dict[str, Any]:
        return self.model_dump()

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "MathProblem":
        return cls(**data)


class MathProblemCollection:
    def __init__(self, problems: List[MathProblem]):
        self.problems = problems

    def __iter__(self):
        return iter(self.problems)

    def __len__(self):
        return len(self.problems)

    def __bool__(self):
        return len(self.problems) > 0

    @classmethod
    def from_dataframe(cls, df: pd.DataFrame) -> "MathProblemCollection":
        problems = []

        def safe_get(field, default=None, cast_type=None, expect_list=False):
            val = row.get(field, default)

            if isinstance(val, (np.ndarray, pd.Series)):
                if val.size == 0:
                    return default if not expect_list else []

                if val.size == 1:
                    val = val.item()
                else:
                    val = val.tolist()

            if val is None:
                return default if not expect_list else []

            if isinstance(val, (float, np.floating)) and np.isnan(val):
                return default if not expect_list else []

            if not isinstance(val, list) and pd.isna(val):
                return default if not expect_list else []

            if isinstance(val, str) and not val.strip():
                return default if not expect_list else []

            if expect_list:
                if isinstance(val, str):
                    val = val.strip("[]")
                    val = [
                        item.strip().strip("'\"")
                        for item in val.split(",")
                        if item.strip()
                    ]
                elif not isinstance(val, list):
                    val = [val]

                if cast_type:
                    try:
                        val = [
                            cast_type(item)
                            for item in val
                            if item is not None and str(item).strip()
                        ]
                    except (ValueError, TypeError):
                        return []
                return val

            if cast_type:
                try:
                    val = cast_type(val)
                except (ValueError, TypeError):
                    return default

            return val

        for _, row in df.iterrows():
            exam_info = ExamInfo(
                exam=safe_get("exam", cast_type=str),
                exam_category=safe_get("exam_category", default=[], expect_list=True),
            )

            metadata = Metadata(
                question_type=safe_get("question_type", cast_type=str),
                problem_type=safe_get("problem_type", cast_type=str),
                difficulty_level=safe_get("difficulty_level", cast_type=str),
                mathematical_techniques=safe_get(
                    "mathematical_techniques", default=[], expect_list=True
                ),
                mathematical_results=safe_get(
                    "mathematical_results", default=[], expect_list=True
                ),
            )

            raw_embedding = row.get("embedding", None)
            if isinstance(raw_embedding, str):
                try:
                    embedding = np.array(eval(raw_embedding))
                except Exception:
                    embedding = None
            elif isinstance(raw_embedding, (list, np.ndarray)):
                embedding = np.array(raw_embedding)
            else:
                embedding = None

            problem = MathProblem(
                uuid=safe_get("uuid", cast_type=str),
                informal_statement=safe_get("problem", cast_type=str, default=""),
                formal_statement=safe_get("lean_code", default=""),
                informal_proof=safe_get("informal_solution", cast_type=str, default=""),
                lean4_solution=row.get("lean4_solution"),
                exam_info=exam_info,
                title=safe_get("problem_name", cast_type=str, default=""),
                metadata=metadata,
                embedding=embedding,
            )

            problems.append(problem)

        return cls(problems)


class UserProofAttempt(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid4()))
    problem: MathProblem
    user_id: str
    user_informal_proof: str
    created_at: datetime = Field(default_factory=datetime.now)

    @classmethod
    def create_new(
        cls, problem: MathProblem, user_id: str, user_proof: str
    ) -> "UserProofAttempt":
        """Create a new attempt with auto-generated ID and current timestamp"""
        return cls(problem=problem, user_id=user_id, user_informal_proof=user_proof)

    @classmethod
    def from_dict(
        cls, data: Dict[str, Any], problem: MathProblem
    ) -> "UserProofAttempt":
        return cls(**data, problem=problem)

    def to_dict(self) -> Dict[str, Any]:
        return self.model_dump()
