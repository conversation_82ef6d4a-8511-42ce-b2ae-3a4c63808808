from typing import List, Optional, Union

import numpy as np

from mathlete.utils.constants import MathProblem, MathProblemCollection


class ProblemManager:
    def __init__(
        self, problems: Union[List[MathProblem], MathProblemCollection, None] = None
    ) -> None:
        self.problems = problems
        self._problems_by_id = {}  # problem_id -> MathProblem
        self._problem_ids = []

        if problems:
            if isinstance(problems, MathProblemCollection):
                problem_list = problems.problems
            else:
                problem_list = problems

            for problem in problem_list:
                self.add_problem(problem)

    @property
    def has_problems(self) -> bool:
        return bool(self._problem_ids)

    @property
    def problem_ids(self) -> List[str]:
        return list(self._problem_ids)

    def add_problem(self, problem: MathProblem):
        problem_id = problem.uuid
        if problem_id not in self._problems_by_id:
            self._problem_ids.append(problem_id)
        self._problems_by_id[problem_id] = problem

    def get_problem_ids(self) -> List[str]:
        return self._problem_ids.copy()

    def get_problem(self, problem_id: str) -> Optional[MathProblem]:
        return self._problems_by_id.get(problem_id)

    def get_problem_embedding(self, problem_id: str) -> Optional[np.ndarray]:
        problem = self.get_problem(problem_id)
        return problem.embedding if problem and problem.embedding is not None else None

    def get_problem_index(self, problem_id: str) -> Optional[int]:
        try:
            return self._problem_ids.index(problem_id)
        except ValueError:
            return None

    def get_problem_indices(self, problem_ids: List[str]) -> List[int]:
        return [
            self.get_problem_index(pid)
            for pid in problem_ids
            if self.get_problem_index(pid) is not None
        ]

    def get_problem_difficulty(self, problem_id: str) -> int:
        problem = self.get_problem(problem_id)
        if not problem or not problem.metadata or not problem.metadata.difficulty_level:
            return 0
        try:
            return int(problem.metadata.difficulty_level)
        except ValueError:
            return 0
