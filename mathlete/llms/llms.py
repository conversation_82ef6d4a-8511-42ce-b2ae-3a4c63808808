import dataclasses
import json
import time

from loguru import logger
from openai import OpenAI
from pydantic import BaseModel
from together import Together

from .json_extractor import JSONExtractor
from .prompt import Prompt
from .prompt_cache import get_prompt_result, save_prompt_result


def run_openai(prompt, model, output_class):
    client = OpenAI()
    messages = [
        {"role": "system", "content": prompt.system},
        {"role": "user", "content": prompt.user},
    ]

    response = client.beta.chat.completions.parse(
        model=model,
        messages=messages,
        response_format=output_class,
    )
    return response.choices[0].message.parsed


def run_vllm(prompt, output_class):
    client = OpenAI(api_key="EMPTY", base_url="http://localhost:9090/v1")
    model = client.models.list().data[0].id
    logger.info(f"Calling model {model} from vllm server")

    messages = [
        {"role": "system", "content": prompt.system},
        {"role": "user", "content": prompt.user},
    ]
    json_schema = output_class.model_json_schema()
    completion = client.chat.completions.create(
        model=model,
        messages=messages,
        extra_body={"guided_json": json_schema},
    )
    result = json.loads(completion.choices[0].message.content)
    return output_class.from_dict(result)


def run_together(prompt, output_class):
    client = Together()
    combined_prompt = f"{prompt.system}\n{prompt.user}"
    response = (
        client.chat.completions.create(
            # model="deepseek-ai/DeepSeek-R1-Distill-Qwen-14B",
            model="deepseek-ai/DeepSeek-R1-Distill-Llama-70B",
            messages=[{"role": "user", "content": combined_prompt}],
            temperature=0.6,
        )
        .choices[0]
        .message.content
    )
    logger.info(f"Raw response: {response}")

    json_extractor = JSONExtractor()
    return json_extractor.extract_json(response, output_class)


def _run_prompt(prompt: Prompt, model: str, output_class):
    logger.info("Run LLM prompt: " + json.dumps(dataclasses.asdict(prompt), indent=4))
    start_time = time.time()

    # It is best not to use the inference model, otherwise it may time out
    openai_models = [
        "gpt-4.1-mini-2025-04-14",
        "gpt-4.1-2025-04-14",
        "o4-mini-2025-04-16",
        "gpt-4o-mini-2024-07-18",
        "gpt-4o-2024-08-06",
    ]

    model_runners = {
        **{model: run_openai for model in openai_models},
        **{"vllm": run_vllm, "together": run_together},
    }

    if not model:
        model = "gpt-4o-2024-08-06"

    if model not in model_runners:
        raise ValueError(f"Unsupported model: {model}")

    if model in openai_models:
        result = model_runners[model](prompt, model, output_class)
    else:
        result = model_runners[model](prompt, output_class)

    logger.info(f"LLM Output: {result}")
    logger.info(f"Got LLM response: {time.time() - start_time:.1f} seconds")
    return result


def execute_prompt(
    prompt: Prompt, output_class: str, model: str = "gpt-4o-2024-08-06"
) -> str:
    cache_hit = get_prompt_result(prompt, model)
    if cache_hit is not None:
        logger.info("Prompt cache hit")
        if issubclass(output_class, BaseModel):
            return output_class.model_validate(cache_hit)
        elif hasattr(output_class, "from_dict"):
            return output_class.from_dict(cache_hit)
        else:
            return output_class(cache_hit)
    result = _run_prompt(prompt, model, output_class)
    save_prompt_result(prompt, model, result)
    return result
