from typing import Any, Dict, List, Optional, overload

from pydantic import BaseModel, Field


class Filters(BaseModel):
    problem_type: Optional[str] = Field(..., description="The type of problem")
    question_type: Optional[str] = Field(..., description="The type of question")
    difficulty: Optional[List[int]] = Field(
        ..., description="The difficulty of the problem"
    )
    exam_category: Optional[str] = Field(..., description="The exam category")
    mathematical_techniques: Optional[List[str]] = Field(
        ..., description="The mathematical techniques used in the problem"
    )
    mathematical_results: Optional[List[str]] = Field(
        ..., description="The mathematical results used in the problem"
    )

    @overload
    def get(self, key: str) -> Any: ...

    @overload
    def get(self, key: str, default: Any) -> Any: ...

    def get(self, key: str, default: Any = ...) -> Any:
        """
        Get a filter value by key.
        """
        if default is ...:
            return getattr(self, key)
        else:
            return getattr(self, key, default)


class QueryDecomposition(BaseModel):
    filters: Optional[Filters] = Field(description="Filters for specific fields")
    valid_query: bool = Field(
        description="Indicates whether the query is considered valid after processing."
    )
    reason: Optional[str] = Field(
        default=None,
        description="Provides a reason for invalidity, if the query is not valid.",
    )


class FinalQuery(BaseModel):
    text_search: str = Field(..., description="The original user query")
    decomposition: QueryDecomposition = Field(
        ..., description="LLM-processed query decomposition"
    )
