from collections import defaultdict
from typing import List

import pandas as pd
from loguru import logger

from mathlete.utils.constants import MathProblemCollection
from mathlete.utils.problem_manager import ProblemManager


def select_diverse_difficulty_problems(
    ranked_uuids: List[str], problem_manager: ProblemManager, max_results: int = 4
) -> List[str]:
    """
    From a ranked list of problems uuids, it return `max_results` problems uuid that are diverse in difficulty.
    The function attempts to pick one problem from each difficulty bin (easy, medium-easy, medium-hard, hard), based on metadata, and
    fills in gaps using nearby difficulty bins.

    Parameters
    ----------
    ranked_uuids : List[str]
        Ranked list of problem UUIDs returned by a search system, ordered by relevance.

    problem_manager : ProblemManager
        An instance of ProblemManager used to retrieve problems and their difficulty levels.

    max_results : int, optional
        Maximum number of problems to return (default is 4).

    Returns
    -------
    List[str]
        A list of selected UUIDs, at most `max_results` long, covering a range of difficulties.
        The selected problems are drawn in order of bin priority and search relevance.
    """
    bins = {
        "easy": range(1, 4),
        "med_easy": range(4, 6),
        "med_hard": range(6, 8),
        "hard": range(8, 11),
    }
    bin_order = ["easy", "med_easy", "med_hard", "hard"]
    neighbor_bins = {
        "easy": ["med_easy"],
        "med_easy": ["easy", "med_hard"],
        "med_hard": ["med_easy", "hard"],
        "hard": ["med_hard"],
    }

    bucketed = defaultdict(list)
    for uuid in ranked_uuids:
        difficulty = problem_manager.get_problem_difficulty(uuid)
        for label, bin_range in bins.items():
            if difficulty in bin_range:
                bucketed[label].append(uuid)
                break

    selected = []
    used_uuids = set()

    # Primary selection: one from each bin if available
    for label in bin_order:
        if bucketed[label]:
            chosen = bucketed[label][0]
            selected.append(chosen)
            used_uuids.add(chosen)

    # Fill in missing bins using neighbors
    for label in bin_order:
        if label not in bucketed or not bucketed[label]:
            for neighbor in neighbor_bins[label]:
                for candidate in bucketed[neighbor][1:]:
                    if candidate not in used_uuids:
                        logger.debug(f"Adding {candidate} from {neighbor}")
                        selected.append(candidate)
                        used_uuids.add(candidate)
                        break
                if len(selected) >= max_results:
                    break
        if len(selected) >= max_results:
            break

    # If still not enough, fill with any remaining unused UUIDs
    for uuid in ranked_uuids:
        if uuid not in used_uuids and len(selected) < max_results:
            logger.debug(f"Adding {uuid} from remaining")
            selected.append(uuid)
            used_uuids.add(uuid)

    return selected[:max_results]


def select_from_search_results(search_output: dict, max_results: int = 4) -> List[str]:
    """
    Extracts and selects a diverse set of math problems from search results
    returned by the search system.

    Parameters
    ----------
    search_output : dict
        Dictionary returned by the search system. Must include a "results" key
        with a list of problem records.
    max_results : int
        Number of problems to return.

    Returns
    -------
    List[str]
        List of selected problem UUIDs.
    """
    df = pd.DataFrame(search_output["results"])
    collection = MathProblemCollection.from_dataframe(df)
    manager = ProblemManager(collection)
    ranked_uuids = manager.get_problem_ids()
    return select_diverse_difficulty_problems(ranked_uuids, manager, max_results)
