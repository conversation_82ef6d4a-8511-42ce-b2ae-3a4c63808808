from typing import List

from mathlete.search.retrievers._lexical import BM25R<PERSON>riever
from mathlete.search.retrievers._semantic import SemanticRetriever
from mathlete.search.retrievers.base import BaseRetriever, SearchResult


class HybridRetriever(BaseRetriever):
    def __init__(
        self,
        cache_dir: str = ".cache",
        use_stemming: bool = False,
        model_name: str = "sentence-transformers/all-MiniLM-L6-v2",
    ):
        """
        Initialize hybrid retriever.

        Args:
            cache_dir: Directory to store cache files
            use_stemming: Whether to use stemming for BM25
            model_name: Name of the sentence-transformer model to use
        """
        super().__init__(cache_dir)
        self.bm25_retriever = BM25Retriever(cache_dir, use_stemming)
        self.semantic_retriever = SemanticRetriever(cache_dir, model_name)

    def fit(
        self, documents: List[str], force_recompute: bool = False
    ) -> "HybridRetriever":
        """
        Fit both retrievers on the given documents.

        Args:
            documents: List of document strings
            force_recompute: If True, recompute indices even if cache exists

        Returns:
            self for method chaining
        """
        self.bm25_retriever.fit(documents, force_recompute)
        self.semantic_retriever.fit(documents, force_recompute)
        self.is_fitted = True
        return self

    def reciprocal_rank_fusion(
        self,
        results1: List[SearchResult],
        results2: List[SearchResult],
        k: float = 60.0,
    ) -> List[SearchResult]:
        """Combine results using reciprocal rank fusion."""
        doc_ranks1 = {r.doc_id: i + 1 for i, r in enumerate(results1)}
        doc_ranks2 = {r.doc_id: i + 1 for i, r in enumerate(results2)}

        all_docs = set(doc_ranks1.keys()) | set(doc_ranks2.keys())

        rrf_scores = {}
        for doc_id in all_docs:
            rank1 = doc_ranks1.get(doc_id, len(results1) + 1)
            rank2 = doc_ranks2.get(doc_id, len(results2) + 1)

            rrf_scores[doc_id] = 1 / (k + rank1) + 1 / (k + rank2)

        sorted_docs = sorted(rrf_scores.items(), key=lambda x: x[1], reverse=True)

        results = []
        for doc_id, score in sorted_docs:
            doc = next((r for r in results1 + results2 if r.doc_id == doc_id), None)
            if doc:
                results.append(
                    SearchResult(doc_id=doc_id, score=score, content=doc.content)
                )

        return results

    def retrieve(
        self, query: str, top_k: int = 10, use_hybrid: bool = True
    ) -> List[SearchResult]:
        """Retrieve documents using both methods."""
        if not self.is_fitted:
            raise ValueError("Retriever must be fitted before calling retrieve")

        bm25_results = self.bm25_retriever.retrieve(query, top_k)

        if not use_hybrid or not bm25_results:
            return bm25_results

        semantic_results = self.semantic_retriever.retrieve(query, top_k)

        combined_results = self.reciprocal_rank_fusion(bm25_results, semantic_results)

        return combined_results[:top_k]
