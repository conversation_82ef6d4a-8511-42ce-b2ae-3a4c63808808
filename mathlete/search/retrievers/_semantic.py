import hashlib
import os
from typing import List

import torch
from loguru import logger
from sentence_transformers import SentenceTransformer, util

from mathlete.search.retrievers.base import BaseRetriever, SearchResult


class SemanticRetriever(BaseRetriever):
    def __init__(
        self,
        cache_dir: str = ".cache",
        model_name: str = "sentence-transformers/all-MiniLM-L6-v2",
    ):
        super().__init__(cache_dir)
        self.model_name = model_name
        self.model = SentenceTransformer(model_name)
        self.embeddings = None

    def _generate_cache_key(self, documents: List[str]) -> str:
        """Generate a unique cache key including model name."""
        base_key = super()._generate_cache_key(documents)
        return hashlib.md5((base_key + self.model_name).encode()).hexdigest()

    def _compute_embeddings(self, documents: List[str]):
        """Compute embeddings for documents."""
        self.embeddings = self.model.encode(
            documents, convert_to_tensor=True, show_progress_bar=True
        )

    def fit(self, documents: List[str], force_recompute: bool = False):
        """Fit the retriever on the given documents."""
        self.documents = documents
        cache_key = self._generate_cache_key(documents)
        cache_path = os.path.join(self.cache_dir, f"semantic_{cache_key}.pkl")

        if not force_recompute and os.path.exists(cache_path):
            cache_data = self._load_cache(cache_path)
            if cache_data:
                self.documents = cache_data["documents"]
                self.embeddings = torch.tensor(cache_data["embeddings"]).to(
                    self.model.device
                )
                self.is_fitted = True
                logger.info("Loaded semantic embeddings from cache.")
                return self

        logger.info("Computing new semantic embeddings...")
        self._compute_embeddings(documents)

        self._save_cache(
            cache_path,
            {"documents": self.documents, "embeddings": self.embeddings.cpu().numpy()},
        )
        logger.info("Semantic embeddings saved to cache.")

        self.is_fitted = True
        return self

    def retrieve(self, query: str, top_k: int = 10) -> List[SearchResult]:
        """Retrieve similar documents."""
        if not self.is_fitted:
            raise ValueError("Retriever must be fitted before calling retrieve")

        query_embedding = self.model.encode(query, convert_to_tensor=True)

        hits = util.semantic_search(query_embedding, self.embeddings, top_k=top_k)[0]

        results = []
        for hit in hits:
            results.append(
                SearchResult(
                    doc_id=hit["corpus_id"],
                    score=hit["score"],
                    content=self.documents[hit["corpus_id"]],
                )
            )

        return results
