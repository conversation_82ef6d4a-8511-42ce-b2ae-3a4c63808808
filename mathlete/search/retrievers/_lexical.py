import hashlib
import os
from typing import List

import bm25s
from loguru import logger
from Stemmer import St<PERSON>mer

from mathlete.search.retrievers.base import BaseRetriever, SearchResult


class BM25Retriever(BaseRetriever):
    def __init__(self, cache_dir: str = ".cache", use_stemming: bool = False):
        """
        Initialize BM25 retriever with caching support.

        Args:
            cache_dir: Directory to store cache files
            use_stemming: Whether to use stemming
        """
        super().__init__(cache_dir)
        self.use_stemming = use_stemming
        self.stemmer = Stemmer("english") if use_stemming else None
        self.corpus_tokens = None
        self.retriever = None

    def _generate_cache_key(self, documents: List[str]) -> str:
        """Generate a unique cache key including stemming settings."""
        base_key = super()._generate_cache_key(documents)
        return hashlib.md5((base_key + str(self.use_stemming)).encode()).hexdigest()

    def _compute_index(self, documents: List[str]):
        """Compute BM25 index."""
        self.corpus_tokens = bm25s.tokenize(
            documents, stopwords="en", stemmer=self.stemmer
        )
        self.retriever = bm25s.BM25()
        self.retriever.index(self.corpus_tokens)

    def fit(self, documents: List[str], force_recompute: bool = False):
        """Fit BM25 retriever on given documents."""
        self.documents = documents
        cache_key = self._generate_cache_key(documents)
        cache_path = os.path.join(self.cache_dir, f"bm25_{cache_key}.pkl")

        if not force_recompute and os.path.exists(cache_path):
            cache_data = self._load_cache(cache_path)
            if cache_data:
                self.documents = cache_data["documents"]
                self.corpus_tokens = cache_data["corpus_tokens"]
                self.retriever = cache_data["retriever"]
                self.is_fitted = True
                logger.info("Loaded BM25 index from cache.")
                return self

        logger.info("Computing new BM25 index...")
        self._compute_index(documents)

        self._save_cache(
            cache_path,
            {
                "documents": self.documents,
                "corpus_tokens": self.corpus_tokens,
                "retriever": self.retriever,
            },
        )
        logger.info("BM25 index saved to cache.")

        self.is_fitted = True
        return self

    def retrieve(self, query: str, top_k: int = 10) -> List[SearchResult]:
        """Retrieve similar documents."""
        if not self.is_fitted:
            raise ValueError("Retriever must be fitted before calling retrieve")

        query_tokens = bm25s.tokenize(query, stemmer=self.stemmer)
        if not query_tokens:
            raise ValueError("Query tokenization resulted in no tokens")

        top_k = min(top_k, len(self.documents))
        doc_ids, scores = self.retriever.retrieve(query_tokens, k=top_k)

        if len(doc_ids.shape) > 1:
            doc_ids = doc_ids[0]
            scores = scores[0]

        results = []
        for doc_id, score in zip(doc_ids[:top_k], scores[:top_k]):
            results.append(
                SearchResult(
                    doc_id=int(doc_id),
                    score=float(score),
                    content=self.documents[doc_id],
                )
            )

        return sorted(results, key=lambda x: x.score, reverse=True)
