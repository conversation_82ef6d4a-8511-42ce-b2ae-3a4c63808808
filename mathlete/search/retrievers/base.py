import hashlib
import os
import pickle
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Any, List


@dataclass
class SearchResult:
    doc_id: int
    score: float
    content: Any


class BaseRetriever(ABC):
    def __init__(self, cache_dir: str = ".cache"):
        """
        Initialize base retriever with caching support.

        Args:
            cache_dir: Directory to store cache files
        """
        self.cache_dir = cache_dir
        os.makedirs(cache_dir, exist_ok=True)
        self.documents = None
        self.is_fitted = False

    def _generate_cache_key(self, documents: List[str]) -> str:
        """Generate a unique cache key based on input data."""
        data_str = "".join(documents)
        return hashlib.md5(data_str.encode()).hexdigest()

    def _save_cache(self, cache_path: str, data: dict):
        """Save current state to cache."""
        with open(cache_path, "wb") as f:
            pickle.dump(data, f)

    def _load_cache(self, cache_path: str) -> dict:
        """Load state from cache. Returns the data if successful."""
        try:
            with open(cache_path, "rb") as f:
                return pickle.load(f)
        except Exception as e:
            print(f"Warning: Failed to load cache: {e}")
            return None

    @abstractmethod
    def fit(self, documents: List[str], force_recompute: bool = False):
        """Fit the retriever on the given documents."""
        pass

    @abstractmethod
    def retrieve(self, query: str, top_k: int = 10) -> List[SearchResult]:
        """Retrieve relevant documents."""
        pass
