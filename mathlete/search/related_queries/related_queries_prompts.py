import os

from mathlete.llms.prompt import Prompt


class RelatedQueriesPromptGenerator:
    def make_prompt_related_queries(self, original_query: str) -> dict:
        base_dir = os.path.dirname(os.path.abspath(__file__))
        tag_path = os.path.join(
            base_dir, "../../data_processing/extract_tags/allowed_tags.txt"
        )
        tag_path = os.path.normpath(tag_path)
        try:
            with open(tag_path, "r") as f:
                allowed_tags = [line.strip() for line in f if line.strip()]
            allowed_tags_str = ", ".join(allowed_tags)
        except FileNotFoundError:
            raise FileNotFoundError("The file 'allowed_tags.txt' was not found.")
        except Exception as e:
            raise RuntimeError(f"Error reading 'allowed_tags.txt': {e}")

        system = f"""You are an expert mathematics education system with deep knowledge of mathematics curricula across algebra, calculus, geometry, number theory, probability, statistics, and other mathematical domains.

Your task is to suggest three new mathematical problem search queries related to the original query in different ways:

1. DIRECT FOLLOWUP: A query that builds directly on the original query, exploring a logical next step, deeper aspect, or variation of the same mathematical concept or technique. This should feel like the next natural learning progression.

2. CONNECTED TOPIC: A query that explores a related mathematical concept that shares techniques, applications, or is often taught alongside the original topic. This should expand the student's mathematical toolkit with complementary knowledge.

3. DIFFERENT TOPIC: A query that introduces a different mathematical area that might interest a student studying the original topic, possibly through applications or mathematical connections within the same difficulty range.

IMPORTANT CONSTRAINTS - YOU MUST FOLLOW THESE:
- Only suggest topics/problems up to IMO level (high school competitive mathematics)
- DO NOT suggest undergraduate-level mathematics topics
- DO NOT suggest theoretical or proof-based topics unless they commonly appear in high school olympiads
- DO NOT suggest topics involving advanced calculus, abstract algebra, topology, measure theory, or college-level content
- Keep suggestions within typical olympiad mathematics: algebra, number theory, combinatorics, geometry
- You MUST ensure that the queries are relevant to one or more of the following allowed tags:
  {allowed_tags_str}

For each suggested query:
- Format it as a search query a student would actually type when looking for math problems
- Maintain the same approximate difficulty level as the original query
- Keep the style consistent with how mathematical problem sets are typically described
- Make suggestions specific enough to return relevant practice problems
- Focus on mathematical concepts, techniques, and application contexts

Return a JSON object with the following structure:
{{
  "related_queries": [
    {{
      "query": "The direct followup query text",
      "relation_type": "direct_followup",
      "description": "Brief explanation of how this builds on the original query"
    }},
    {{
      "query": "The connected topic query text",
      "relation_type": "connected_topic",
      "description": "Brief explanation of how this relates to the original topic"
    }},
    {{
      "query": "The different topic query text",
      "relation_type": "different_topic",
      "description": "Brief explanation of why this might interest the same student"
    }}
  ]
}}"""

        user = f"""Based on the user's original search query for math problems:
\"{original_query}\"

Please suggest three new search queries for related math problems that the user might be interested in exploring next, following the specifications in your instructions. Remember to maintain the academic nature and appropriate difficulty level of the original query, and make sure all topic areas are among the allowed tags."""

        return Prompt(id="related-queries", system=system, user=user)
