from loguru import logger

from mathlete.llms import llms
from mathlete.search.related_queries.related_queries_models import (
    RelatedQueriesResponse,
)
from mathlete.search.related_queries.related_queries_prompts import (
    RelatedQueriesPromptGenerator,
)


class RelatedQueriesGenerator:
    def __init__(self):
        pass

    def suggest_queries(self, query: str, model: str) -> RelatedQueriesResponse:
        prompt_generator = RelatedQueriesPromptGenerator()
        prompt = prompt_generator.make_prompt_related_queries(query)
        model = model or "gpt-4o-2024-08-06"
        try:
            return llms.execute_prompt(
                prompt=prompt, output_class=RelatedQueriesResponse, model=model
            )

        except Exception as e:
            logger.error(f"Error suggesting related queries: {str(e)}")
            raise RuntimeError(f"Related queries suggestion failed: {str(e)}")
