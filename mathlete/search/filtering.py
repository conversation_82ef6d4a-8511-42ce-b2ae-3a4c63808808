from typing import Dict, List, Union

import pandas as pd
from loguru import logger

from mathlete.search.tags.tags_matcher import MetadataFuzzyMatcher


class FilterError(Exception):
    pass


async def apply_field_filter(
    field: str, values: List[str], df: pd.DataFrame
) -> pd.DataFrame:
    """
    Apply filter for a specific field to the dataframe asynchronously.

    Args:
        field: Field name to filter on
        values: Normalized values to filter with
        df: Input DataFrame

    Returns:
        Filtered DataFrame
    """
    if not values:
        return df

    mask = df[field].notna()
    mask &= df[field].isin(values)

    filtered_df = df[mask]

    if filtered_df.empty:
        logger.warning(f"Filter {field}={values} resulted in an empty DataFrame")

    return filtered_df


async def apply_filters(
    filters: Dict[str, Union[str, List[str]]], df: pd.DataFrame
) -> pd.DataFrame:
    """
    Apply filters to the dataframe with fuzzy matching against valid metadata options asynchronously.

    Args:
        filters: Dictionary where keys are filter fields and values can be either:
            - For string fields (problem_type, question_type, exam_category):
                - A single string (e.g., "Algebra")
                - A list of strings (e.g., ["Algebra", "Geometry"])
        df: Input DataFrame

    Returns:
        Filtered DataFrame

    Raises:
        FilterError: If there's an issue applying filters
    """
    if df is None or df.empty:
        logger.error("Empty or None DataFrame provided")
        raise FilterError("Empty or None DataFrame provided")

    if not filters:
        logger.warning("No filters provided")
        return df.copy()

    try:
        valid_columns = {"problem_type", "question_type", "exam_category", "difficulty"}

        metadata_matcher = MetadataFuzzyMatcher()

        cleaned_filters = {}
        for field, value in filters.items():
            if field not in valid_columns:
                logger.warning(f"Skipping invalid filter field: {field}")
                continue

            if field not in df.columns:
                logger.warning(f"Column {field} not found in DataFrame")
                continue

            if isinstance(value, list):
                if len(value) == 0:
                    logger.warning(f"Skipping empty list for field: {field}")
                    continue
                if all(pd.isna(v) for v in value):
                    logger.warning(f"Skipping all null values for field: {field}")
                    continue
            else:
                if not value or pd.isna(value):
                    logger.warning(f"Skipping null/empty value for field: {field}")
                    continue

            if field == "difficulty":
                if not isinstance(value, list) or len(value) != 2:
                    logger.warning(
                        "Difficulty filter requires a list of two values [min, max]"
                    )
                    continue
                try:
                    min_diff = (
                        float(value[0]) if isinstance(value[0], str) else value[0]
                    )
                    max_diff = (
                        float(value[1]) if isinstance(value[1], str) else value[1]
                    )
                    cleaned_filters[field] = [min_diff, max_diff]
                except (ValueError, TypeError):
                    logger.warning(
                        "Difficulty values must be numbers or string-encoded numbers"
                    )
                    continue
                continue

            if isinstance(value, str):
                value = [value]

            if not isinstance(value, list):
                logger.warning(
                    f"Skipping invalid filter value (must be str or list) for field: {field}"
                )
                continue

            normalized_values = await metadata_matcher.normalize_filter_values(
                field, value
            )

            if normalized_values:
                cleaned_filters[field] = normalized_values

        # Apply filters sequentially
        filtered_df = df.copy()
        initial_count = len(filtered_df)

        for field, values in cleaned_filters.items():
            if field == "difficulty":
                min_diff, max_diff = values
                filtered_df = filtered_df[
                    (filtered_df["difficulty"] >= min_diff)
                    & (filtered_df["difficulty"] <= max_diff)
                ]
            else:
                filtered_df = await apply_field_filter(field, values, filtered_df)

            if filtered_df.empty:
                break

        logger.info(
            f"Applied filters: {cleaned_filters}. "
            f"Rows before: {initial_count}, after: {len(filtered_df)}"
        )

        return filtered_df

    except Exception as e:
        logger.error(f"Error applying filters: {str(e)}")
        raise FilterError(f"Failed to apply filters: {str(e)}")
