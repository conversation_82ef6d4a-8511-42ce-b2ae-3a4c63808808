from enum import Enum
from typing import Any, Dict, List

from pydantic import BaseModel


class UserLevel(Enum):
    BEGINNER = "beginner"
    HIGH_SCHOOL = "high_school"
    UNDERGRADUATE = "undergraduate"
    GRADUATE = "graduate"
    EXPERT = "expert"


class QueryCleanliness(Enum):
    CLEAN = "clean"
    AMBIGUOUS = "ambiguous"
    TYPO = "typo"


class GeneratedQueries(BaseModel):
    topic_based_queries: List[str]
    technique_based_queries: List[str]

    def to_dict(self) -> Dict[str, Any]:
        return self.model_dump()

    @classmethod
    def from_dict(cls, data: Dict[str, Any]):
        return cls(**data)
