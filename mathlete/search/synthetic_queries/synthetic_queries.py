import argparse
import concurrent.futures
import os
import random
from datetime import datetime
from pathlib import Path
from typing import List

import pandas as pd
from loguru import logger
from tqdm import tqdm

from mathlete.llms import llms
from mathlete.search.synthetic_queries.synthetic_queries_models import (
    GeneratedQueries,
    QueryCleanliness,
    UserLevel,
)
from mathlete.search.synthetic_queries.synthetic_queries_prompts import (
    SyntheticQueriesPromptGenerator,
)


class SyntheticQueryGenerator:
    REQUIRED_COLUMNS = {"uuid", "problem"}

    def __init__(self):
        pass

    def generate_queries(
        self,
        problem: str,
        solution: str,
        user_trait: str,
        cleanliness_level: str,
    ) -> List[str]:
        """
        Generates synthetic queries for a given math problem, user trait, and cleanliness level.
        """
        prompt_generator = SyntheticQueriesPromptGenerator()
        prompt = prompt_generator.make_synthetic_queries_prompt(
            problem, solution, user_trait, cleanliness_level
        )
        try:
            return llms.execute_prompt(prompt=prompt, output_class=GeneratedQueries)
        except Exception as e:
            logger.error(f"Error generating queries: {str(e)}")
            raise RuntimeError(f"Query generation failed: {str(e)}")

    def _process_row(
        self,
        row,
        with_solution,
        mode,
        available_user_traits,
        available_cleanliness_levels,
    ):
        uuid = row.uuid
        problem = row.problem
        if with_solution:
            solution = row.informal_solution
        else:
            solution = None

        if mode == "random":
            user_trait = random.choice([level.value for level in UserLevel])
            cleanliness_level = random.choice(
                [level.value for level in QueryCleanliness]
            )
        else:
            user_trait = random.choice(available_user_traits)
            cleanliness_level = random.choice(available_cleanliness_levels)

        try:
            queries = self.generate_queries(
                problem, solution, user_trait, cleanliness_level
            )
            if queries:
                return {
                    "uuid": uuid,
                    "problem": problem,
                    "solution": solution,
                    "topic_based_queries": queries.topic_based_queries,
                    "technique_based_queries": queries.technique_based_queries,
                    "user_trait": user_trait,
                    "cleanliness_level": cleanliness_level,
                }
        except Exception as e:
            logger.error(f"Error generating queries for uuid {uuid}: {e}")
        return None

    def generate_synthetic_dataset(
        self,
        df: pd.DataFrame,
        with_solution: bool = False,
        mode: str = "random",
        user_traits: list[str] | None = None,
        cleanliness_levels: list[str] | None = None,
        save_to_file: bool = False,
        output_dir: str = "data/synthetic_queries",
        max_workers: int = 50,
    ) -> pd.DataFrame:
        """
        Generates a dataset of synthetic queries in parallel.

        Args:
            df (pd.DataFrame): Input dataframe containing 'uuid', 'problem'.
            mode (str): "random" for random selection or predefined user traits.
            user_traits (List[str], optional): Custom user traits.
            cleanliness_levels (List[str], optional): Custom cleanliness levels.
            save_to_file (bool): Whether to save the output.
            output_dir (str): Directory to save the output.
            max_workers (int): Number of parallel workers (default: 4).

        Returns:
            pd.DataFrame: A new dataframe containing synthetic queries.
        """

        missing_columns = self.REQUIRED_COLUMNS - set(df.columns)
        if missing_columns:
            logger.error(f"Missing required columns: {missing_columns}")
            raise ValueError(f"Missing required columns: {missing_columns}")

        available_user_traits = (
            [level.value for level in UserLevel] if user_traits is None else user_traits
        )
        available_cleanliness_levels = (
            [level.value for level in QueryCleanliness]
            if cleanliness_levels is None
            else cleanliness_levels
        )

        if mode != "random":
            if any(
                trait not in [lvl.value for lvl in UserLevel]
                for trait in available_user_traits
            ):
                raise ValueError(
                    f"Invalid user traits provided: {available_user_traits}"
                )
            if any(
                level not in [lvl.value for lvl in QueryCleanliness]
                for level in available_cleanliness_levels
            ):
                raise ValueError(
                    f"Invalid cleanliness levels provided: {available_cleanliness_levels}"
                )

        synthetic_data = []

        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            results = list(
                tqdm(
                    executor.map(
                        lambda row: self._process_row(
                            row,
                            mode,
                            with_solution,
                            available_user_traits,
                            available_cleanliness_levels,
                        ),
                        df.itertuples(index=False),
                    ),
                    total=len(df),
                    desc="Generating Synthetic Queries",
                )
            )

        synthetic_data = [res for res in results if res is not None]
        synthetic_df = pd.DataFrame(synthetic_data)

        if save_to_file:
            os.makedirs(output_dir, exist_ok=True)

            base_filename = os.path.join(output_dir, "synthetic_queries.csv")

            if os.path.exists(base_filename):
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                new_filename = os.path.join(
                    output_dir, f"synthetic_queries_{timestamp}.csv"
                )
                synthetic_df.to_csv(new_filename, index=False)
                logger.info(f"Saved synthetic queries to {new_filename}")
            else:
                synthetic_df.to_csv(base_filename, index=False)
                logger.info(f"Saved synthetic queries to {base_filename}")

        return synthetic_df


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Generate synthetic queries for math problems."
    )

    parser.add_argument(
        "--limit",
        type=int,
        default=None,
        help="The maximum number of rows to process from the dataframe.",
    )
    parser.add_argument(
        "--with-solution",
        action="store_true",
        help="Whether to include the solution in the synthetic queries.",
    )

    parser.add_argument(
        "--save", action="store_true", help="Flag to save the output to a CSV file."
    )

    DEFAULT_OUTPUT_DIR = Path(__file__).parent.parent / "../data/synthetic_queries"
    parser.add_argument(
        "--output-dir",
        type=Path,
        default=DEFAULT_OUTPUT_DIR,
        help="The directory to save the output file. Defaults to 'data/synthetic_queries'.",
    )

    args = parser.parse_args()

    script_dir = Path(__file__).parent.parent
    df_path = script_dir / ".." / "data" / "final_data.parquet"
    if not os.path.exists(df_path):
        logger.error(f"Error: The file {df_path} does not exist.")
        exit(1)

    # filtering on valid english problem only
    df = pd.read_parquet(df_path)
    logger.info(f"Loaded {len(df)} problems from {df_path}")
    df = df[
        (df["exam"].notna())
        & (df["languages"].astype(str) == "['English']")
        & df["is_valid_no_sorry"]
    ]
    logger.info(f"Filtered to {len(df)} problems")

    if args.limit is not None:
        df = df.head(args.limit)

    synthetic_query_generator = SyntheticQueryGenerator()
    synthetic_df = synthetic_query_generator.generate_synthetic_dataset(
        df, save_to_file=args.save, output_dir=args.output_dir
    )

    logger.info(f"Generated {len(synthetic_df)} synthetic queries.")
