from mathlete.llms.prompt import Prompt


class SyntheticQueriesPromptGenerator:
    def make_synthetic_queries_prompt(
        self,
        problem: str,
        informal_solution: str,
        user_trait: str,
        cleanliness_level: str,
    ) -> Prompt:
        system = (
            """You are an AI tasked with generating realistic user search queries for a mathematical problem search system.
                These queries should be based on the underlying concepts, techniques, or themes present in the given math problem.

                The goal is to create diverse, natural search queries that a user might type into a search engine to find this problem,
                without having access to the problem statement itself. The queries should match the problem’s topic, technique, or mathematical structure,
                such that they would retrieve the original problem from a database.

                Generate the queries based on the following guidelines:

                1. **Topic-based queries**: These are queries related to general topics or areas of math (e.g., differentiation, inequalities, sequences).
                Example queries include: 'Problems about derivatives', 'Geometric sequence problems', 'Inequality problems'.

                2. **Technique-based queries**: These queries focus on specific techniques, methods, or approaches used to solve the problem (e.g., induction, integration, chain rule).
                Example queries include: 'Problems solvable by induction', 'Integration problems using partial fractions', 'Problems involving the chain rule'.

                Please ensure the queries fall under one of these categories: **Topic-based** or **Technique-based**. Do not include queries unrelated to these categories.

                Next, adjust the complexity and phrasing of the queries according to the user's profile:

                - **User Trait**: Vary the complexity based on whether the user is a beginner, undergraduate, graduate, or expert.
                - For beginners (e.g., high school students), use simpler, less formal queries (e.g., 'Easy problems on derivatives').
                - For undergraduates, use more detailed queries (e.g., 'Problems involving the product rule').
                - For graduates or experts, generate technical queries (e.g., 'Problems involving Galois theory').

                - **Cleanliness Level**: Adjust the query's clarity based on the cleanliness level.
                - **Clean**: Clear, well-structured queries.
                - **Messy**: Slightly ambiguous or disorganized queries.
                - **Typos**: Queries with spelling mistakes or incorrect punctuation.

                The generated queries should be phrased naturally, as a user might search for problems that involve the concepts or techniques related to the problem at hand.

                For example, if the problem involves derivatives of quadratic functions, potential queries could include:
                - 'Problems about derivatives of quadratic functions'
                - 'Quadratic differentiation problems'
                - 'Differentiation problems'

                Do **not** reuse the problem statement directly, but ensure the queries would lead the user to find this problem in a search engine.

                Output must be in JSON format with the following structure:
                {{
                "topic_based_queries": ["query1", "query2", "query3"],
                "technique_based_queries": ["query1", "query2", "query3"]
                }}
                """
            ""
        )
        user = f"""Generate a list of synthetic search queries based on the following characteristics:
        - **Problem:** {problem}"""

        if informal_solution:
            user += f"""\n- **Solution:** {informal_solution}"""

        user += f"""\n- **User Trait:** {user_trait}
        - **Cleanliness Level:** {cleanliness_level}"""

        return Prompt(id="synthetic_queries", system=system, user=user)
