import argparse
import asyncio
import pickle
import re
from concurrent.futures import <PERSON>hr<PERSON><PERSON>oolExecutor
from functools import lru_cache
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

from fuzzywuzzy import fuzz
from loguru import logger
from sklearn.feature_extraction.text import ENGLISH_STOP_WORDS

from mathlete.utils.constants import MathProblem, MathProblemCollection
from mathlete.utils.stopwords import CUSTOM_STOPWORDS


class MetadataFuzzyMatcher:
    def __init__(
        self,
        problem_dataset: Union[List[MathProblem], MathProblemCollection, None] = None,
        max_workers: int = 8,
    ):

        self.stop_words = set(ENGLISH_STOP_WORDS)
        self.stop_words.update(CUSTOM_STOPWORDS)

        self.metadata_options = {
            # "question_type": ["proof", "math-word-problem", "mcq"],
            "exam_category": [
                "Team Selection Test",
                "Undergraduate Contests",
                "Junior Olympiads",
                "International Contests",
                "National And Regional Contests",
                "National Olympiads",
            ],
            "problem_type": [
                "Algebra",
                "Inequalities",
                "Number Theory",
                "Geometry",
                "Calculus",
                "Combinatorics",
                "Logic and Puzzles",
            ],
        }

        current_dir = Path(__file__).resolve().parent
        tags_path = (
            current_dir.parent.parent
            / "data_processing"
            / "extract_tags"
            / "allowed_tags.txt"
        )
        with open(tags_path, "r", encoding="utf-8") as f:
            tags = [line.strip() for line in f if line.strip()]
        self.metadata_options["tags"] = tags

        self.match_thresholds = {
            "question_type": 85,
            "exam_category": 85,
            "problem_type": 85,
            "tags": 85,
        }

        self.problem_dataset: List[MathProblem] = problem_dataset or []
        self.max_workers = max_workers
        self._token_cache = {}
        self._executor = ThreadPoolExecutor(max_workers=max_workers)

    def __del__(self):
        self._executor.shutdown(wait=False)

    async def save_dataset(self, filepath: str):
        def _sync_save():
            with open(filepath, "wb") as f:
                pickle.dump(
                    {
                        "problem_dataset": self.problem_dataset,
                        "metadata_options": self.metadata_options,
                        "match_thresholds": self.match_thresholds,
                    },
                    f,
                )

        loop = asyncio.get_event_loop()
        await loop.run_in_executor(self._executor, _sync_save)

    async def load_dataset(
        self, dataset: List[MathProblem] = None, filepath: str = None
    ):
        if dataset:
            self.problem_dataset = dataset
        elif filepath:

            def _sync_load():
                with open(filepath, "rb") as f:
                    return pickle.load(f)

            loop = asyncio.get_event_loop()
            data = await loop.run_in_executor(self._executor, _sync_load)
            self.problem_dataset = data["problem_dataset"]
            self.metadata_options = data["metadata_options"]
            self.match_thresholds = data["match_thresholds"]

    def tokenize_query(self, query: str) -> List[str]:
        # Clean and lowercase
        clean_query = re.sub(r"[^\w\s]", " ", query.lower())
        words = [word for word in clean_query.split() if word not in self.stop_words]

        tokens = words.copy()
        if len(words) >= 2:
            tokens.extend(f"{words[i]} {words[i + 1]}" for i in range(len(words) - 1))

        return tokens

    @lru_cache(maxsize=1024)  # noqa B019
    def _cached_partial_ratio(self, token: str, target: str) -> int:
        return fuzz.partial_ratio(token, target)

    async def _get_match_score(self, tokens: List[str], option_lower: str) -> int:
        best_token_score = 0

        for token in tokens:
            if len(token) < 3:
                continue

            if token in option_lower:
                return 100

            cache_key = f"{token}::{option_lower}"
            if cache_key in self._token_cache:
                score = self._token_cache[cache_key]
            else:
                score = self._cached_partial_ratio(token, option_lower)
                self._token_cache[cache_key] = score

            best_token_score = max(best_token_score, score)

        return best_token_score

    async def match_field(
        self, tokens: List[str], field_name: str, threshold: int
    ) -> Dict[str, List[Dict[str, Any]]]:
        """
        Match tokens against a specific metadata field and return matches with scores.

        Args:
            tokens: List of tokenized query terms
            field_name: Name of the metadata field to match against
            threshold: Minimum score to consider a match

        Returns:
            Dictionary with field name as key and list of match objects as value
        """
        if not tokens or field_name not in self.metadata_options:
            return {}

        options = self.metadata_options[field_name]
        scores = await asyncio.gather(
            *[self._get_match_score(tokens, option.lower()) for option in options]
        )

        matches = [
            {"value": option, "score": score}
            for option, score in zip(options, scores)
            if score >= threshold
        ]

        matches.sort(key=lambda x: x["score"], reverse=True)

        return {field_name: matches} if matches else {}

    async def match_fields(self, tokens: List[str]) -> Dict[str, List[Dict[str, Any]]]:
        """
        Match tokens against all metadata fields and return matches with scores.

        Args:
            tokens: List of tokenized query terms

        Returns:
            Dictionary mapping field names to lists of match objects
        """
        if not tokens:
            return {}

        field_results = await asyncio.gather(
            *[
                self.match_field(tokens, field, self.match_thresholds[field])
                for field in self.metadata_options.keys()
            ]
        )

        merged_results = {}
        for result in field_results:
            merged_results.update(result)

        return merged_results

    async def find_best_match(
        self, value: str, options: List[str], threshold: int = 85
    ) -> Optional[str]:
        best_match = None
        best_score = 0

        async def get_score(option: str):
            return await asyncio.to_thread(self._cached_partial_ratio, value, option)

        tasks = [get_score(option) for option in options]
        scores = await asyncio.gather(*tasks)

        for option, score in zip(options, scores):
            if score > best_score and score >= threshold:
                best_score = score
                best_match = option

        return best_match

    async def normalize_filter_values(self, field: str, values: List[str]) -> List[str]:
        normalized_values = []

        if field not in self.metadata_options or not self.metadata_options[field]:
            logger.error(f"Field {field} not found in metadata options")
            return [str(v).strip() for v in values if v]

        threshold = self.match_thresholds.get(field, 85)
        options = self.metadata_options[field]

        async def process_value(v: str):
            if not v:
                return None
            v_str = str(v).strip()
            matched_value = await self.find_best_match(v_str, options, threshold)
            if not matched_value:
                logger.warning(f"No match found for {field}={v_str}")
            return matched_value

        tasks = [process_value(v) for v in values]
        matched_values = await asyncio.gather(*tasks)

        for matched_value in matched_values:
            if matched_value:
                normalized_values.append(matched_value)

        return normalized_values

    def filter_problems_by_metadata(
        self, metadata_matches: Dict[str, List[Dict[str, Any]]]
    ) -> List[MathProblem]:
        """
        Filter problems by metadata matches.

        Args:
            metadata_matches: Dictionary mapping field names to lists of match objects

        Returns:
            List of matching MathProblem objects
        """
        if not metadata_matches or not self.problem_dataset:
            return self.problem_dataset

        return [
            problem
            for problem in self.problem_dataset
            if all(
                (
                    any(
                        tag == match["value"]
                        for match in field_matches
                        for tag in (problem.metadata.tags or [])
                    )
                    if field == "tags"
                    else (
                        any(
                            problem.exam_info.exam_category == match["value"]
                            for match in field_matches
                        )
                        if field == "exam_category"
                        else (
                            any(
                                getattr(problem.metadata, field, None) == match["value"]
                                for match in field_matches
                            )
                            if problem.metadata
                            else False
                        )
                    )
                )
                for field, field_matches in metadata_matches.items()
            )
        ]

    async def match_query(self, query: str) -> Dict[str, Any]:
        """
        Match a query against metadata fields and return matching problems.

        Args:
            query: The search query string

        Returns:
            Dict containing metadata matches with scores and matching problems
        """
        tokens = self.tokenize_query(query)
        if not tokens:
            return {"metadata_matches": {}, "problem_matches": []}

        metadata_matches = await self.match_fields(tokens)
        problem_matches = [
            problem.to_dict()
            for problem in self.filter_problems_by_metadata(metadata_matches)
        ]

        return {
            "metadata_matches": metadata_matches,
            "problem_matches": problem_matches,
        }


def main():

    parser = argparse.ArgumentParser(
        description="Metadata Fuzzy Matcher for Math Problems"
    )
    parser.add_argument(
        "--query", type=str, required=True, help="Query string to search for"
    )
    parser.add_argument(
        "--save", type=str, help="Path to save the dataset (optional)", default=None
    )
    parser.add_argument(
        "--load",
        type=str,
        help="Path to load the dataset from (optional)",
        default=None,
    )
    parser.add_argument(
        "--max_workers",
        type=int,
        default=8,
        help="Max number of worker threads for processing",
    )
    args = parser.parse_args()

    async def run():
        matcher = MetadataFuzzyMatcher(max_workers=args.max_workers)

        if args.load:
            await matcher.load_dataset(filepath=args.load)

        if args.query:
            result = await matcher.match_query(args.query)
            logger.info(f"Metadata Matches:{result['metadata_matches']}")
            logger.info(f"\nProblem Matches:{result['problem_matches']}")

        if args.save:
            await matcher.save_dataset(args.save)

    asyncio.run(run())


if __name__ == "__main__":
    main()
