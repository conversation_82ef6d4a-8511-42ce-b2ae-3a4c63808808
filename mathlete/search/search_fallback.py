from typing import Any, Dict

# Define authorized values and their variations
PROBLEM_TYPES = {
    "Algebra": ["algebra", "algebraic"],
    "Inequalities": ["inequalities", "inequality"],
    "Number Theory": ["number theory", "number theoretic", "numerical theory"],
    "Geometry": ["geometry", "geometric", "geometrical"],
    "Calculus": ["calculus"],
    "Combinatorics": ["combinatorics", "combinatorial", "counting"],
    "Logic and Puzzles": ["logic", "puzzle", "logical puzzle", "brain teaser"],
    "Other": ["other"],
}

QUESTION_TYPES = {
    "MCQ": ["mcq", "multiple choice", "multiple choice question", "multiple option"],
    "math-word-problem": [
        "word problem",
        "story problem",
        "textual problem",
        "application problem",
    ],
    "proof": ["proof", "prove that", "demonstrate that", "proof question"],
}

EXAM_CATEGORIES = {
    "International Contests": ["international", "international contests"],
    "Junior Olympiads": ["junior", "junior olympiad"],
    "National And Regional Contests": [
        "national",
        "regional",
        "national contest",
        "regional contest",
    ],
    "Team Selection Test": ["team selection", "selection test"],
    "Undergraduate Contests": ["undergraduate", "college", "university"],
}


def normalize_text(text: str) -> str:
    # TODO deal with math in the query
    return text.lower().strip()


def process_query(query: str) -> Dict[str, Any]:
    filters = {}
    text_search = normalize_text(query)

    # Match problem type
    for official_type, variations in PROBLEM_TYPES.items():
        if any(variation in text_search for variation in variations):
            filters["problem_type"] = official_type
            break

    # Match question type
    for official_type, variations in QUESTION_TYPES.items():
        if any(variation in text_search for variation in variations):
            filters["question_type"] = official_type
            break

    # Match exam category
    for official_category, variations in EXAM_CATEGORIES.items():
        if any(variation in text_search for variation in variations):
            filters["exam_category"] = official_category
            break

    # Remove matched filter terms from text_search
    used_terms = []
    for variations in PROBLEM_TYPES.values():
        used_terms.extend(variations)
    for variations in QUESTION_TYPES.values():
        used_terms.extend(variations)
    for variations in EXAM_CATEGORIES.values():
        used_terms.extend(variations)

    # Create text search by removing matched terms
    remaining_terms = []
    for word in text_search.split():
        if not any(term in word for term in used_terms):
            remaining_terms.append(word)

    text_search = " ".join(remaining_terms)

    return {"filters": filters, "text_search": text_search}
