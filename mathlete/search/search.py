import argparse
import ast
from pathlib import Path
from typing import Any, Dict

import numpy as np
import pandas as pd
from loguru import logger

from mathlete.llms import llms
from mathlete.search.filtering import apply_filters
from mathlete.search.retrievers._hybrid import HybridRetriever
from mathlete.search.search_fallback import process_query
from mathlete.search.search_models import FinalQuery, QueryDecomposition
from mathlete.search.search_prompts import QueryDecompositionPromptGenerator


class MathProblemSearch:
    def __init__(
        self,
        cache_dir: str = ".cache",
        use_stemming: bool = False,
        model_name: str = "sentence-transformers/msmarco-distilbert-cos-v5",
        doc_instruction_template: str = None,
        query_instruction_template: str = None,
        llm_model: str = "gpt-4o-2024-08-06",
    ):
        """
        Initialize search with documents and retrievers.

        Args:
            documents: List of strings representing problem data
            cache_dir: Directory for caching retriever data
            use_stemming: Whether to use stemming for BM25
            model_name: Name of the sentence-transformer model
            doc_instruction_template: Template string for formatting documents with instructions
        """

        self.doc_instruction_template = (
            doc_instruction_template
            or """
        Instruct: Create a representation of the math problem that captures its key mathematical concepts, relationships, and equivalence properties. This representation should facilitate the retrieval of problems that are mathematically equivalent.

        Problem Statement: {problem}
        Solution: {solution}
        Metadata: {metadata}
        """
        )

        self.query_instruction_template = (
            query_instruction_template
            or """
        Instruct: Generate a representation of the math search query that emphasizes the core mathematical equivalence criteria. This representation will be used to retrieve problems and solutions that match the underlying mathematical structure of the query.

        Query: {query}
        """
        )

        self.retriever = HybridRetriever(
            cache_dir=cache_dir, use_stemming=use_stemming, model_name=model_name
        )

        self.retriever_fitted = False
        self.df = None

        self.llm_model = llm_model

    def format_metadata(self, row: pd.Series) -> str:
        metadata_fields = {
            "problem_type": "Problem Type",
            "question_type": "Question Type",
            "exam_category": "Exam Category",
            "mathematical_techniques": "Mathematical Techniques",
            "mathematical_results": "Mathematical Results",
        }
        metadata_lines = []
        for field, label in metadata_fields.items():
            if field in row:
                value = row[field]
                if (
                    isinstance(value, str)
                    and value.startswith("[")
                    and value.endswith("]")
                ):
                    try:
                        value_list = ast.literal_eval(value)
                        if isinstance(value_list, list) and value_list:
                            metadata_lines.append(f"{label}: {', '.join(value_list)}")
                    except (ValueError, SyntaxError):
                        pass
                elif isinstance(value, (list, np.ndarray, pd.Series)):
                    if any(not pd.isna(v) and v != "" for v in value):
                        metadata_lines.append(f"{label}: {', '.join(map(str, value))}")
                elif not pd.isna(value) and value != "":
                    metadata_lines.append(f"{label}: {value}")
        return "\n".join(metadata_lines)

    def format_document(self, row: pd.Series) -> str:
        metadata = self.format_metadata(row)

        formatted_doc = self.doc_instruction_template.format(
            metadata=metadata, problem=row["problem"], solution=row["informal_solution"]
        )

        return "\n".join(line for line in formatted_doc.split("\n") if line.strip())

    def format_documents(self, df: pd.DataFrame) -> list[str]:
        return [self.format_document(row) for _, row in df.iterrows()]

    def format_query(self, query: str) -> str:
        return self.query_instruction_template.format(query=query)

    def fit_retriever(self, df: pd.DataFrame):
        """
        Fit the retriever with a dataframe directly, using formatted documents.

        Args:
            df: DataFrame containing at minimum 'problem' and 'informal_solution' columns
        """
        if not {"problem", "informal_solution"}.issubset(df.columns):
            raise ValueError(
                "DataFrame must contain 'problem' and 'informal_solution' columns."
            )

        self.df = df
        formatted_documents = self.format_documents(df)

        if not self.retriever_fitted:
            self.retriever.fit(formatted_documents)
            self.retriever_fitted = True

    def set_doc_instruction_template(self, template: str):
        """
        Update the instruction template and refit the retriever if necessary.

        Args:
            template: New instruction template string
        """
        self.doc_instruction_template = template
        if self.df is not None and self.retriever_fitted:
            self.retriever_fitted = False
            self.fit_retriever(self.df)

    def decompose_query(self, query: str) -> FinalQuery:
        """
        Decompose a natural language query into filters and text search components.

        Args:ss
            query: User's natural language query

        Returns:
            QueryDecomposition object containing filters and text search components
        """
        prompt_generator = QueryDecompositionPromptGenerator()
        prompt = prompt_generator.make_prompt_query_decomposition(query)
        try:
            query_decomposition = llms.execute_prompt(
                prompt=prompt, output_class=QueryDecomposition, model=self.llm_model
            )
            self.query_decomposition = FinalQuery(
                text_search=query, decomposition=query_decomposition
            )
            logger.info(
                f"Original query: {query} and decomposed query is {query_decomposition}"
            )
            return self.query_decomposition
        except Exception as e:
            logger.warning(f"Query decomposition failed: {e}. Using fallback.")
            # Fallback: Basic keyword matching
            query = process_query(query)
            filters = query["filters"]
            text_search = query["text_search"]

            self.query_decomposition = FinalQuery(
                text_search=text_search,
                decomposition=QueryDecomposition(filters=filters, valid_query=True),
            )
            logger.info(f"Original query: {query} and decomposed query is: {filters}")
            return self.query_decomposition

    def search(
        self,
        query: str,
        top_k: int = 10,
        use_hybrid: bool = True,
        enable_filtering: bool = True,
    ) -> Dict[str, Any]:
        if not self.retriever_fitted:
            raise RuntimeError(
                "Retriever has not been fitted. Call fit_retriever() first."
            )

        decomposed = self.decompose_query(query)
        filters = decomposed.decomposition.filters
        valid_query = decomposed.decomposition.valid_query

        if not valid_query:
            logger.debug("Query is not valid")
            return {"results": [], "filters": [], "enable_filtering": enable_filtering}

        query_text = self.format_query(query)
        logger.debug(f"Query text: {query_text}")
        results = self.retriever.retrieve(
            query_text, top_k=top_k, use_hybrid=use_hybrid
        )

        if not results and not filters:
            logger.debug("Found no filter or text search to be applied")
            return {"results": [], "filters": [], "enable_filtering": enable_filtering}

        retrieved_df = self.df.iloc[[r.doc_id for r in results]] if results else self.df

        if enable_filtering and filters:
            logger.info(f"Applying filters {filters}")
            filtered_df = apply_filters(filters=filters, df=retrieved_df)
            if filtered_df.empty:
                logger.debug(
                    "Filters removed too much data, falling back to text search results"
                )
                filtered_df = retrieved_df
        else:
            logger.info("No filters")
            filtered_df = retrieved_df

        return {
            "results": filtered_df.to_dict(orient="records")[:top_k],
            "filters": filters,
            "enable_filtering": enable_filtering,
        }


def main():
    script_dir = Path(__file__).resolve().parent
    data_file_path = script_dir.parent / "data" / "final_data.parquet"

    parser = argparse.ArgumentParser(description="Search math problems")
    parser.add_argument("--query", type=str, help="Search query for math problems")
    parser.add_argument(
        "--top_k", type=int, default=5, help="Number of top results to retrieve"
    )
    parser.add_argument(
        "--dataset_path",
        type=str,
        default=data_file_path,
        help="Path to the dataset",
    )
    parser.add_argument(
        "--test", action="store_true", help="Use only the first 10 rows of the dataset"
    )
    parser.add_argument(
        "--enable_filtering", action="store_true", help="Enable filtering of results"
    )

    args = parser.parse_args()

    df = pd.read_parquet(args.dataset_path)
    if args.test:
        logger.info("Running in test mode")
        df = df.head(10)

    searcher = MathProblemSearch()
    logger.info("Fitting Retrievers")
    searcher.fit_retriever(df)
    logger.info("Retrievers are fitted")

    search_results = searcher.search(
        args.query, top_k=args.top_k, enable_filtering=args.enable_filtering
    )

    results = search_results["results"]
    filters = search_results["filters"]

    logger.info(f"Applied Filters: {filters}")

    for idx, result in enumerate(results):
        logger.info(f"{idx}. {result}")


if __name__ == "__main__":
    main()
