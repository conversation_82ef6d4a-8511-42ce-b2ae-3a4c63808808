from typing import List

import numpy as np
from hdbscan import HDBSCAN
from loguru import logger
from sklearn.feature_extraction.text import ENGLISH_STOP_WORDS as SKLEARN_STOPWORDS
from sklearn.feature_extraction.text import TfidfVectorizer

from mathlete.utils.problem_manager import Problem<PERSON>anager
from mathlete.utils.stopwords import CUSTOM_STOPWORDS


class MathClusterer:
    def __init__(self):
        self.clusters = {}
        self.problem_to_cluster = {}
        self.cluster_domains = {}

    def create_clusters(
        self, problem_manager: ProblemManager, min_cluster: int = 20
    ) -> List[List[str]]:
        logger.info("Starting two-stage problem clustering")
        problem_ids = problem_manager.get_problem_ids()
        valid_problem_ids = []
        problem_types = []

        for pid in problem_ids:
            embedding = problem_manager.get_problem_embedding(pid)
            problem = problem_manager.get_problem(pid)

            if embedding is not None and problem is not None:
                valid_problem_ids.append(pid)

                metadata = problem.metadata
                problem_type = metadata.problem_type or "Unknown"
                problem_types.append(problem_type)

        if not valid_problem_ids:
            logger.warning("No valid embeddings found for clustering")
            return []

        # Stage 1: Group by problem type
        domain_clusters = {}
        for pid, ptype in zip(valid_problem_ids, problem_types):
            if ptype not in domain_clusters:
                domain_clusters[ptype] = []
            domain_clusters[ptype].append(pid)

        # Stage 2: Apply hierarchical clustering within each domain
        self.clusters = {}
        self.problem_to_cluster = {}
        next_cluster_id = 0

        for domain, domain_pids in domain_clusters.items():
            if not domain_pids:
                continue

            domain_embeddings = []
            domain_valid_pids = []
            for pid in domain_pids:
                embedding = problem_manager.get_problem_embedding(pid)
                if embedding is not None:
                    domain_embeddings.append(embedding)
                    domain_valid_pids.append(pid)

            if not domain_embeddings:
                continue

            logger.info(
                f"There is {len(domain_valid_pids)} valid domain problems for {domain}"
            )

            if len(domain_valid_pids) < min_cluster:
                logger.info(
                    f"Domain {domain} has less than {min_cluster} problems. Assigning to one cluster."
                )
                cluster_id = next_cluster_id
                next_cluster_id += 1
                self.clusters[cluster_id] = domain_valid_pids
                for pid in domain_valid_pids:
                    self.problem_to_cluster[pid] = cluster_id
                    self.cluster_domains[cluster_id] = domain
                continue

            domain_embeddings = np.vstack(domain_embeddings)
            clusterer = HDBSCAN(min_cluster_size=5)
            cluster_labels = clusterer.fit_predict(domain_embeddings)

            labels = clusterer.labels_
            local_n_clusters = len(set(labels)) - (1 if -1 in labels else 0)
            logger.info(f"There is {local_n_clusters} clusters for domain {domain}")

            label_to_global_cluster_id = {}
            for pid, label in zip(domain_valid_pids, cluster_labels):
                if label == -1:
                    if -1 not in self.clusters:
                        self.clusters[label] = []
                    self.clusters[label].append(pid)
                    self.problem_to_cluster[pid] = label
                    continue

                if label not in label_to_global_cluster_id:
                    label_to_global_cluster_id[label] = next_cluster_id
                    next_cluster_id += 1

                cluster_id = label_to_global_cluster_id[label]

                if cluster_id not in self.clusters:
                    self.clusters[cluster_id] = []

                self.clusters[cluster_id].append(pid)
                self.problem_to_cluster[pid] = cluster_id
                self.cluster_domains[cluster_id] = domain

        cluster_labels = np.array([-1] * len(valid_problem_ids))
        for i, pid in enumerate(valid_problem_ids):
            if pid in self.problem_to_cluster:
                cluster_labels[i] = self.problem_to_cluster[pid]

        logger.info(
            f"Clustered {len(valid_problem_ids)} problems into {len(self.clusters)} clusters across {len(domain_clusters)} domains"
        )
        return cluster_labels

    def post_process_noise_points(
        self, problem_manager: ProblemManager, similarity_matrix=None, top_k: int = 5
    ):
        noise_points = self.clusters.pop(-1, [])
        if not noise_points:
            logger.info("No noise points to post-process")
            return

        logger.info(f"Post-processing {len(noise_points)} noise points")

        if similarity_matrix is None:
            raise ValueError(
                "Similarity matrix is required for post-processing noise points"
            )

        for noise_pid in noise_points:
            noise_idx = problem_manager.get_problem_index(noise_pid)
            if noise_idx is None:
                continue

            all_pids = problem_manager.get_problem_ids()

            similarities = [
                (pid, similarity_matrix[noise_idx][i])
                for i, pid in enumerate(all_pids)
                if pid != noise_pid
            ]
            similarities.sort(key=lambda x: x[1], reverse=True)
            top_k_similarities = similarities[:top_k]

            # Count cluster occurrences among neighbors
            cluster_counts = {}
            for neighbor_pid, _ in top_k_similarities:
                neighbor_cluster = self.problem_to_cluster.get(neighbor_pid)
                if neighbor_cluster is not None and neighbor_cluster != -1:
                    if neighbor_cluster not in cluster_counts:
                        cluster_counts[neighbor_cluster] = 0
                    cluster_counts[neighbor_cluster] += 1

            if cluster_counts:
                best_cluster = max(cluster_counts.items(), key=lambda x: x[1])[0]

                if best_cluster not in self.clusters:
                    self.clusters[best_cluster] = []

                self.clusters[best_cluster].append(noise_pid)
                self.problem_to_cluster[noise_pid] = best_cluster
            else:
                # If all neighbors are noise, find the first non-noise problem
                for neighbor_pid, _ in similarities:
                    neighbor_cluster = self.problem_to_cluster.get(neighbor_pid)
                    if neighbor_cluster is not None and neighbor_cluster != -1:
                        if neighbor_cluster not in self.clusters:
                            self.clusters[neighbor_cluster] = []

                        self.clusters[neighbor_cluster].append(noise_pid)
                        self.problem_to_cluster[noise_pid] = neighbor_cluster
                        break

        logger.info(f"Assigned {len(noise_points)} noise points to existing clusters")

    def _get_problem_text_for_topic(self, problem):
        text_parts = []

        metadata = problem.metadata
        for field in ["mathematical_techniques", "mathematical_results"]:
            values = getattr(metadata, field, None)
            if isinstance(values, list):
                text_parts.extend(values)
            elif values:
                text_parts.append(values)

        title = problem.title
        text_parts.append(title)

        return " ".join(text_parts)

    def generate_cluster_labels(self, problem_manager, top_n_terms=5):
        """Generate descriptive labels for each cluster based on TF-IDF analysis of problem texts."""
        if not self.clusters:
            return {}

        cluster_docs = {}
        all_docs = []

        for cluster_id, problem_ids in self.clusters.items():
            if cluster_id == -1 or not problem_ids:
                continue

            cluster_docs[cluster_id] = []
            for pid in problem_ids:
                problem = problem_manager.get_problem(pid)
                if not problem:
                    continue

                text = self._get_problem_text_for_topic(problem)
                if text:
                    all_docs.append(text)
                    cluster_docs[cluster_id].append(text)

        if not all_docs:
            return {k: f"Cluster {k}" for k in self.clusters.keys() if k != -1}

        try:
            STOPWORDS = list(SKLEARN_STOPWORDS.union(CUSTOM_STOPWORDS))
            vectorizer = TfidfVectorizer(
                min_df=5, token_pattern=r"\b\w{4,}\b", stop_words=STOPWORDS
            )

            vectorizer.fit(all_docs)
            feature_names = vectorizer.get_feature_names_out()

            cluster_labels = {}
            for cluster_id, docs in cluster_docs.items():
                if not docs:
                    cluster_labels[cluster_id] = f"Cluster {cluster_id}"
                    continue

                tfidf_matrix = vectorizer.transform(docs)
                avg_tfidf = np.asarray(tfidf_matrix.mean(axis=0)).flatten()

                top_indices = avg_tfidf.argsort()[::-1][:top_n_terms]
                top_terms = [feature_names[i] for i in top_indices]

                domain = self.cluster_domains.get(cluster_id, "")
                if domain and domain != "Unknown":
                    label = f"{domain}: {', '.join(top_terms)}"
                else:
                    label = f"Cluster {cluster_id}: {', '.join(top_terms)}"

                logger.info(f"Generated label for cluster {cluster_id}: {label}")
                cluster_labels[cluster_id] = label

            return cluster_labels

        except Exception as e:
            logger.error(f"Error generating cluster labels: {str(e)}", exc_info=True)
            return {k: f"Cluster {k}" for k in self.clusters.keys() if k != -1}

    def get_cluster_for_problem(self, problem_id):
        return self.problem_to_cluster.get(problem_id, -1)

    def get_cluster_label(self, cluster_id, problem_manager):
        if not hasattr(self, "cluster_labels") or not self.cluster_labels:
            if problem_manager:
                self.cluster_labels = self.generate_cluster_labels(problem_manager)
            else:
                return f"Cluster {cluster_id}"

        return self.cluster_labels.get(cluster_id, f"Cluster {cluster_id}")

    def get_all_cluster_labels(self):
        result = []
        for pid in self.problem_to_cluster:
            result.append(self.problem_to_cluster[pid])
        return np.array(result)
