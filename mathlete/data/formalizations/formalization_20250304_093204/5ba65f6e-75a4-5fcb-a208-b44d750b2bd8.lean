/- Start of correct informal solution for reference
1. Consider a polynomial \( P(z) = a_n z^n + a_{n-1} z^{n-1} + \ldots + a_1 z + a_0 \) with integer coefficients \( a_i \).
2. Let \( x \) be an integer root of the polynomial, i.e., \( P(x) = 0 \).
3. We can write the polynomial equation as:
   \[
   a_n x^n + a_{n-1} x^{n-1} + \ldots + a_1 x + a_0 = 0
   \]
4. Taking the absolute value of both sides, we get:
   \[
   |a_n x^n + a_{n-1} x^{n-1} + \ldots + a_1 x + a_0| = 0
   \]
5. By the triangle inequality, we have:
   \[
   |a_n x^n| \leq |a_{n-1} x^{n-1}| + |a_{n-2} x^{n-2}| + \ldots + |a_1 x| + |a_0|
   \]
6. Since \( x \) is an integer, we can express the inequality as:
   \[
   |a_n| |x|^n \leq |a_{n-1}| |x|^{n-1} + |a_{n-2}| |x|^{n-2} + \ldots + |a_1| |x| + |a_0|
   \]
7. Dividing both sides by \( |x|^{n-1} \) (assuming \( x \neq 0 \)), we get:
   \[
   |a_n| |x| \leq |a_{n-1}| + \frac{|a_{n-2}|}{|x|} + \ldots + \frac{|a_1|}{|x|^{n-2}} + \frac{|a_0|}{|x|^{n-1}}
   \]
8. Since \( |x| \geq 1 \), each term \( \frac{|a_i|}{|x|^k} \) (for \( k \geq 1 \)) is less than or equal to \( |a_i| \). Therefore:
   \[
   |a_n| |x| \leq |a_{n-1}| + |a_{n-2}| + \ldots + |a_1| + |a_0|
   \]
9. If \( |x| \) were greater than the maximum of the moduli of the coefficients, say \( M = \max(|a_0|, |a_1|, \ldots, |a_n|) \), then:
   \[
   |a_n| |x| > M \cdot n
   \]
   which contradicts the inequality derived in step 8.
10. Therefore, \( |x| \) cannot exceed the maximum of the moduli of the coefficients.

\(\blacksquare\)
-/ End of correct informal solution for reference

/- Start of correct Lean4 solution for reference
import Mathlib

/- Prove that the modulus of an integer root of a polynomial with integer coefficients cannot exceed the maximum of the moduli of the coefficients. -/
theorem algebra_62534 {P : Polynomial ℤ} (hP : P ≠ 0) (r : ℤ) (hr : P.eval r = 0) :
    |r| ≤ sSup {(|coeff| : ℝ) | coeff ∈ P.coeffs} := by sorry
-/ End of correct Lean4 solution for reference

-- Automatically generated Lean4 formalization
import Mathlib.Data.Polynomial
import Mathlib.Analysis.SpecialFunctions.Basic

import Mathlib.Data.Polynomial
import Mathlib.Analysis.SpecialFunctions.Basic

open Polynomial

-- User's Attempt to Formalize

-- Given a polynomial P with integer coefficients
-- Assume r is a root of P
example {P : Polynomial ℤ} (hP : P ≠ 0) (r : ℤ) (hr : P.eval r = 0) :
  |r| ≤ sSup {(|coeff| : ℝ) | coeff ∈ P.coeffs} :=
begin
  -- Consider a polynomial P(z) = a_n z^n + a_{n-1} z^{n-1} + ... + a_1 z + a_0
  -- Let x be an integer root of the polynomial, i.e., P(x) = 0
  -- We can write the polynomial equation as:
  -- a_n x^n + a_{n-1} x^{n-1} + ... + a_1 x + a_0 = 0
  -- Take the absolute value of both sides:
  -- |a_n x^n + a_{n-1} x^{n-1} + ... + a_1 x + a_0| = 0
  -- By the triangle inequality:
  -- |a_n x^n| ≤ |a_{n-1} x^{n-1}| + |a_{n-2} x^{n-2}| + ... + |a_1 x| + |a_0|
  sorry  -- GAP: User did not complete the proof-certifying steps.
end
