from typing import List

from loguru import logger
from sklearn.feature_extraction.text import ENGLISH_STOP_WORDS as SKLEARN_STOPWORDS
from sklearn.feature_extraction.text import TfidfVectorizer

from mathlete.utils.constants import ExamInfo, MathProblem, Metadata
from mathlete.utils.stopwords import CUSTOM_STOPWORDS
from mathlete.vectors.base import BaseEmbeddingGenerator


class TfidfEmbeddingGenerator(BaseEmbeddingGenerator):
    def __init__(self, stopwords: List[str] = None):
        self.template = (
            "{title}\n"
            "{problem_type}\n"
            "{exam_category}\n"
            "{mathematical_techniques}\n"
            "{mathematical_results}"
        )
        default_stopwords = list(SKLEARN_STOPWORDS.union(CUSTOM_STOPWORDS))
        self.stopwords = stopwords if stopwords else default_stopwords

    def format(self, problem: MathProblem) -> str:
        metadata = problem.metadata or Metadata()
        exam_info = problem.exam_info or ExamInfo()

        return self.template.format(
            title=problem.title or "",
            problem_type=metadata.problem_type or "",
            exam_category=" ".join(exam_info.exam_category or []),
            mathematical_techniques=" ".join(metadata.mathematical_techniques or []),
            mathematical_results=" ".join(metadata.mathematical_results or []),
        )

    def generate_embeddings(self, problems: List[MathProblem]) -> List[MathProblem]:
        logger.info(f"Generating TF-IDF embeddings for {len(problems)} problems")
        corpus = [self.format(p) for p in problems]
        vectorizer = TfidfVectorizer(ngram_range=(1, 2), stop_words=self.stopwords)
        tfidf_matrix = vectorizer.fit_transform(corpus).toarray()
        for problem, embedding in zip(problems, tfidf_matrix):
            problem.embedding = embedding
        return problems
