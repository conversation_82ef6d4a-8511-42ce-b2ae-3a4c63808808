from typing import List, Union

from loguru import logger
from sentence_transformers import SentenceTransformer

from mathlete.utils.cleaning import preprocess_math_text
from mathlete.utils.constants import (
    ExamInfo,
    MathProblem,
    MathProblemCollection,
    Metadata,
)


class EmbeddingGenerator:
    """Handles generation of problem embeddings."""

    def __init__(self, embedding_model_name="all-MiniLM-L6-v2"):
        self.embedding_model_name = embedding_model_name
        self.embedding_model = SentenceTransformer(self.embedding_model_name)
        self.embedding_template = self._create_embedding_template()

    def _create_embedding_template(self):
        return (
            "Title: {title}\n"
            "Problem: {problem}\n"
            "Domain: {problem_type}\n"
            "Exam: {exam}"
        )

    def format_for_embedding(self, problem: MathProblem) -> str:
        metadata = problem.metadata or Metadata()
        exam_info = problem.exam_info or ExamInfo()

        return self.embedding_template.format(
            problem=preprocess_math_text(problem.informal_statement),
            problem_type=metadata.problem_type or "",
            exam=exam_info.exam or "",
            title=problem.title or "",
        )

    def generate_embeddings(
        self, problems: Union[List[MathProblem], MathProblemCollection]
    ) -> List[MathProblem]:
        problems = list(problems)
        logger.info(
            f"Generate {self.embedding_model_name} embeddings for {len(problems)} problems"
        )
        formatted = [self.format_for_embedding(p) for p in problems]
        logger.debug(f"{formatted[:3]}")
        embeddings = self.embedding_model.encode(formatted, show_progress_bar=True)

        for problem, embedding in zip(problems, embeddings):
            problem.embedding = embedding

        return problems
