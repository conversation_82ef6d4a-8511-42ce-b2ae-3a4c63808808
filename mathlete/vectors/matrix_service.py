import os

import numpy as np
from loguru import logger


class MatrixService:
    def __init__(self, matrix_path: str = "similarity_matrix.npy"):
        """
        A service to manage similarity matrix persistence and loading.
        """
        self.matrix_path = matrix_path

    def save_similarity_matrix(self, similarity_matrix: np.ndarray) -> None:
        os.makedirs(os.path.dirname(os.path.abspath(self.matrix_path)), exist_ok=True)
        np.save(self.matrix_path, similarity_matrix)
        logger.info(f"Saved similarity matrix to {self.matrix_path}")

    def load_similarity_matrix(self) -> np.ndarray:
        if not os.path.exists(self.matrix_path):
            raise FileNotFoundError(
                f"Similarity matrix file not found at {self.matrix_path}"
            )

        matrix = np.load(self.matrix_path)
        logger.info(f"Loaded similarity matrix from {self.matrix_path}")
        return matrix

    def matrix_exists(self) -> bool:
        return os.path.exists(self.matrix_path)
