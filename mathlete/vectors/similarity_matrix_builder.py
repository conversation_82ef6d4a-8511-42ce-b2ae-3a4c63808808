from typing import List

import numpy as np
from loguru import logger
from sentence_transformers import util
from sklearn.metrics import pairwise_distances
from sklearn.preprocessing import MultiLabelBinarizer, OneHotEncoder

from mathlete.utils.constants import MathProblem
from mathlete.utils.problem_manager import ProblemManager
from mathlete.vectors.embedding_generator import EmbeddingGenerator


class SimilarityMatrixBuilder:
    def __init__(
        self,
        problem_manager: ProblemManager,
        embedding_generator: EmbeddingGenerator = None,
        alpha: float = 0.7,
        use_hybrid: bool = True,
    ) -> None:
        self.problem_manager = problem_manager
        self.embedding_generator = embedding_generator or EmbeddingGenerator()
        self.problem_ids = self.problem_manager.get_problem_ids()
        self.alpha = alpha
        self.use_hybrid = use_hybrid
        self._similarity_matrix = None

    def compute_metadata_similarity(self, problems: List[MathProblem]) -> np.ndarray:
        """Compute similarity matrix based on problem metadata."""
        if not problems:
            raise ValueError("Problems list cannot be empty")

        logger.info(
            f"Computing metadata similarity matrix for {len(problems)} problems"
        )
        n = len(problems)

        problem_types = []
        exams = []
        exam_categories = []
        mathematical_techniques = []
        mathematical_results = []

        for problem in problems:
            problem_types.append(
                getattr(problem.metadata, "problem_type", "") or ""
                if problem.metadata
                else ""
            )
            exams.append(
                getattr(problem.exam_info, "exam", "") or ""
                if problem.exam_info
                else ""
            )

            categories = (
                getattr(problem.exam_info, "exam_category", [])
                if problem.exam_info
                else []
            )
            exam_categories.append(
                categories
                if isinstance(categories, list)
                else [categories] if categories else []
            )

            techniques = (
                getattr(problem.metadata, "mathematical_techniques", [])
                if problem.metadata
                else []
            )
            mathematical_techniques.append(
                techniques
                if isinstance(techniques, list)
                else [techniques] if techniques else []
            )

            results = (
                getattr(problem.metadata, "mathematical_results", [])
                if problem.metadata
                else []
            )
            mathematical_results.append(
                results if isinstance(results, list) else [results] if results else []
            )

        logger.debug("Computing similarity for problem types and exams")

        # Problem type similarity (exact match)
        problem_types_array = np.array(problem_types).reshape(-1, 1)
        unique_types = set(problem_types)
        if len(unique_types) > 1 and any(problem_types):  # Check for non-empty values
            try:
                type_encoder = OneHotEncoder(
                    sparse_output=True, handle_unknown="ignore"
                )
                type_vectors = type_encoder.fit_transform(problem_types_array)
                type_similarity = (type_vectors @ type_vectors.T).toarray()
            except Exception as e:
                logger.warning(f"Error computing problem type similarity: {e}")
                type_similarity = np.zeros((n, n))
        else:
            logger.warning("Warning; all problem types are the same (or empty)")
            type_similarity = (
                np.ones((n, n))
                if len(unique_types) == 1 and list(unique_types)[0]
                else np.zeros((n, n))
            )

        # Exam similarity (exact match)
        exams_array = np.array(exams).reshape(-1, 1)
        unique_exams = set(exams)
        if len(unique_exams) > 1 and any(exams):  # Check for non-empty values
            try:
                exam_encoder = OneHotEncoder(
                    sparse_output=True, handle_unknown="ignore"
                )
                exam_vectors = exam_encoder.fit_transform(exams_array)
                exam_similarity = (exam_vectors @ exam_vectors.T).toarray()
            except Exception as e:
                logger.warning(f"Error computing exam similarity: {e}")
                exam_similarity = np.zeros((n, n))
        else:
            logger.warning("Warning; all exams are the same (or empty)")
            exam_similarity = (
                np.ones((n, n))
                if len(unique_exams) == 1 and list(unique_exams)[0]
                else np.zeros((n, n))
            )

        logger.debug("Computing Jaccard similarity for categories and techniques")

        def compute_jaccard_similarity(feature_lists):
            if not any(any(lst) for lst in feature_lists):  # All lists are empty
                logger.warning("Warning; all features are empty")
                return np.zeros((n, n))
            try:
                mlb = MultiLabelBinarizer(sparse_output=False)
                binary_matrix = mlb.fit_transform(feature_lists)
                jaccard_distances = pairwise_distances(
                    binary_matrix, metric="jaccard", n_jobs=-1
                )
                return 1.0 - jaccard_distances
            except Exception as e:
                logger.warning(f"Error computing Jaccard similarity: {e}")
                return np.zeros((n, n))

        categories_similarity = compute_jaccard_similarity(exam_categories)

        # Combine techniques and results
        merged_techniques_results = [
            techniques + results
            for techniques, results in zip(
                mathematical_techniques, mathematical_results
            )
        ]
        merged_similarity = compute_jaccard_similarity(merged_techniques_results)

        logger.debug("Combining similarities with per-problem adaptive weighting")
        # don't ask about my weighting strategy
        base_weights = np.array(
            [0.40, 0.15, 0.40, 0.05]
        )  # type, exam, merged, categories
        metadata_similarity_matrix = np.zeros((n, n))

        for i in range(n):
            problem = problems[i]

            # Check which features are available for this specific problem
            available_features = np.array(
                [
                    bool(
                        getattr(problem.metadata, "problem_type", None)
                        if problem.metadata
                        else None
                    ),
                    bool(
                        getattr(problem.exam_info, "exam", None)
                        if problem.exam_info
                        else None
                    ),
                    bool(
                        (
                            getattr(problem.metadata, "mathematical_techniques", None)
                            if problem.metadata
                            else None
                        )
                        or (
                            getattr(problem.metadata, "mathematical_results", None)
                            if problem.metadata
                            else None
                        )
                    ),
                    bool(
                        getattr(problem.exam_info, "exam_category", None)
                        if problem.exam_info
                        else None
                    ),
                ]
            )

            # Normalize weights for available features of this problem
            active_weights = base_weights * available_features
            if active_weights.sum() > 0:
                active_weights = active_weights / active_weights.sum()
            else:
                logger.debug(
                    f"No features available for problem {i}, using zero similarity"
                )
                continue

            metadata_similarity_matrix[i, :] = (
                active_weights[0] * type_similarity[i, :]
                + active_weights[1] * exam_similarity[i, :]
                + active_weights[2] * merged_similarity[i, :]
                + active_weights[3] * categories_similarity[i, :]
            )

        logger.info("Successfully computed metadata similarity matrix")
        return metadata_similarity_matrix

    def _precompute_similarities(self) -> np.ndarray:
        """Precompute similarity matrix using text and/or metadata embeddings."""
        logger.info("Precomputing similarity matrix")
        problems = [self.problem_manager.get_problem(pid) for pid in self.problem_ids]

        embedded_problems = self.embedding_generator.generate_embeddings(problems)
        text_embeddings = np.array([problem.embedding for problem in embedded_problems])
        text_similarity_matrix = util.cos_sim(text_embeddings, text_embeddings).numpy()
        self._text_similarity_matrix = text_similarity_matrix

        if not self.use_hybrid:
            for problem in embedded_problems:
                self.problem_manager.add_problem(problem)
            return text_similarity_matrix

        metadata_similarity_matrix = self.compute_metadata_similarity(problems)

        self._similarity_matrix = (
            self.alpha * text_similarity_matrix
            + (1 - self.alpha) * metadata_similarity_matrix
        )

        for problem in embedded_problems:
            self.problem_manager.add_problem(problem)

        return self._similarity_matrix

    def get_similarity_matrix(self) -> np.ndarray:
        """Get the precomputed similarity matrix, computing it if necessary."""
        if self._similarity_matrix is None:
            self._similarity_matrix = self._precompute_similarities()
        return self._similarity_matrix

    def analyze_weight_distribution(
        self, weights=None, show_histogram=False, cumulative=False
    ):
        if weights is None:
            sim_matrix = self.get_similarity_matrix()
            weights = sim_matrix[~np.eye(sim_matrix.shape[0], dtype=bool)].flatten()

        stats = {
            "min": weights.min(),
            "max": weights.max(),
            "mean": weights.mean(),
            "median": np.median(weights),
            "std_dev": weights.std(),
            "percentile_25": np.percentile(weights, 25),
            "percentile_75": np.percentile(weights, 75),
        }

        logger.info("Weight Distribution Analysis:")
        logger.info(f" Min: {stats['min']:.4f}")
        logger.info(f" Max: {stats['max']:.4f}")
        logger.info(f" Mean: {stats['mean']:.4f}")
        logger.info(f" Median: {stats['median']:.4f}")
        logger.info(f" Std Dev: {stats['std_dev']:.4f}")
        logger.info(f" 25th Percentile: {stats['percentile_25']:.4f}")
        logger.info(f" 75th Percentile: {stats['percentile_75']:.4f}")

        if show_histogram:
            import matplotlib.pyplot as plt

            if cumulative:
                plt.hist(
                    weights, bins=20, cumulative=True, density=True, histtype="step"
                )
                plt.title("Cumulative Distribution of Weights")
                plt.ylabel("Cumulative Probability")
            else:
                plt.hist(weights, bins=20)
                plt.title("Distribution of Weights")
                plt.ylabel("Frequency")
            plt.xlabel("Weight")
            plt.grid(True)
            plt.show()

        return stats

    def find_problem_pairs_at_similarity_percentile(self, percentile, n_pairs=5):
        """
        Find pairs of problems with similarity close to the specified percentile.

        Args:
            percentile: The percentile of similarity to target (0-100).
            n_pairs: Number of problem pairs to retrieve.

        Returns:
            list: List of tuples of (problem_id1, problem_id2, similarity_score)
        """

        sim_matrix = self.get_similarity_matrix()
        n = sim_matrix.shape[0]

        triu_indices = np.triu_indices(n, k=1)
        pair_similarities = sim_matrix[triu_indices]

        target_similarity = np.percentile(pair_similarities, percentile)
        logger.info(
            f"Targeting similarity value: {target_similarity:.4f} (percentile: {percentile})"
        )

        distances = np.abs(pair_similarities - target_similarity)
        closest_indices = np.argsort(distances)[:n_pairs]

        result = []
        for idx in closest_indices:
            i, j = triu_indices[0][idx], triu_indices[1][idx]
            problem_id1 = self.problem_ids[i]
            problem_id2 = self.problem_ids[j]
            similarity = sim_matrix[i, j]
            result.append((problem_id1, problem_id2, similarity))

        return result
