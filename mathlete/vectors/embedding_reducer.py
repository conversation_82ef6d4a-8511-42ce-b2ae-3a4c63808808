from typing import List

import numpy as np
from loguru import logger
from sklearn.decomposition import PCA
from umap import UMAP

from mathlete.utils.constants import MathProblem


class EmbeddingReducer:
    """Handles dimensionality reduction of embeddings using PCA or UMAP.."""

    def __init__(self):
        pass

    def reduce_with_pca(
        self, embedded_problems: List[MathProblem], pca_components: int = 50
    ) -> List[MathProblem]:
        problems_with_embeddings = [
            p for p in embedded_problems if p.embedding is not None
        ]

        if not problems_with_embeddings:
            raise ValueError(
                "No embeddings found in the provided problems. Cannot perform reduction."
            )

        embeddings = np.array([p.embedding for p in problems_with_embeddings])
        n_samples, n_features = embeddings.shape

        pca_components = min(pca_components, n_features)
        if pca_components >= n_samples:
            logger.warning(
                f"Reducing PCA components from {pca_components} to {max(1, n_samples-1)} due to sample size"
            )
            pca_components = max(1, n_samples - 1)

        logger.info(f"Applying PCA reduction to {pca_components} components")
        pca = PCA(n_components=pca_components)
        reduced_embeddings = pca.fit_transform(embeddings)
        logger.info(
            f"Reduced embeddings from {n_features} to {pca_components} dimensions"
        )

        for problem, reduced in zip(problems_with_embeddings, reduced_embeddings):
            problem.embedding = reduced

        return problems_with_embeddings

    def reduce_with_umap(
        self,
        embedded_problems: List[MathProblem],
        umap_components: int = 2,
        umap_neighbors: int = 15,
        clusters: np.ndarray = None,
        similarity_matrix: np.ndarray = None,
    ) -> List[MathProblem]:
        """
        Apply UMAP for visualization.
        Can use either embeddings directly or a precomputed similarity matrix.
        """

        problems_with_embeddings = [
            p for p in embedded_problems if p.embedding is not None
        ]

        if not problems_with_embeddings:
            raise ValueError(
                "No embeddings found in the provided problems. Cannot perform reduction."
            )

        n_samples = len(problems_with_embeddings)

        umap_neighbors = min(umap_neighbors, max(2, n_samples - 1))

        if similarity_matrix is not None:
            if similarity_matrix.shape != (n_samples, n_samples):
                raise ValueError(
                    f"Provided similarity matrix must be of shape ({n_samples}, {n_samples})"
                )
            logger.info(
                f"Applying UMAP reduction to {umap_components} components using a precomputed similarity matrix with {umap_neighbors} neighbors"
            )
            reducer = UMAP(
                n_components=umap_components,
                n_neighbors=umap_neighbors,
                metric="precomputed",
                min_dist=0.1,
            )
            distance_matrix = 1 - similarity_matrix
            fit_data = distance_matrix
        else:
            logger.info(
                f"Applying UMAP reduction to {umap_components} components using cosine distance with {umap_neighbors} neighbors"
            )
            embeddings = np.array([p.embedding for p in problems_with_embeddings])
            reducer = UMAP(
                n_components=umap_components,
                n_neighbors=umap_neighbors,
                metric="cosine",
                min_dist=0.1,
            )
            fit_data = embeddings

        if clusters is not None and len(clusters) == n_samples:
            visualization_embeddings = reducer.fit_transform(fit_data, y=clusters)
        else:
            visualization_embeddings = reducer.fit_transform(fit_data)

        for problem, viz_embedding in zip(
            problems_with_embeddings, visualization_embeddings
        ):
            problem.embedding = viz_embedding

        return problems_with_embeddings
