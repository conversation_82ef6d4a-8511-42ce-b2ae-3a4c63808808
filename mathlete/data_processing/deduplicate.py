import argparse
import json
import os
from pathlib import Path

import pandas as pd
from loguru import logger
from openai import OpenAI
from sentence_transformers import SentenceTransformer, util

from mathlete.data_processing.data_processing_models import ProblemComparison


class ProblemDeduplicator:
    def __init__(
        self,
        model_name="all-MiniLM-L6-v2",
        output_file="data/diagnostics/duplicates.json",
    ):
        """
        Initialize the ProblemDeduplicator with configurable parameters.

        Args:
            model_name (str): Sentence transformer model for embeddings
            similarity_threshold (float): Cosine similarity threshold for duplicates
            output_file (str): Path for storing duplicate information
        """
        self.model_name = model_name
        self.output_file = output_file

        self.model = SentenceTransformer(self.model_name)

    def compute_embeddings(self, problems):
        """
        Compute embeddings for given problems.
        """
        return self.model.encode(problems, normalize_embeddings=True)

    def find_similar_problems(self, df, similarity_threshold=0.9):
        """
        Find similar problems based on embedding cosine similarity.
        """
        logger.info(f"Starting similarity analysis on {len(df)} problems")

        embeddings = self.compute_embeddings(df["problem"].tolist())
        self.similarity_matrix = util.cos_sim(embeddings, embeddings).numpy()
        self.similarity_threshold = similarity_threshold

        clusters = {}
        for i in range(len(df)):
            current_uuid = df.iloc[i]["uuid"]
            similar_items = [
                (df.iloc[j]["uuid"], self.similarity_matrix[i][j])
                for j in range(i + 1, len(df))
                if self.similarity_matrix[i][j] > self.similarity_threshold
            ]

            if similar_items:
                clusters[current_uuid] = similar_items

        clusters = {k: v for k, v in clusters.items() if v}

        logger.info(f"Found {len(clusters)} problem clusters")
        return clusters

    def verify_duplicates_with_gpt(self, problem1, problem2):
        """
        Use GPT-4 to verify if two problems are mathematically the same.
        """
        self.client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        try:
            completion = self.client.beta.chat.completions.parse(
                model="gpt-4o-2024-08-06",
                messages=[
                    {
                        "role": "system",
                        "content": "You are a mathematical problem expert.",
                    },
                    {
                        "role": "user",
                        "content": f"""Are these two mathematical problems essentially the same?

Problem 1: {problem1}

Problem 2: {problem2}

Respond in JSON format with the following structure:
{{
    "verdict": "SAME" or "DIFFERENT"
}}""",
                    },
                ],
                response_format=ProblemComparison,
            )

            gpt_response = completion.choices[0].message.parsed

            return "SAME" in gpt_response.verdict
        except Exception as e:
            logger.error(f"GPT verification failed: {e}")
            return None

    def analyze_clusters(
        self,
        df,
        similarity_threshold=0.9,
        use_gpt_verification=False,
        verbose_mode=True,
    ):
        """
        Comprehensive analysis of problem clusters.

        Args:
            df (pd.DataFrame): DataFrame with problems
            use_gpt_verification (bool): Whether to use GPT for final verification
            verbose_mode (bool): Whether to print detailed comparisons

        Returns:
            dict: Detailed analysis of problem clusters
        """
        clusters = self.find_similar_problems(
            df, similarity_threshold=similarity_threshold
        )
        detailed_analysis = {}

        for key, values in clusters.items():
            base_row = df[df["uuid"] == key].iloc[0]
            cluster_details = []

            for val, score in values:
                comparison_row = df[df["uuid"] == val].iloc[0]

                column_differences = {}
                gpt_verification = None

                for col in df.columns:
                    if col != "uuid":
                        column_differences[col] = {
                            "base": base_row[col],
                            "comparison": comparison_row[col],
                            "match": base_row[col] == comparison_row[col],
                        }

                if use_gpt_verification:
                    gpt_verification = self.verify_duplicates_with_gpt(
                        base_row["problem"], comparison_row["problem"]
                    )

                cluster_details.append(
                    {
                        "uuid": val,
                        "similarity_score": score,
                        "column_differences": column_differences,
                        "gpt_verified_same": gpt_verification,
                    }
                )

                if verbose_mode:
                    logger.info(
                        f"🔍 Comparing UUID {key} with UUID {val}: Similarity {score:.4f}"
                    )
                    logger.info("-" * 60)
                    for col, diff in column_differences.items():
                        logger.info(
                            f"{col}:\n - {diff['base']}\n - {diff['comparison']}"
                        )
                        match_status = "✅ MATCH" if diff["match"] else "❌ DIFFERENT"
                        logger.info(f" → {match_status}\n")

                    if use_gpt_verification and gpt_verification is not None:
                        gpt_status = "✅ SAME" if gpt_verification else "❌ DIFFERENT"
                        logger.info(f"GPT Verification: {gpt_status}\n")

            detailed_analysis[key] = cluster_details

        for _, cluster in detailed_analysis.items():
            for entry in cluster:
                entry["similarity_score"] = float(entry["similarity_score"])

        script_dir = Path(__file__).resolve().parent.parent
        data_path = script_dir / self.output_file
        data_path.parent.mkdir(parents=True, exist_ok=True)

        with open(data_path, "w") as f:
            json.dump(detailed_analysis, f, indent=2)

        logger.info(f"Detailed analysis written to {data_path}")
        return detailed_analysis

    def remove_duplicates(
        self,
        df,
        similarity_threshold=0.9,
        use_gpt_verification=False,
        save_to_file: bool = False,
    ):
        """
        Remove duplicate problems based on similarity analysis.
        Prioritizes keeping problems with non-empty "exam" fields.

        Args:
            df (pd.DataFrame): DataFrame with problems
            similarity_threshold (float): Cosine similarity threshold for duplicates
            use_gpt_verification (bool): Whether to use GPT for final verification
            save_to_file (bool): Whether to save deduplicated dataset to file

        Returns:
            pd.DataFrame: DataFrame with duplicates removed
        """
        clusters = self.find_similar_problems(
            df, similarity_threshold=similarity_threshold
        )

        uuids_to_remove = set()

        for base_uuid, similar_problems in clusters.items():
            for similar_uuid, _ in similar_problems:
                base_problem_row = df[df["uuid"] == base_uuid].iloc[0]
                similar_problem_row = df[df["uuid"] == similar_uuid].iloc[0]

                is_duplicate = True
                if use_gpt_verification:
                    is_duplicate = self.verify_duplicates_with_gpt(
                        base_problem_row["problem"], similar_problem_row["problem"]
                    )

                if is_duplicate:
                    base_has_exam = (
                        pd.notna(base_problem_row.get("exam", None))
                        and base_problem_row["exam"] != ""
                    )
                    similar_has_exam = (
                        pd.notna(similar_problem_row.get("exam", None))
                        and similar_problem_row["exam"] != ""
                    )

                    if base_has_exam and not similar_has_exam:
                        uuids_to_remove.add(similar_uuid)
                    elif similar_has_exam and not base_has_exam:
                        uuids_to_remove.add(base_uuid)
                        break
                    else:
                        uuids_to_remove.add(similar_uuid)

        deduplicated_df = df[~df["uuid"].isin(uuids_to_remove)].copy()

        logger.info(
            f"Duplicates above {similarity_threshold} similarity are removed\n"
            f"Removed {len(df) - len(deduplicated_df)} duplicate problems\n"
            f"Prioritized problems with non-empty exam fields"
        )

        if save_to_file:
            output_path = str(Path(__file__).parent.parent / "data/dedup_data.parquet")
            deduplicated_df.to_parquet(output_path)
            logger.info(f"Deduplication dataset saved in {output_path}")

        return deduplicated_df


def main():
    parser = argparse.ArgumentParser(description="Problem Deduplication Tool")

    parser.add_argument(
        "--input_file",
        type=str,
        default=str(Path(__file__).parent.parent / "data/merged_data.parquet"),
        help="Path to input CSV file containing problems",
    )
    parser.add_argument(
        "--model_name",
        type=str,
        default="all-MiniLM-L6-v2",
        help="Sentence transformer model name",
    )
    parser.add_argument(
        "--output_file",
        type=str,
        default=str(Path(__file__).parent.parent / "data/diagnostics/duplicates.json"),
        help="Output JSON file for duplicates",
    )
    parser.add_argument(
        "--similarity_threshold",
        type=float,
        default=0.9,
        help="Cosine similarity threshold",
    )
    parser.add_argument(
        "--use_gpt_verification",
        action="store_true",
        help="Use GPT verification for duplicates",
    )
    parser.add_argument(
        "--save_to_file", action="store_true", help="Save deduplicated dataset"
    )

    args = parser.parse_args()

    logger.info("Loading input dataset...")
    df = pd.read_parquet(args.input_file)
    logger.info("Input data loaded")

    deduplicator = ProblemDeduplicator(
        model_name=args.model_name, output_file=args.output_file
    )
    logger.info("Running deduplication analysis...")
    deduplicator.remove_duplicates(
        df,
        similarity_threshold=args.similarity_threshold,
        use_gpt_verification=args.use_gpt_verification,
        save_to_file=args.save_to_file,
    )


if __name__ == "__main__":
    main()
