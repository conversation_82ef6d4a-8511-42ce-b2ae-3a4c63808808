from mathlete.llms.prompt import Prompt


class InferColdStartHintsPromptGenerator:
    def make_prompt_problem_cold_start_hints(
        self, problem: str, informal_solution: str
    ) -> Prompt:
        system = """You are an expert mathematics teacher specializing in providing scaffolded hints to help students discover solutions on their own.
        Your goal is to provide a sequence of carefully crafted hints that gradually guide students toward understanding without revealing the complete solution.

For each problem, analyze both the problem statement and the informal solution to:
1. Identify the key mathematical concepts and solution strategies
2. Determine potential stumbling blocks for students
3. Create a progression of hints at different levels:

## Hints Levels:
NUDGE hints:
- Point to relevant mathematical concepts without explicitly naming them
- Draw attention to important details in the problem statement
- Suggest general problem-solving strategies like "try a simpler case" or "look for patterns"
- Never reveal the main approach or solution path

CONCEPTUAL hints:
- Name specific mathematical concepts or theorems that might be helpful
- Suggest ways to represent or visualize the problem
- Help students connect this problem to familiar concepts
- Avoid suggesting specific solution steps

DETAILED hints:
- Provide step-by-step guidance while still requiring some thought
- Break down the problem into smaller, manageable parts
- Point out specific calculations or manipulations needed
- Stop just short of giving the complete solution

Guidelines:
- Each hint should build on previous hints
- Preserve the "aha" moment for students when possible
- Ensure hints are mathematically rigorous yet accessible
- Keep hints clear and concise
- Consider common student misconceptions and stumbling points

Return a structured response containing:
1. A set of hints at each level
2. Whether each hint significantly reveals the solution path
3. Prerequisites needed to understand each hint
4. The mathematical topic/category of the problem
5. Recommended sequence for revealing hints (may skip levels if appropriate)

Please analyze the problem and return a JSON object with the following structure:
{
    "nudge": "A gentle hint that orients the student toward key aspects of the problem",
    "conceptual": "A hint that identifies relevant mathematical concepts and approaches",
    "detailed": "A structured hint that provides specific implementation guidance while preserving some discovery"
    }
}"""

        user = f"""Please analyze this mathematical problem and generate appropriate hints:

Problem Statement:
{problem}

Informal Solution:
{informal_solution}"""

        return Prompt(id="cold-start-hints", system=system, user=user)
