import argparse
from concurrent.futures import Thread<PERSON>oolExecutor
from pathlib import Path

import pandas as pd
from loguru import logger

from mathlete.data_processing.infer_cold_start_hints.infer_cold_start_hints_models import (
    ColdStartHintGenerationResult,
)
from mathlete.data_processing.infer_cold_start_hints.infer_cold_start_hints_prompts import (
    InferColdStartHintsPromptGenerator,
)
from mathlete.llms import llms


class MathHintGenerator:
    def __init__(self):
        pass

    def generate_hints(
        self, problem: str, informal_solution: str
    ) -> ColdStartHintGenerationResult:
        logger.info(f"Generating hints for problem {problem}")

        prompt_generator = InferColdStartHintsPromptGenerator()
        prompt = prompt_generator.make_prompt_problem_cold_start_hints(
            problem, informal_solution
        )
        try:
            return llms.execute_prompt(
                prompt=prompt, output_class=ColdStartHintGenerationResult
            )

        except Exception as e:
            logger.error(f"Error generating hints: {str(e)}")
            raise RuntimeError(f"Hint generation failed: {str(e)}")


def process_problems(df: pd.DataFrame, max_workers: int) -> pd.DataFrame:
    hint_generator = MathHintGenerator()

    HintLevel = ["nudge", "conceptual", "detailed"]
    hint_columns = [f"hint_{level}" for level in HintLevel]

    def generate_hints_for_problem(row):
        try:
            result = hint_generator.generate_hints(
                row["problem"], row["informal_solution"]
            )
            hints = {f"hint_{level}": getattr(result, level) for level in HintLevel}
        except Exception as e:
            logger.error(f"Error processing problem: {e}")
            hints = {col: None for col in hint_columns}

        return pd.Series(hints)

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        results = list(
            executor.map(generate_hints_for_problem, [row for _, row in df.iterrows()])
        )

    results_df = pd.DataFrame(results, columns=hint_columns, index=df.index)
    return pd.concat([df[["uuid"]], results_df], axis=1)


def generate_hints(
    input_file: Path, output_file: Path, max_workers: int = 20
) -> pd.DataFrame:
    """
    Process problems from input file and save results with hints to output file.
    """
    logger.info(f"Reading problems from {input_file}")
    df = pd.read_parquet(input_file)

    total_problems = len(df)
    logger.info(f"Found {total_problems} problems to process")

    enhanced_df = process_problems(df, max_workers)

    if output_file:
        enhanced_df.to_parquet(output_file)
        logger.info(f"Saved results to {output_file}")
    return enhanced_df


def main():
    parser = argparse.ArgumentParser(
        description="Generate scaffolded hints for mathematical problems."
    )

    script_dir = Path(__file__).parent.parent
    default_input_file = script_dir / "../data/merged_data.parquet"
    default_output_file = script_dir / "../data/hints_data.parquet"

    parser.add_argument(
        "input_file",
        type=Path,
        nargs="?",
        default=default_input_file,
        help="Input file path (Parquet format)",
    )
    parser.add_argument(
        "output_file",
        type=Path,
        nargs="?",
        default=default_output_file,
        help="Output file path",
    )
    parser.add_argument(
        "--max_workers",
        type=int,
        default=50,
        help="Maximum number of worker threads (default is 20)",
    )

    args = parser.parse_args()

    _ = generate_hints(args.input_file, args.output_file, args.max_workers)
    logger.info("Processing completed")


if __name__ == "__main__":
    main()
