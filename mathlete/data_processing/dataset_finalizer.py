import argparse
from pathlib import Path

import pandas as pd
from loguru import logger

from mathlete.data_processing.deduplicate import ProblemDeduplicator


def main():
    parser = argparse.ArgumentParser(description="Problem Deduplication Tool")

    parser.add_argument(
        "--input_file",
        type=str,
        default=str(Path(__file__).parent.parent / "data/final_data.parquet"),
        help="Path to input Parquet file containing problems",
    )
    parser.add_argument(
        "--model_name",
        type=str,
        default="all-MiniLM-L6-v2",
        help="Sentence transformer model name",
    )
    parser.add_argument(
        "--output_file",
        type=str,
        default=str(Path(__file__).parent.parent / "data/diagnostics/duplicates.json"),
        help="Output JSON file for duplicates",
    )
    parser.add_argument(
        "--similarity_threshold",
        type=float,
        default=0.85,
        help="Cosine similarity threshold",
    )
    parser.add_argument(
        "--use_gpt_verification",
        action="store_true",
        help="Use GPT verification for duplicates",
    )
    parser.add_argument(
        "--save_to_file",
        action="store_true",
        help="Save deduplicated dataset",
    )

    args = parser.parse_args()

    logger.info("Loading input dataset...")
    df = pd.read_parquet(args.input_file)
    logger.info(f"Original dataset loaded with {len(df)} rows.")

    # df_filt = df[df["exam"].notna() | df["is_valid_no_sorry"]]
    df_filt = df[df["is_valid_no_sorry"]]
    logger.info(
        f"Filtered dataset to {len(df_filt)} rows based on exam and is_valid_no_sorry criteria."
    )

    # remove aops and aops-wiki data for now
    df_last = df_filt[~df_filt["source"].isin(["aops", "aops-wiki"])]
    logger.info(f"Filtered dataset to {len(df_last)} rows based on source criteria.")

    deduplicator = ProblemDeduplicator(
        model_name=args.model_name, output_file=args.output_file
    )
    logger.info("Running deduplication analysis...")
    deduplicator.remove_duplicates(
        df_last,
        similarity_threshold=args.similarity_threshold,
        use_gpt_verification=args.use_gpt_verification,
        save_to_file=args.save_to_file,
    )
    logger.info("Deduplication completed.")


if __name__ == "__main__":
    main()
