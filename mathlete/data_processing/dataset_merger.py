import argparse
import json
from pathlib import Path
from typing import Dict, List, Optional, Tuple

import pandas as pd
from datasets import load_dataset
from loguru import logger

from mathlete.data_processing.dataset_postprocessor import clean_dataset


class BaseDataProcessor:
    def __init__(self):
        self.dataset_mapping = {
            "aops": "AI-MO/aops-base-v2.0",
            "aops-wiki": "AI-MO/aops-wiki-base",
            "aops-forum": "AI-MO/aops-base-v2.0",
            "olympiads": "AI-MO/olympiads-0.4-base",
            "olympiads-ref": "AI-MO/olympiads-ref-base",
            "MATH": "AI-MO/math-base-v1.0",
            "cnk12": "AI-MO/cn_k12-base",
            "cn_k12": "AI-MO/cn_k12-base",
            "orca": "AI-MO/orca_math-base",
            "metamath": "AI-MO/metamath-base",
            "number-theory-books": "AI-MO/number-theory-base",
        }

        self.column_selection = {
            "cnk12": ["uuid", "question_type", "problem_type"],
            "cn_k12": ["uuid", "question_type", "problem_type"],
            "aops": [
                "uuid",
                "question_type",
                "problem_type",
                "year",
                "exam",
                "category",
            ],
            "aops-wiki": ["uuid", "question_type", "problem_type", "url"],
            "aops-forum": [
                "uuid",
                "question_type",
                "problem_type",
                "year",
                "exam",
                "category",
            ],
            "olympiads": ["uuid", "question_type", "problem_type", "metadata-origin"],
            "olympiads-ref": ["uuid", "question_type", "problem_type", "metadata"],
            "number-theory-books": ["uuid", "question_type", "problem_type"],
            "MATH-train": ["uuid", "problem_type", "question_type", "level"],
            "MATH-test": ["uuid", "problem_type", "question_type", "level"],
            "metamath": ["uuid", "question_type", "problem_type"],
            "orca": ["uuid", "question_type", "problem_type"],
        }

        # rows in numina-math-lean4 but not in the corresponding source dataset
        self.unmatched_rows_dict = {}

    def get_hf_dataset(self, source_name: str) -> Optional[pd.DataFrame]:
        """
        Fetch and process a dataset from Hugging Face.

        Args:
            source_name: Name of the source dataset

        Returns:
            Processed pandas DataFrame or None if there's an error
        """
        if source_name in ["MATH-train", "MATH-test"]:
            split = "train" if source_name == "MATH-train" else "test"
            dataset_path = self.dataset_mapping["MATH"]
            try:
                dataset = load_dataset(dataset_path)[split].to_pandas()
            except Exception as e:
                logger.error(f"Error loading MATH dataset: {e}")
                return None

        elif source_name == "olympiads-ref":
            dataset = load_dataset(
                "AI-MO/olympiads-ref-base",
                # revision="96d53e8a385246b8504a17afe55587eb54cbde44",
            )["train"].to_pandas()

        else:
            dataset_path = self.dataset_mapping.get(source_name)
            if not dataset_path:
                logger.error(f"No corresponding dataset found for {source_name}")
                return None

            try:
                dataset = load_dataset(dataset_path)["train"].to_pandas()
            except Exception as e:
                logger.error(f"Error loading dataset {dataset_path}: {e}")
                return None

        selected_columns = self.column_selection.get(source_name, ["uuid"])
        missing_columns = [
            col for col in selected_columns if col not in dataset.columns
        ]

        if missing_columns:
            logger.warning(
                f"Warning: Missing columns {missing_columns} in dataset {dataset_path}"
            )
            return None

        return dataset[selected_columns]

    def check_duplicates(self, df: pd.DataFrame, source: str) -> pd.DataFrame:
        """
        Check for and handle duplicate UUIDs.

        Args:
            df: Input DataFrame
            source: Source name for logging

        Returns:
            DataFrame with duplicates removed
        """
        duplicates = df.duplicated(subset=["uuid"], keep=False)
        if duplicates.any():
            logger.warning(
                f"Warning: Found duplicate UUIDs in dataset for source {source}"
            )
            return df.drop_duplicates(subset=["uuid"])
        return df

    def merge_datasets(
        self,
        df_math_numina: pd.DataFrame,
        output_path: str = "merged_dataset.parquet",
        unmatched_output_path: str = "unmatched_uuids.json",
        write_output: bool = True,
    ) -> Tuple[pd.DataFrame, Dict[str, List[str]], List[str]]:
        """
        Merge multiple datasets based on UUID.

        Args:
            df_math_numina: Main DataFrame to merge with Hugging Face datasets
            output_path (str): Path to save the merged dataset
            unmatched_output_path (str): Path to save the unmatched UUIDs
            write_output (bool): Whether to write the output to files

        Returns:
            Tuple of (merged DataFrame, dictionary of unmatched UUIDs by dataset path, list of all unmatched UUIDs)
        """
        merged_dfs = []
        all_initial_uuids = set(df_math_numina["uuid"])

        math_numina_sources = df_math_numina["source"].unique()
        current_sources = (
            set(self.dataset_mapping.keys())
            | {"unknown"}
            | {"MATH-test"}
            | {"MATH-train"}
        )
        new_sources = list(set(math_numina_sources) - set(current_sources))
        if new_sources:
            logger.warning(
                f"There are new sources {new_sources} of data that are not mapped"
            )

        for source in df_math_numina["source"].unique():
            logger.info(f"Processing source: {source}")

            hf_df = self.get_hf_dataset(source)
            if hf_df is None:
                continue

            hf_df = self.check_duplicates(hf_df, source)

            try:
                merged_df = df_math_numina[df_math_numina["source"] == source].merge(
                    hf_df, on="uuid", how="inner"
                )

                unmatched_rows = list(
                    set(df_math_numina[df_math_numina["source"] == source]["uuid"])
                    - set(hf_df["uuid"])
                )
                if unmatched_rows:
                    dataset_path = self.dataset_mapping.get(source, source)
                    self.unmatched_rows_dict[dataset_path] = unmatched_rows
                    logger.warning(
                        f"Unmatched UUIDs from df_math_numina for {dataset_path}: {unmatched_rows}"
                    )

                merged_dfs.append(merged_df)

            except Exception as e:
                logger.error(f"Error merging {source}: {e}")
                continue
        logger.info("Concatenating datasets...")
        final_dataset = pd.concat(merged_dfs, ignore_index=True)
        logger.debug(f"Final dataset columns: {final_dataset.columns}")
        logger.debug(f"Final dataset shape: {final_dataset.shape}")
        logger.info("Cleaning dataset...")
        final_dataset = clean_dataset(final_dataset)
        all_final_uuids = set(final_dataset["uuid"])
        all_unmatched_uuids = list(all_initial_uuids - all_final_uuids)
        logger.info(
            f"Total number of unmatched UUIDs across all sources: {len(all_unmatched_uuids)}"
        )

        if write_output:

            project_root = Path(__file__).resolve().parents[1]

            output_path = project_root / "data" / output_path
            unmatched_output_path = (
                project_root / "data" / "diagnostics" / unmatched_output_path
            )

            output_path.parent.mkdir(parents=True, exist_ok=True)
            unmatched_output_path.parent.mkdir(parents=True, exist_ok=True)

            final_dataset.to_parquet(output_path)
            logger.info(f"Merged dataset saved to {output_path}")
            unmatched_output_path.write_text(
                json.dumps(
                    {
                        "unmatched_by_dataset": self.unmatched_rows_dict,
                        "total_unmatched": all_unmatched_uuids,
                    },
                    indent=2,
                )
            )
            logger.info(f"Unmatched data saved to {unmatched_output_path}")

        return final_dataset, self.unmatched_rows_dict, all_unmatched_uuids


def main():
    parser = argparse.ArgumentParser(description="Merge Hugging Face datasets")
    parser.add_argument(
        "--write_output",
        action="store_true",
        default=True,
        help="Flag to write the merged dataset and unmatched UUIDs to files",
    )
    args = parser.parse_args()

    math_numina_lean_path = "AI-MO/numina-math-lean4"
    math_numina_lean = load_dataset(math_numina_lean_path)["train"]
    df_math_numina = math_numina_lean.to_pandas()

    processor = BaseDataProcessor()
    _, _, _ = processor.merge_datasets(df_math_numina, write_output=args.write_output)


if __name__ == "__main__":
    main()
