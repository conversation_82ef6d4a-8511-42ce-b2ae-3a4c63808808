import argparse
from concurrent.futures import ThreadPoolExecutor
from pathlib import Path

import pandas as pd
from loguru import logger

from mathlete.data_processing.infer_problem_name.infer_problem_name_models import (
    ProblemName,
)
from mathlete.data_processing.infer_problem_name.infer_problem_name_prompts import (
    InferProblemNamePromptGenerator,
)
from mathlete.llms import llms


class InferProblemName:
    def __init__(self):
        pass

    def generate_problem_name(self, problem: str) -> ProblemName:
        logger.info(f"Generating problem name for problem {problem}")
        prompt_generator = InferProblemNamePromptGenerator()
        prompt = prompt_generator.make_prompt_problem_name(problem)
        try:
            return llms.execute_prompt(prompt=prompt, output_class=ProblemName)
        except Exception as e:
            logger.error(f"Error generating problem name: {str(e)}")
            raise RuntimeError(f"Problem name generation failed: {str(e)}")


def process_problems(df: pd.DataFrame, max_workers: int) -> pd.DataFrame:
    name_generator = InferProblemName()

    def generate_name_for_problem(row):
        try:
            result = name_generator.generate_problem_name(row["problem"])
            return pd.Series({"problem_name": result.problem_name})
        except Exception as e:
            logger.error(f"Error processing problem: {e}")
            return pd.Series({"problem_name": None})

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        results = list(
            executor.map(generate_name_for_problem, [row for _, row in df.iterrows()])
        )

    results_df = pd.DataFrame(results, columns=["problem_name"], index=df.index)
    return pd.concat([df[["uuid"]], results_df], axis=1)


def generate_problem_names(
    input_file: Path, output_file: Path, max_workers: int = 20
) -> pd.DataFrame:
    logger.info(f"Reading problems from {input_file}")
    df = pd.read_parquet(input_file)

    total_problems = len(df)
    logger.info(f"Found {total_problems} problems to process")

    processed_df = process_problems(df, max_workers)
    if output_file:
        processed_df.to_parquet(output_file)
        logger.info(f"Saved results to {output_file}")
    return processed_df


def main():
    parser = argparse.ArgumentParser(
        description="Generate problem names for mathematical problems."
    )

    script_dir = Path(__file__).parent.parent
    default_input_file = script_dir / "../data/merged_data.parquet"
    default_output_file = script_dir / "../data/problem_names_data.parquet"

    parser.add_argument(
        "input_file",
        type=Path,
        nargs="?",
        default=default_input_file,
        help="Input file path (Parquet format)",
    )
    parser.add_argument(
        "output_file",
        type=Path,
        nargs="?",
        default=default_output_file,
        help="Output file path",
    )
    parser.add_argument(
        "--max_workers",
        type=int,
        default=50,
        help="Maximum number of worker threads (default is 20)",
    )

    args = parser.parse_args()
    _ = generate_problem_names(args.input_file, args.output_file, args.max_workers)
    logger.info("Processing completed")


if __name__ == "__main__":
    main()
