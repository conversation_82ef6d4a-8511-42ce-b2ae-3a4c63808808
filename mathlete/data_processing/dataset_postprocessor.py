import ast
import re
from typing import Dict, List, Set, Union

import numpy as np
import pandas as pd
from loguru import logger


def normalize_category(value):
    if value is None:
        return []

    if isinstance(value, str):
        try:
            value = ast.literal_eval(value)
        except (ValueError, SyntaxError):
            value = [value]

    if isinstance(value, list):
        return [category.replace("_", " ").title() for category in value]

    return []


def build_exam_category_mapping(df: pd.DataFrame) -> Dict[str, List[str]]:
    """
    Build a mapping of exams to their unique categories, excluding 'unknown' categories.

    Args:
        df: DataFrame containing 'exam' and 'exam_category' columns

    Returns:
        Dictionary mapping exam names to lists of unique categories
    """

    def parse_categories(categories: Union[str, List, None]) -> List[str]:
        """Convert various category formats into a list of categories."""
        if categories is None:
            return []

        if isinstance(categories, str):
            try:
                parsed = ast.literal_eval(categories)
                return parsed if isinstance(parsed, list) else [categories]
            except (<PERSON><PERSON>rror, SyntaxError):
                return [categories]

        return categories if isinstance(categories, list) else []

    exam_category_dict: Dict[str, Set[str]] = {}

    for exam, categories in zip(df["exam"], df["exam_category"]):
        if exam is None:
            continue

        if exam not in exam_category_dict:
            exam_category_dict[exam] = set()

        parsed_categories = parse_categories(categories)
        exam_category_dict[exam].update(parsed_categories)

    logger.debug(f"Exam_category mapping: {exam_category_dict}")

    # Filter out unknown categories and empty entries
    return {
        exam: [cat for cat in categories if cat and cat.lower() != "unknown"]
        for exam, categories in exam_category_dict.items()
        if exam
        and categories
        and any(cat and cat.lower() != "unknown" for cat in categories)
    }


def is_empty(x: Union[np.ndarray, str, list, None]) -> bool:
    """
    Check if a value is empty (empty array, None, empty string, or empty list).

    Args:
        x: Value to check

    Returns:
        True if the value is considered empty, False otherwise
    """
    if isinstance(x, np.ndarray):
        return x.size == 0  # NumPy arrays
    if x is None or (isinstance(x, float) and pd.isna(x)):
        return True  # NaN values
    if isinstance(x, str) and not x.strip():
        return True  # Empty strings
    if isinstance(x, list):
        return len(x) == 0 or all(
            is_empty(item) for item in x
        )  # Check if list is empty or contains only empty values
    return False


def update_exam_categories(df: pd.DataFrame) -> pd.DataFrame:
    """
    Update exam categories in the DataFrame using the built mapping.

    Args:
        df: DataFrame to update

    Returns:
        Updated DataFrame with filled exam categories
    """
    exam_category_mapping = build_exam_category_mapping(df)
    logger.debug(f"Now this is the mapping {exam_category_mapping}")

    special_exam_mapping = {
        "AHSME": ["National And Regional Contests"],
        "AIME": ["National Olympiads", "National And Regional Contests"],
        "AIME_I": ["National Olympiads", "National And Regional Contests"],
        "AIME_II": ["National Olympiads", "National And Regional Contests"],
        "AJHSME": ["National And Regional Contests"],
        "AMC_10": ["National And Regional Contests"],
        "AMC_10A": ["National And Regional Contests"],
        "AMC_10B": ["National And Regional Contests"],
        "AMC_12": ["National And Regional Contests"],
        "AMC_12A": ["National And Regional Contests"],
        "AMC_12B": ["National And Regional Contests"],
        "AMC_12P": ["National And Regional Contests"],
        "AMC_8": ["National And Regional Contests"],
    }

    def get_category(exam):
        if exam in special_exam_mapping:
            return special_exam_mapping[exam]
        else:
            return (
                exam_category_mapping.get(exam)
                if exam_category_mapping.get(exam)
                else None
            )

    df["exam_category"] = df["exam_category"].mask(
        df["exam_category"].apply(is_empty),
        df["exam"].map(lambda exam: get_category(exam)),
    )

    return df


def clean_dataset(df, remove_empty: bool = True):
    """
    Cleans and processes the dataset by:
    - Filling missing 'exam' and 'level' from metadata.
    - Normalizing 'category' into 'exam_category'.
    - Extracting 'year' from 'resource_path' if missing.
    - Removing rows with empty essential fields.
    - Selecting relevant columns.

    Args:
        df (pd.DataFrame): Input DataFrame with raw data.
        remove_empty (bool): Remove problems with no problem, lean_code or informal_solution.

    Returns:
        pd.DataFrame: Cleaned and processed DataFrame.
    """

    def extract_exam(tag_str):
        try:
            tags = ast.literal_eval(tag_str)
            for tag in tags:
                if tag.startswith("exam:"):
                    return tag.split("exam:")[1]
        except (ValueError, SyntaxError):
            return None
        return None

    df["exam"] = df["exam"].where(df["exam"].notna(), df["tags"].apply(extract_exam))

    df["level"] = df["level"].where(
        df["level"].notna(),
        df["metadata"].apply(lambda x: x.get("tier") if isinstance(x, dict) else None),
    )

    # Extract 'problem_match' as 'problem_number'
    df["problem_number"] = df["metadata"].apply(
        lambda x: x.get("problem_match") if isinstance(x, dict) else None
    )

    # Extract unique resource paths
    resource_paths = (
        df["metadata"]
        .dropna()
        .apply(lambda x: x.get("resource_path") if isinstance(x, dict) else None)
        .dropna()
        .unique()
        .tolist()
    )

    # Extract IMO years from paths
    pattern = r"IMO(\d{4})"
    path_to_year = {
        path: int(match.group(1))
        for path in resource_paths
        for match in re.finditer(pattern, path)
    }

    # Fill missing 'year' from 'resource_path'
    def get_year_from_metadata(row):
        if pd.notna(row["year"]):
            return row["year"]
        metadata = row["metadata"]
        if isinstance(metadata, dict) and "resource_path" in metadata:
            return path_to_year.get(metadata["resource_path"], None)
        return None

    df["year"] = df.apply(get_year_from_metadata, axis=1)

    # (year, exam, problem_id)
    pattern_url = r"artofproblemsolving\.com/wiki/index\.php/(\d{4})_([A-Z]+(?:[ _][A-Z0-9]+)?)_Problems/Problem_(\d+)"

    def extract_from_url(url):
        if not isinstance(url, str):
            return None, None, None
        match = re.search(pattern_url, url)
        if match:
            return match.groups()
        return None, None, None

    extracted_values = df["url"].apply(lambda x: pd.Series(extract_from_url(x)))
    df["year"] = df["year"].where(df["year"].notna(), extracted_values[0])

    """logger.info("Loading IMO dataset to extract years...")
    from datasets import load_dataset
    hf_dataset = load_dataset("AI-MO/aops_wiki_imo", split="train")
    hf_df = hf_dataset.to_pandas()
    df = df.merge(hf_df[["uuid", "year"]], on="uuid", how="left", suffixes=("", "_hf"))
    df["year"] = df["year"].combine_first(df["year_hf"])
    """

    df["year"] = df["year"].fillna("Unknown").astype(str)
    df["exam"] = df["exam"].where(df["exam"].notna(), extracted_values[1])
    df["problem_number"] = df["problem_number"].where(
        df["problem_number"].notna(), extracted_values[2]
    )

    df["exam_category"] = df["category"].apply(normalize_category)
    df = update_exam_categories(df)

    if remove_empty:
        essential_columns = ["problem", "lean_code", "informal_solution"]

        # Capture rows that will be dropped
        dropped_rows_na = df[df[essential_columns].isna().any(axis=1)]
        dropped_rows_empty = df[
            df[essential_columns].applymap(lambda x: str(x).strip() == "").any(axis=1)
        ]
        dropped_rows = pd.concat([dropped_rows_na, dropped_rows_empty]).drop_duplicates(
            subset=["uuid"]
        )
        display_columns = essential_columns + ["uuid", "source", "exam"]
        dropped_rows = dropped_rows[display_columns]
        logger.warning(
            f"Dropping the following rows with no {essential_columns}:\n {dropped_rows}"
        )

        # Perform the filtering
        df = df.dropna(subset=essential_columns, how="any")
        logger.info(f"Dropped {len(dropped_rows)} rows with no {essential_columns}")
        df = df[
            ~df[essential_columns].apply(
                lambda row: row.astype(str).str.strip().eq("").any(), axis=1
            )
        ]
        logger.info(
            f"Dropped {len(dropped_rows_empty)} rows with empty {essential_columns}"
        )

    # Select relevant columns
    columns = [
        "uuid",
        "problem",
        "informal_solution",
        "lean_code",
        "lean4_solution",
        "natural_language",
        "source",
        "question_type",
        "problem_type",
        "problem_number",
        "exam",
        "exam_category",
        "year",
        "level",
        "is_valid_no_sorry",
        "is_valid_with_sorry",
    ]
    df = df[columns]

    return df
