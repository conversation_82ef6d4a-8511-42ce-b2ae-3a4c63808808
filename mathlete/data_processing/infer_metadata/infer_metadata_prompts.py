from mathlete.llms.prompt import Prompt


class InferMetadataPromptGenerator:
    def make_prompt_mathematical_analysis(
        self, problem: str, informal_solution: str
    ) -> Prompt:
        system = """You are an expert in analyzing mathematical proofs. Analyze the given problem and solution carefully, following these guidelines:

            1. LANGUAGES IDENTIFICATION
            - Include ONLY natural spoken/written languages (e.g., English, Russian, Chinese)

            2. MATHEMATICAL TECHNIQUES IDENTIFICATION
            - Include both high-level proof techniques and specific mathematical methods used in the solution. Note: These techniques describe the approach or method of reasoning applied to solve the problem, and are distinct from mathematical results (established theorems or formal conclusions).

            A. **Proof Techniques (High-level strategies)**: Mathematical induction, Proof by contradiction, Proof by contrapositive, Direct proof, Proof by cases, Pigeonhole principle, Proof by exhaustion, Proof by minimum counterexample

            B. **Algebraic Techniques** (methods for algebraic problem solving, not formal algebraic theorems): Factorization, Completing the square, Polynomial long division, Partial fractions decomposition, Variable substitution, Rationalization

            C. **Calculus Techniques** (procedures for solving calculus problems, not established calculus theorems): Integration by parts, Integration by substitution, Partial differentiation, Implicit differentiation, <PERSON><PERSON>Hô<PERSON>al's technique, Taylor series expansion

            D. **Number Theory Techniques** (problem-solving methods in number theory, distinct from formal results): Prime factorization, Modular arithmetic, Divisibility tests, GCD computation, Diophantine analysis

            E. **Geometric Techniques** (methods for approaching geometry problems, not formal geometric results): Angle chasing, Area comparison, Similarity arguments, Coordinate geometry, Vector operations, Geometric transformations

            - DO NOT include:
            * Vague descriptions ("algebraic manipulation", "simplification")
            * Problem-specific steps without a named technique
            * Software tools or programming techniques
            * General mathematical concepts without specific technique names

            3. MATHEMATICAL RESULTS IDENTIFICATION
            - Include only established theorems, lemmas, inequalities, or formal results
            - Common examples:
            * Pythagorean theorem
            * AM-GM inequality
            * Cauchy-Schwarz inequality
            * Fundamental theorem of algebra
            - DO NOT include:
            * General concepts or topics
            * Problem-specific findings
            * Intermediate steps in the proof
            - For results with multiple names, use the canonical version
            Example: Use "AM-GM inequality" not "Arithmetic-Geometric Mean Inequality"

            Response Format:
            {
                "languages": ["list of natural languages only"],
                "mathematical_techniques": ["list of distinct techniques"],
                "mathematical_results": ["list of distinct results"]
            }"""

        user = f"PROBLEM\n{problem}\n\nINFORMAL SOLUTION\n{informal_solution}"

        return Prompt(id="infer_metadata", system=system, user=user)

    def make_prompt_non_mathematical_analysis(self, informal_solution: str) -> Prompt:
        system = """Extract and remove ONLY author/source metadata and administrative elements. Be conservative in what you remove, focusing specifically on attribution information, publication references, document markers, and administrative text while preserving all substantive content.

                EXTRACT AND REMOVE ONLY:
                1. Author/source information (including usernames and signatures):
                - "Solution by [name]"
                - "From [publication]"
                - "Posted by [username]"
                - Signatures: `~ username`, `-username`, `— username`
                - User mentions: Any **standalone** phrase resembling a username (e.g., text containing a mix of letters, numbers, and/or symbols, often appearing at the end of the text)
                - User signatures with emojis or symbols: (`~ username 😃`)

                2. Publication metadata:
                - Version indicators: "Version [number]", "v[number]"
                - Timestamps: "Last updated: [date]", "Created: [date]"
                - Page numbering: "Page [number]", "p. [number]"
                - Document status indicators: "Draft", "Final", "Confidential"
                - Administrative text: "This page is intentionally left blank", "End of document"
                - Header/footer content: Copyright notices, legal disclaimers
                - Document identifiers: "Document ID: [number]", "Reference: [code]"

                DO NOT REMOVE:
                1. Mathematical content:
                - References to theorems ("By difference of squares...")
                - Solution steps ("First, we observe...")
                - Problem context ("Joe has 2 ounces...")
                - Solution numbering ("Solution 1")
                - Mathematical notes ("NOTE:", "Observe that")
                - Editorial comments ("This is elegant")

                Examples:

                Input 1:
                PROOF: "By difference of squares, where $a$ and $b$ are nonzero digits."
                Output 1: []

                Input 2:
                PROOF: "Using the binomial theorem, we can expand $(x+y)^n$. This shows that the coefficient of $x^2y^{n-2}$ is $\binom{n}{2}$. Therefore, the answer is 15.
                Solution by Dr. Jane Wilson"
                Output 2: ["Solution by Dr. Jane Wilson"]

                Input 3:
                PROOF: "The function $f(x)$ is differentiable. -random_user123"
                Output 3: ["random_user123"]

                Input 4:
                PROOF: "By induction, assume the base case holds. ~ User_99 😃"
                Output 4: ["~ User_99 😃"]

                Response Format:
                {
                    "non_mathematical_text": ["only extracted metadata occurrences, empty list if none found"]
                }"""

        user = f"INFORMAL SOLUTION\n{informal_solution}"

        return Prompt(id="infer_metadata", system=system, user=user)
