import argparse
from concurrent.futures import ThreadPoolExecutor
from pathlib import Path

import pandas as pd
from loguru import logger

from mathlete.data_processing.infer_metadata.infer_metadata_models import (
    MathematicalAnalysis,
    NonMathematicalAnalysis,
    ProblemAnalysis,
)
from mathlete.data_processing.infer_metadata.infer_metadata_prompts import (
    InferMetadataPromptGenerator,
)
from mathlete.llms import llms


class ProblemAnalyzer:
    def __init__(self):
        pass

    def analyze_mathematical_content(
        self,
        problem: str,
        informal_solution: str,
    ) -> MathematicalAnalysis:

        logger.info(
            f"Extracting languages, mathematical techniques, results, and non-mathematical text from:\n Problem: {problem}\n Solution:{informal_solution}"
        )

        prompt_generator = InferMetadataPromptGenerator()
        prompt = prompt_generator.make_prompt_mathematical_analysis(
            problem, informal_solution
        )
        try:
            return llms.execute_prompt(prompt=prompt, output_class=MathematicalAnalysis)
        except Exception as e:
            logger.error(f"Error analyzing mathematical content: {str(e)}")
            raise RuntimeError(f"Mathematical analysis failed: {str(e)}")

    def extract_non_mathematical_text(
        self, informal_solution: str
    ) -> NonMathematicalAnalysis:
        prompt_generator = InferMetadataPromptGenerator()
        prompt = prompt_generator.make_prompt_non_mathematical_analysis(
            informal_solution
        )
        try:
            return llms.execute_prompt(
                prompt=prompt, output_class=NonMathematicalAnalysis
            )
        except Exception as e:
            logger.error(f"Error extracting non-mathematical text: {str(e)}")
            raise RuntimeError(f"Text extraction failed: {str(e)}")

    def analyze_problem(self, problem: str, informal_solution: str) -> ProblemAnalysis:
        math_analysis = self.analyze_mathematical_content(problem, informal_solution)
        non_math_analysis = self.extract_non_mathematical_text(informal_solution)

        return ProblemAnalysis(
            languages=math_analysis.languages,
            mathematical_techniques=math_analysis.mathematical_techniques,
            mathematical_results=math_analysis.mathematical_results,
            non_mathematical_text=non_math_analysis.non_mathematical_text,
        )


def analyze_problems(input_file, output_file, max_workers=20):
    analyzer = ProblemAnalyzer()

    def analyze_row(row):
        try:
            result = analyzer.analyze_problem(row["problem"], row["informal_solution"])
            logger.info(f"Extracted metadata are {result}")
            return (
                result.languages,
                result.mathematical_techniques,
                result.mathematical_results,
                result.non_mathematical_text,
            )
        except Exception as e:
            logger.debug(
                f"An error occured while extracing metadata for row {row}: {e}"
            )
            return [], [], [], []

    df = pd.read_parquet(input_file)

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        results = list(executor.map(analyze_row, [row for _, row in df.iterrows()]))

    metadata_columns = [
        "languages",
        "mathematical_techniques",
        "mathematical_results",
        "non_mathematical_text",
    ]

    df_metadata = pd.DataFrame(results, columns=metadata_columns, index=df.index)
    df = pd.concat([df[["uuid"]], df_metadata], axis=1)
    if output_file:
        df.to_parquet(output_file)

    return df


def main():
    parser = argparse.ArgumentParser(description="Extract metadata from problems.")

    script_dir = Path(__file__).parent.parent
    default_input_file = script_dir / "../data/merged_data.parquet"
    default_output_file = script_dir / "../data/enhanced_data.parquet"

    parser.add_argument(
        "input_file",
        type=Path,
        nargs="?",
        default=default_input_file,
        help="Input file path (Parquet format)",
    )
    parser.add_argument(
        "output_file",
        type=Path,
        nargs="?",
        default=default_output_file,
        help="Output file path",
    )
    parser.add_argument(
        "--max_workers",
        type=int,
        default=20,
        help="Maximum number of worker threads (default is 20)",
    )

    args = parser.parse_args()

    _ = analyze_problems(args.input_file, args.output_file, args.max_workers)


if __name__ == "__main__":
    main()
