from typing import Any, Dict, List

from pydantic import BaseModel


class MathematicalAnalysis(BaseModel):
    languages: List[str]
    mathematical_techniques: List[str]
    mathematical_results: List[str]

    def to_dict(self) -> Dict[str, Any]:
        return self.model_dump()

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "MathematicalAnalysis":
        return cls(**data)


class NonMathematicalAnalysis(BaseModel):
    non_mathematical_text: List[str]

    def to_dict(self) -> Dict[str, Any]:
        return self.model_dump()

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "NonMathematicalAnalysis":
        return cls(**data)


class ProblemAnalysis(BaseModel):
    languages: List[str]
    mathematical_techniques: List[str]
    mathematical_results: List[str]
    non_mathematical_text: List[str]
