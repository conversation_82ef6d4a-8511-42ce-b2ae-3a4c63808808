import argparse
import ast
from pathlib import Path

import pandas as pd
from loguru import logger
from sklearn.feature_extraction.text import ENGLISH_STOP_WORDS as SKLEARN_STOPWORDS
from sklearn.feature_extraction.text import TfidfVectorizer

from mathlete.utils.stopwords import CUSTOM_STOPWORDS

ALL_STOPWORDS = SKLEARN_STOPWORDS.union(CUSTOM_STOPWORDS)


def remove_subtags(tags):
    """
    Remove subtags including singular/plural forms
    """
    # Sort tags by length (longest first) and then lexicographically
    tags = sorted(tags, key=lambda x: (-len(x.split()), -len(x), x))
    result = []

    for tag in tags:
        # Skip this tag if it's a substring or singular/plural variant
        if not any(
            (tag in other and tag != other)
            or (tag + "s" == other)
            or (tag == other + "s")
            for other in result
        ):
            result.append(tag)

    return result


def is_meaningful(tag, min_word_length=3):
    word_parts = tag.split()

    for word in word_parts:
        if word.lower() in ALL_STOPWORDS:
            return False

        if len(word) < min_word_length and len(word_parts) > 1:
            return False

    return True


def extract_tags_tfidf(df, col, min_df=2, max_tags_per_doc=10):
    """
    Extract meaningful tags using TF-IDF weighting
    - min_df: minimum document frequency for a term to be included
    - max_tags_per_doc: maximum number of tags per document
    """
    texts = df[col].apply(lambda x: " ".join(ast.literal_eval(x)))

    tfidf_vec = TfidfVectorizer(ngram_range=(1, 2), min_df=min_df, sublinear_tf=True)

    X_tfidf = tfidf_vec.fit_transform(texts)
    vocab = tfidf_vec.get_feature_names_out()

    valid_tags = {vocab[i] for i, term in enumerate(vocab) if is_meaningful(term)}

    valid_tags = set(remove_subtags(sorted(list(valid_tags))))

    tags_per_row = []
    for i, _ in enumerate(texts):
        doc_vector = X_tfidf[i].toarray().flatten()

        doc_terms = []
        for term_idx, score in enumerate(doc_vector):
            term = vocab[term_idx]
            if score > 0 and term in valid_tags:
                doc_terms.append((term, score))

        top_tags = sorted(doc_terms, key=lambda x: -x[1])[:max_tags_per_doc]

        final_tags = remove_subtags([tag for tag, _ in top_tags])
        tags_per_row.append(final_tags)

    return tags_per_row


def load_allowed_tags(file_name="allowed_tags.txt"):
    # thanks to deepseek for the filtering after extract_tags_from_dataframe
    script_dir = Path(__file__).parent
    file_path = script_dir / file_name

    if not file_path.exists():
        raise FileNotFoundError(f"Allowed tags file not found: {file_path}")

    with open(file_path, "r") as f:
        allowed_tags = {line.strip() for line in f.readlines() if line.strip()}

    return allowed_tags


def extract_tags_from_dataframe(
    df, min_df=2, max_tags_per_doc=5, allowed_tags_file=None
):
    topic_tags = extract_tags_tfidf(df, "topic_based_queries", min_df, max_tags_per_doc)
    technique_tags = extract_tags_tfidf(
        df, "technique_based_queries", min_df, max_tags_per_doc
    )

    if not allowed_tags_file:
        logger.info("Loading allowed tags from allowed_tags.txt")
        allowed_tags = load_allowed_tags("allowed_tags.txt")
    else:
        logger.info(f"Loading allowed tags from {allowed_tags_file}")
        allowed_tags = load_allowed_tags(allowed_tags_file)

    result = {
        row["uuid"]: {
            "topic_tags": [tag for tag in topic_tags[i] if tag in allowed_tags],
            "technique_tags": [tag for tag in technique_tags[i] if tag in allowed_tags],
        }
        for i, row in df.iterrows()
    }

    return result


def analyze_tags(tags_mapping, tag_type="both"):
    """
    Analyze tags across all documents and print statistics

    Parameters:
    - tags_mapping: The dictionary with document UUIDs as keys and tag objects as values
    - tag_type: Which tags to analyze - 'topic_tags', 'technique_tags', or 'both'

    Returns:
    - A sorted list of (tag, frequency) tuples
    """
    all_tags = []

    for _, tags_obj in tags_mapping.items():
        if tag_type == "topic_tags" or tag_type == "both":
            all_tags.extend(tags_obj["topic_tags"])
        if tag_type == "technique_tags" or tag_type == "both":
            all_tags.extend(tags_obj["technique_tags"])

    tag_counts = {}
    for tag in all_tags:
        tag_counts[tag] = tag_counts.get(tag, 0) + 1

    sorted_tags = sorted(tag_counts.items(), key=lambda x: (-x[1], x[0]))

    return sorted_tags


def save_to_parquet(result, output_file):
    rows = []
    for uuid, tags in result.items():
        row = {
            "uuid": uuid,
            "topic_tags": tags["topic_tags"],
            "technique_tags": tags["technique_tags"],
        }
        rows.append(row)

    df = pd.DataFrame(rows)
    df.to_parquet(output_file)


def main():
    parser = argparse.ArgumentParser(
        description="Extract multiple solutions from mathematical problem solutions."
    )

    script_dir = Path(__file__).parent.parent
    default_input_file = (
        script_dir / "../data/synthetic_queries/synthetic_queries_20250408_175934.csv"
    )
    default_output_file = script_dir / "../data/tags.parquet"

    parser.add_argument(
        "input_file",
        type=Path,
        nargs="?",
        default=default_input_file,
        help="Input file path",
    )
    parser.add_argument(
        "output_file",
        type=Path,
        nargs="?",
        default=default_output_file,
        help="Output file path",
    )

    args = parser.parse_args()

    df = pd.read_csv(args.input_file)
    result = extract_tags_from_dataframe(df)
    save_to_parquet(result, args.output_file)

    print(f"Results have been saved to {args.output_file}")


if __name__ == "__main__":
    main()
