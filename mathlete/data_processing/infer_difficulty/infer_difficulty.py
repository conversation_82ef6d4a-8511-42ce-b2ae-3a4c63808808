import argparse
from concurrent.futures import ThreadPoolExecutor
from pathlib import Path

import pandas as pd
from loguru import logger

from mathlete.data_processing.infer_difficulty.infer_difficulty_models import (
    GradingPromptResult,
)
from mathlete.data_processing.infer_difficulty.infer_difficulty_prompts import (
    InferDifficultyPromptGenerator,
)
from mathlete.llms import llms


class MathGrader:
    def __init__(self):
        pass

    def grade_problem(self, problem: str) -> GradingPromptResult:
        logger.info(f"Grading problem: {problem}")

        prompt_generator = InferDifficultyPromptGenerator()
        prompt = prompt_generator.make_prompt_problem_difficulty(problem)
        try:
            return llms.execute_prompt(prompt=prompt, output_class=GradingPromptResult)

        except Exception as e:
            logger.error(f"Error grading problem: {str(e)}")
            raise RuntimeError(f"Problem grading failed: {str(e)}")


def process_problems(df: pd.DataFrame, max_workers: int) -> pd.DataFrame:
    grader = MathGrader()

    def grade_single_problem(row):
        try:
            result = grader.grade_problem(row["problem"])
            return pd.Series(
                {
                    "difficulty_level": result.difficulty_level,
                    "difficulty_explanation": result.explanation,
                }
            )
        except Exception as e:
            logger.error(f"Error processing problem: {e}")
            return pd.Series(
                {"difficulty_level": None, "difficulty_explanation": f"Error: {str(e)}"}
            )

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        results = list(
            executor.map(grade_single_problem, [row for _, row in df.iterrows()])
        )

    results_df = pd.DataFrame(results, index=df.index)
    return pd.concat([df[["uuid"]], results_df], axis=1)


def grade_problems(
    input_file: Path, output_file: Path, max_workers: int = 20
) -> pd.DataFrame:
    """
    Process problems from input file and save results to output file.
    """
    logger.info(f"Reading problems from {input_file}")
    df = pd.read_parquet(input_file)

    total_problems = len(df)
    logger.info(f"Found {total_problems} problems to grade")

    enhanced_df = process_problems(df, max_workers)

    successful_grades = enhanced_df["difficulty_level"].notna().sum()
    logger.info(f"Successfully graded {successful_grades} problems")

    if output_file:
        enhanced_df.to_parquet(output_file)
        logger.info(f"Saved results to {output_file}")

    return enhanced_df


def main():
    parser = argparse.ArgumentParser(
        description="Grade math problems using AoPS standards."
    )
    script_dir = Path(__file__).parent.parent
    default_input_file = script_dir / "../data/merged_data.parquet"
    default_output_file = script_dir / "../data/graded_data.parquet"

    parser.add_argument(
        "input_file",
        type=Path,
        nargs="?",
        default=default_input_file,
        help="Input file path (Parquet format)",
    )
    parser.add_argument(
        "output_file",
        type=Path,
        nargs="?",
        default=default_output_file,
        help="Output file path",
    )
    parser.add_argument(
        "--max_workers",
        type=int,
        default=50,
        help="Maximum number of worker threads (default is 30)",
    )

    args = parser.parse_args()

    _ = grade_problems(args.input_file, args.output_file, args.max_workers)


if __name__ == "__main__":
    main()
