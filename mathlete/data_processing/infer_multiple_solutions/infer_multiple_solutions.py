import argparse
import difflib
import re
from concurrent.futures import ThreadPoolExecutor
from pathlib import Path
from typing import List

import pandas as pd
from loguru import logger

from mathlete.data_processing.infer_multiple_solutions.infer_multiple_solutions_models import (
    MultipleSolutions,
    SolutionsMarkers,
)
from mathlete.data_processing.infer_multiple_solutions.infer_multiple_solutions_prompts import (
    InferMultipleSolutionsPromptGenerator,
)
from mathlete.llms import llms


class SolutionVerifier:
    @staticmethod
    def normalize_text(text: str) -> str:
        text = text.strip()
        text = re.sub(r"\s+", " ", text)
        text = re.sub(r"\n+", " ", text)

        return text

    @staticmethod
    def verify_solution_extraction(
        original: str, extracted: List[str], threshold: float = 0.9
    ) -> bool:
        normalized_original = SolutionVerifier.normalize_text(original)
        normalized_extracted = SolutionVerifier.normalize_text(" ".join(extracted))
        logger.debug(f"Original solution: {normalized_original}")
        logger.debug(f"Extracted solution: {normalized_extracted}")

        similarity = difflib.SequenceMatcher(
            None, normalized_original, normalized_extracted
        ).ratio()

        return similarity > threshold


class InferMultipleSolutions:
    def __init__(self):
        pass

    def extract_solutions_by_markers(
        self, solution: str, markers: List[str]
    ) -> List[str]:
        """
        Extract solutions using regex based on detected markers.
        """
        if not markers:
            return []

        # Improve regex for 'OR' and enforce solution markers with colons or newlines
        marker_patterns = []
        for marker in markers:
            if marker.strip().upper() == "OR":
                marker_patterns.append(
                    r"(?:\nOR\n|\n- OR -\n)"
                )  # Ensure 'OR' is standalone
            else:
                marker_patterns.append(
                    r"\b" + re.escape(marker) + r"(?=\s|:|\n|$)"
                )  # Match full marker

        marker_regex = "|".join(marker_patterns)

        # Find all marker positions
        marker_matches = list(re.finditer(marker_regex, solution))

        if not marker_matches:
            return []

        solutions = []
        first_match_start = marker_matches[0].start()

        # Check for content before first marker
        if first_match_start > 0:
            first_solution = solution[:first_match_start].strip()
            if first_solution:
                solutions.append(first_solution)

        # Extract solutions based on markers
        for i in range(len(marker_matches)):
            current_marker = marker_matches[i]

            if i == len(marker_matches) - 1:
                solution_text = solution[current_marker.start() :].strip()  # noqa E203
            else:
                next_marker_start = marker_matches[i + 1].start()
                solution_text = solution[
                    current_marker.start() : next_marker_start  # noqa E203
                ].strip()

            if solution_text:
                solutions.append(solution_text)

        return solutions

    def infer_multiple_solutions(
        self, solution: str, use_llm: bool = False, custom_markers: list[str] = None
    ) -> MultipleSolutions:
        """
        Infer multiple solutions from a given solution text, either using LLMs or a predefined list of markers.

        Args:
            solution (str): The solution text to analyze for multiple solutions.
            use_llm (bool): Whether to use LLMs for extracting solution markers. Defaults to False.
            custom_markers (list[str], optional): A predefined list of solution markers. If provided, LLMs are skipped.

        Returns:
            MultipleSolutions: Object containing the extracted multiple solutions.

        Raises:
            RuntimeError: If multiple solutions inference fails.
        """
        logger.info("Inferring multiple solutions from solution text")

        default_markers = [
            "Alternate solution",
            "Alternatively",
            "Altenative",
            "OR",
            "Solution 1",
            "Solution 2",
            "Solution 3",
            "Solution 4",
            "Solution 5",
            "Second solution",
            "Third solution",
            "Proof 1",
            "Proof 2",
            "Solution2",
            "HERE is another type of solution",
            "Approach by[",
        ]

        markers = custom_markers if custom_markers else default_markers

        if use_llm:
            try:
                marker_prompt_generator = InferMultipleSolutionsPromptGenerator()
                marker_prompt = marker_prompt_generator.make_prompt_solution_markers(
                    solution
                )

                marker_result = llms.execute_prompt(
                    prompt=marker_prompt, output_class=SolutionsMarkers
                )

                if not marker_result.solution_markers:
                    logger.info(
                        "No solution markers found. Falling back to original solution."
                    )
                    return MultipleSolutions(multiple_solutions=[])

                markers = marker_result.solution_markers
            except Exception as e:
                logger.error(f"Error inferring multiple solutions: {str(e)}")
                raise RuntimeError(f"Multiple solutions inference failed: {str(e)}")

        extracted_solutions = self.extract_solutions_by_markers(solution, markers)

        if not extracted_solutions:
            logger.info("No solutions extracted. Falling back to original solution.")
            return MultipleSolutions(multiple_solutions=[])

        if not SolutionVerifier.verify_solution_extraction(
            solution, extracted_solutions
        ):
            logger.error(
                f"Solution extraction verification failed. Falling back to original solution.\nOriginal: {solution}\nExtracted: {extracted_solutions}"
            )
            return MultipleSolutions(multiple_solutions=[])

        logger.info(f"Multiple solutions inferred: {extracted_solutions}")
        return MultipleSolutions(multiple_solutions=extracted_solutions)


def process_multiple_solutions(
    df: pd.DataFrame, max_workers: int, use_llm: bool
) -> pd.DataFrame:
    """
    Process multiple solutions for a DataFrame of problems.

    Args:
        df (pd.DataFrame): DataFrame containing solutions to process.
        max_workers (int): Maximum number of worker threads to use.

    Returns:
        pd.DataFrame: DataFrame with extracted multiple solutions.
    """
    multiple_solutions_inferrer = InferMultipleSolutions()

    def infer_multiple_solutions_for_solution(row):
        try:
            result = multiple_solutions_inferrer.infer_multiple_solutions(
                solution=row["informal_solution"], use_llm=use_llm
            )
            return pd.Series({"multiple_solutions": result.multiple_solutions})
        except Exception as e:
            logger.error(f"Error processing solution: {e}")
            return pd.Series({"multiple_solutions": None})

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        results = list(
            executor.map(
                infer_multiple_solutions_for_solution, [row for _, row in df.iterrows()]
            )
        )
    # Maybe we should remove rows with empty multiple_solutions
    results_df = pd.DataFrame(results, columns=["multiple_solutions"], index=df.index)
    return pd.concat([df[["uuid"]], results_df], axis=1)


def extract_multiple_solutions(
    input_file: Path, output_file: Path, max_workers: int = 50, use_llm: bool = False
) -> pd.DataFrame:
    """
    Process solutions from input file and save results with multiple solutions to output file.

    Args:
        input_file (Path): Path to input Parquet file containing solutions.
        output_file (Path): Path to output Parquet file for multiple solutions.
        max_workers (int, optional): Maximum number of worker threads. Defaults to 20.

    Returns:
        pd.DataFrame: DataFrame with extracted multiple solutions.
    """
    logger.info(f"Reading solutions from {input_file}")
    df = pd.read_parquet(input_file)
    total_solutions = len(df)
    logger.info(f"Found {total_solutions} solutions to process")

    enhanced_df = process_multiple_solutions(df, max_workers, use_llm)
    if output_file:
        enhanced_df.to_parquet(output_file)
        logger.info(f"Saved results to {output_file}")
    return enhanced_df


def main():
    parser = argparse.ArgumentParser(
        description="Extract multiple solutions from mathematical problem solutions."
    )

    script_dir = Path(__file__).parent.parent
    default_input_file = script_dir / "../data/merged_data.parquet"
    default_output_file = script_dir / "../data/multiple_solutions_data.parquet"

    parser.add_argument(
        "input_file",
        type=Path,
        nargs="?",
        default=default_input_file,
        help="Input file path (Parquet format)",
    )
    parser.add_argument(
        "output_file",
        type=Path,
        nargs="?",
        default=default_output_file,
        help="Output file path",
    )
    parser.add_argument(
        "--max_workers",
        type=int,
        default=50,
        help="Maximum number of worker threads (default is 20)",
    )

    parser.add_argument(
        "--use_llm",
        action="store_true",
        help="Flag to determine whether to use LLMs for extracting solution markers",
    )

    args = parser.parse_args()
    _ = extract_multiple_solutions(
        args.input_file, args.output_file, args.max_workers, args.use_llm
    )

    logger.info("Processing completed")


if __name__ == "__main__":
    main()
