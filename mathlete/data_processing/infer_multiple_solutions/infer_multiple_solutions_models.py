from typing import Any, Dict, List

from pydantic import BaseModel


class SolutionsMarkers(BaseModel):
    solution_markers: List[str]

    def to_dict(self) -> Dict[str, Any]:
        return self.model_dump()

    @classmethod
    def from_dict(cls, data: Dict[str, Any]):
        return cls(**data)


class MultipleSolutions(BaseModel):
    multiple_solutions: List[str]

    def to_dict(self) -> Dict[str, Any]:
        return self.model_dump()

    @classmethod
    def from_dict(cls, data: Dict[str, Any]):
        return cls(**data)
