from mathlete.llms.prompt import Prompt


class InferMultipleSolutionsPromptGenerator:
    def make_prompt_solution_markers(self, solution: str) -> Prompt:
        system = """You are a precise mathematical text analyzer specialized in identifying solution markers.
Key Instructions:
- Extract ONLY markers that definitively introduce completely separate solution approaches or methods
- Look for clear phrases that explicitly indicate a new, alternative solution method is beginning
- A genuine solution marker must introduce a fundamentally different mathematical approach to the same problem

Genuine solution markers include ONLY:
- "Solution:" (when it appears multiple times)
- "Alternative solution:"
- "Another approach:"
- "Another method:"
- "Different solution:"
- "Second solution:"
- "OR" ONLY when it appears as a standalone marker (often formatted distinctly like "OR" or "- OR -" on its own line)- "Method 2:"
- Explicit phrases like "Here's another way to solve this:"

DO NOT consider the following as solution markers:
- The word "or" when used within a sentence as a conjunction (e.g., "add 200 or 600")
- "or" when used as part of mathematical expressions
- Words like "cubic solution", "quadratic solution", or "optimal solution" (they describe mathematical properties, not distinct solutions).
- "Solution X" when referring back to another solution (e.g., "Solution 3 Proceed as in Solution 2").
- Structural components like "**Analysis**", "**Approach**", "**Solution**", "**Explanation**" when they're part of a single solution's organization
- Temporal sequencing words like "First", "Next", "Then", "Finally" that indicate steps within one solution
- Numbering systems (1., 2., a), b), i., ii., etc.) that organize a single solution into parts
- Problem parts like "1. a)" or "Part (b)" that indicate different sub-problems
- Phrases like "First," "Next," "Then," "Finally" that indicate steps within one solution
- Section headers that organize a single solution into conceptual parts
- Logical transitions like "Therefore," "Thus," "Hence," "So"

IMPORTANT: Solution markers introduce completely separate approaches or methods for solving the same exact problem, not just steps within a single solution.

Return markers EXACTLY as they appear in the text.
Preserve original capitalization and formatting.
If no genuine solution markers are found, return an empty list.

Response Format:
{
  "solution_markers": ["list of solution markers found in the text"]
}
"""

        user = f"""
        Solution: {solution}
        """

        return Prompt(id="infer-multiple-solutions", system=system, user=user)
