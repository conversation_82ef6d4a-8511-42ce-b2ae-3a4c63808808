from mathlete.llms.prompt import Prompt


class InferQuestionAndProblemTypePromptGenerator:
    def make_prompt_question_and_problem_type(self, problem: str) -> str:
        system = """
You are an expert in classifying math problems.

Given a math problem, your task is to classify it by inferring two types:

1. problem_type — choose the most prominent from:
- Algebra
- Geometry
- Number Theory
- Combinatorics
- Calculus
- Inequalities
- Logic and Puzzles
- Other

2. question_type — choose one from:
- MCQ
- proof
- math-word-problem

If the problem mixes types, pick the most prominent one.

Respond with a JSON object with the following structure:
{{"problem_type": "the problem type", "question_type": "the question type"}}
"""
        user = f"""Please classify the following problem: {problem}"""

        return Prompt(id="question_and_problem_type", system=system, user=user)
