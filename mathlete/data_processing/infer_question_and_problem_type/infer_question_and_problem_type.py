import argparse
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor
from pathlib import Path

import pandas as pd
from loguru import logger

from mathlete.data_processing.infer_question_and_problem_type.infer_question_and_problem_type_models import (
    QuestionAndProblemType,
)
from mathlete.data_processing.infer_question_and_problem_type.infer_question_and_problem_type_prompts import (
    InferQuestionAndProblemTypePromptGenerator,
)
from mathlete.llms import llms


class InferProblemAndQuestionType:
    def __init__(self):
        pass

    def generate_classification(self, problem: str) -> QuestionAndProblemType:
        logger.info(f"Classifying problem: {problem}")
        prompt_generator = InferQuestionAndProblemTypePromptGenerator()
        prompt = prompt_generator.make_prompt_question_and_problem_type(problem)
        try:
            return llms.execute_prompt(
                prompt=prompt, output_class=QuestionAndProblemType
            )
        except Exception as e:
            logger.error(f"Error generating problem name: {str(e)}")
            raise RuntimeError(f"Problem name generation failed: {str(e)}")


def process_problems(df: pd.DataFrame, max_workers: int) -> pd.DataFrame:
    classifier = InferProblemAndQuestionType()

    def classify_problem(row):
        try:
            result = classifier.generate_classification(row["problem"])
            return pd.Series(
                {
                    "problem_type": result.problem_type,
                    "question_type": result.question_type,
                }
            )
        except Exception as e:
            logger.error(
                f"Error processing problem UUID {row.get('uuid', 'unknown')}: {e}"
            )
            return pd.Series({"problem_type": None, "question_type": None})

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        results = list(
            executor.map(classify_problem, [row for _, row in df.iterrows()])
        )

    results_df = pd.DataFrame(
        results, columns=["problem_type", "question_type"], index=df.index
    )
    return pd.concat([df[["uuid"]], results_df], axis=1)


def generate_classifications(
    input_file: Path, output_file: Path, max_workers: int = 20
) -> pd.DataFrame:
    logger.info(f"Reading problems from {input_file}")
    df = pd.read_parquet(input_file)

    total_problems = len(df)
    logger.info(f"Found {total_problems} problems to process")

    processed_df = process_problems(df, max_workers)
    if output_file:
        processed_df.to_parquet(output_file)
        logger.info(f"Saved results to {output_file}")
    return processed_df


def main():
    parser = argparse.ArgumentParser(
        description="Classify math problems by problem_type and question_type."
    )

    script_dir = Path(__file__).parent.parent
    default_input_file = script_dir / "../data/imo_data.parquet"
    default_output_file = script_dir / "../data/imo_data_classified.parquet"

    parser.add_argument(
        "input_file",
        type=Path,
        nargs="?",
        default=default_input_file,
        help="Input file path (Parquet format)",
    )
    parser.add_argument(
        "output_file",
        type=Path,
        nargs="?",
        default=default_output_file,
        help="Output file path",
    )
    parser.add_argument(
        "--max_workers",
        type=int,
        default=20,
        help="Maximum number of worker threads (default is 20)",
    )

    args = parser.parse_args()
    _ = generate_classifications(args.input_file, args.output_file, args.max_workers)
    logger.info("Processing completed")


if __name__ == "__main__":
    main()
