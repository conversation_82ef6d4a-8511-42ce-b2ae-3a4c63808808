import argparse
from pathlib import Path
from typing import Optional, Union

import pandas as pd
from datasets import load_dataset
from loguru import logger

from mathlete.data_processing.dataset_merger import BaseDataProcessor
from mathlete.data_processing.infer_cold_start_hints.infer_cold_start_hints import (
    generate_hints,
)
from mathlete.data_processing.infer_difficulty.infer_difficulty import grade_problems
from mathlete.data_processing.infer_metadata.infer_metadata import analyze_problems
from mathlete.data_processing.infer_multiple_solutions.infer_multiple_solutions import (
    extract_multiple_solutions,
)
from mathlete.data_processing.infer_problem_name.infer_problem_name import (
    generate_problem_names,
)


def run_pipeline(
    merged_data_file,
    metadata_data_file,
    difficulty_data_file,
    hints_data_file,
    multiple_solutions_data_file,
    problem_names_data_file,
    max_workers,
    final_data_file: str = None,
    filter_valid: bool = False,
    filter_english: bool = True,
    input_merged_df: Optional[pd.DataFrame] = None,
):
    output_dir = Path(merged_data_file).parent
    output_dir.mkdir(parents=True, exist_ok=True)

    if input_merged_df is not None:
        logger.info("Using provided dataframe directly...")
        merged_df = input_merged_df
    else:
        logger.info("Step 1: Merging datasets...")

        math_numina_lean_path = "AI-MO/numina-math-lean4"
        math_numina_lean = load_dataset(math_numina_lean_path)["train"]
        df_math_numina = math_numina_lean.to_pandas()

        processor = BaseDataProcessor()
        merged_df, _, _ = processor.merge_datasets(
            df_math_numina, output_path=merged_data_file
        )

    logger.info("Step 2: Inferring metadata...")
    metadata_df = analyze_problems(
        input_file=merged_data_file,
        output_file=metadata_data_file,
        max_workers=max_workers,
    )

    logger.info("Step 3: Inferring difficulty...")
    difficulty_df = grade_problems(
        input_file=merged_data_file,
        output_file=difficulty_data_file,
        max_workers=max_workers,
    )

    logger.info("Step 4: Inferring hints...")
    hints_df = generate_hints(
        input_file=merged_data_file,
        output_file=hints_data_file,
        max_workers=max_workers,
    )

    logger.info("Step 5: Inferring multiple solutions...")
    multiple_solutions_df = extract_multiple_solutions(
        input_file=merged_data_file,
        output_file=multiple_solutions_data_file,
        max_workers=max_workers,
    )

    logger.info("Step 6: Inferring problem names...")
    problem_names_df = generate_problem_names(
        input_file=merged_data_file,
        output_file=problem_names_data_file,
        max_workers=max_workers,
    )

    final_df = None
    if final_data_file:
        logger.info("Step 7: Merging datasets...")
        dataframes = {
            "merged_df": merged_df,
            "metadata_df": metadata_df,
            "difficulty_df": difficulty_df,
            "hints_df": hints_df,
            "multiple_solutions_df": multiple_solutions_df,
            "problem_names_df": problem_names_df,
        }

        for name, df in dataframes.items():
            if not df["uuid"].is_unique:
                raise ValueError(f"{name} has duplicate uuid values!")

        uuid_sets = {name: set(df["uuid"]) for name, df in dataframes.items()}
        first_set = uuid_sets["merged_df"]

        for name, uuid_set in uuid_sets.items():
            if uuid_set != first_set:
                raise ValueError(f"UUID sets in {name} do not match other dataframes!")

        final_df = (
            merged_df.merge(metadata_df, on="uuid")
            .merge(difficulty_df, on="uuid")
            .merge(hints_df, on="uuid")
            .merge(multiple_solutions_df, on="uuid")
            .merge(problem_names_df, on="uuid")
        )

        original_count = len(final_df)

        if filter_valid:
            logger.info("Filtering out problems without Lean4 solution...")
            final_df = final_df[final_df["is_valid_no_sorry"]]
            logger.info(f"Filtered valid problems: {original_count} → {len(final_df)}")
            original_count = len(final_df)

        if filter_english:
            logger.info("Filtering for English language problems only...")
            final_df = final_df[final_df["languages"].astype(str) == "['English']"]
            logger.info(
                f"Filtered English problems: {original_count} → {len(final_df)}"
            )

        final_df.to_parquet(final_data_file)
        logger.info(f"Final dataset saved to {final_data_file}")

        return {
            "merged_df": merged_df,
            "metadata_df": metadata_df,
            "difficulty_df": difficulty_df,
            "hints_df": hints_df,
            "multiple_solutions_df": multiple_solutions_df,
            "problem_names_df": problem_names_df,
            "final_df": final_df if final_data_file else None,
        }


def load_merged_df(input_file: Union[str, Path]) -> pd.DataFrame:
    input_file = Path(input_file)
    if input_file.suffix == ".parquet":
        return pd.read_parquet(input_file)
    elif input_file.suffix == ".csv":
        return pd.read_csv(input_file)
    else:
        raise ValueError(f"Unsupported file format: {input_file.suffix}")


def parse_args():
    parser = argparse.ArgumentParser(description="Run the data processing pipeline")

    base_data_path = Path(__file__).resolve().parent.parent / "data"
    default_paths = {
        "merged_data_file": base_data_path / "merged_data.parquet",
        "metadata_data_file": base_data_path / "enhanced_data.parquet",
        "difficulty_data_file": base_data_path / "graded_data.parquet",
        "hints_data_file": base_data_path / "hints_data.parquet",
        "multiple_solutions_data_file": base_data_path
        / "multiple_solutions_data.parquet",
        "problem_names_data_file": base_data_path / "problem_names_data.parquet",
        "final_data_file": base_data_path / "final_data.parquet",
    }

    for arg_name, default_path in default_paths.items():
        help_text = f"Path to save the {arg_name.replace('_data_file', '').replace('_', ' ')} dataset (default: '{default_path.relative_to(base_data_path.parent)}')"
        parser.add_argument(
            f"--{arg_name}",
            type=Path,
            default=None,
            help=help_text,
        )

    parser.add_argument(
        "--input_file",
        type=str,
        default=None,
        help="Path to an existing merged dataframe file to use as input (optional)",
    )

    parser.add_argument(
        "--max_workers",
        type=int,
        default=50,
        help="Number of workers for processing (default: 50)",
    )

    parser.add_argument(
        "--filter_valid",
        type=str,
        choices=["yes", "no"],
        default=None,
        help="Filter out problems without Lean4 solution (yes/no)",
    )
    parser.add_argument(
        "--filter_english",
        type=str,
        choices=["yes", "no"],
        default=None,
        help="Filter for English language problems only (yes/no)",
    )

    args = parser.parse_args()
    only_merged_and_final_provided = (
        args.merged_data_file is not None
        and args.final_data_file is not None
        and all(
            getattr(args, key) is None
            for key in default_paths
            if key not in ("merged_data_file", "final_data_file")
        )
    )

    if only_merged_and_final_provided:
        pass
    else:
        for key, default_path in default_paths.items():
            if getattr(args, key) is None:
                setattr(args, key, default_path)

    return args


if __name__ == "__main__":
    args = parse_args()

    filter_valid = False if args.filter_valid == "no" else True
    filter_english = False if args.filter_english == "no" else True

    input_merged_df = None
    if args.merged_data_file.exists():
        logger.info(f"Loading dataframe from {args.merged_data_file}...")
        input_merged_df = load_merged_df(args.merged_data_file)

    run_pipeline(
        merged_data_file=args.merged_data_file,
        metadata_data_file=args.metadata_data_file,
        difficulty_data_file=args.difficulty_data_file,
        hints_data_file=args.hints_data_file,
        multiple_solutions_data_file=args.multiple_solutions_data_file,
        problem_names_data_file=args.problem_names_data_file,
        final_data_file=args.final_data_file,
        max_workers=args.max_workers,
        filter_valid=filter_valid,
        filter_english=filter_english,
        input_merged_df=input_merged_df,
    )

"""
Example usage:

# Default run to build pipeline from scratch:
python data_processing/run_pipeline.py

# Provide merged_data_file and final_data_file, skip filtering:
python data_processing/run_pipeline.py --merged_data_file data/imo_data.parquet --final_data_file data/final_imo_data.parquet

# Provide all dataset paths explicitly (other args optional):
python data_processing/run_pipeline.py \
    --merged_data_file data/imo_data.parquet \
    --metadata_data_file data/enhanced_data.parquet \
    --difficulty_data_file data/graded_data.parquet \
    --hints_data_file data/hints_data.parquet \
    --multiple_solutions_data_file data/multiple_solutions_data.parquet \
    --problem_names_data_file data/problem_names_data.parquet \
    --final_data_file data/final_imo_data.parquet \
    --max_workers 20 \
    --filter_valid yes \
    --filter_english no
"""
