from typing import Dict, List

from pydantic import BaseModel


class Vertex(BaseModel):
    id: int
    x: float
    y: float
    problem_id: str
    cluster: str
    cluster_id: str
    neighbors: List[str] = []
    similarity_scores: Dict[str, float] = {}


class Edge(BaseModel):
    source: Vertex
    target: Vertex
    weight: float
    similarity: float

    model_config = {"arbitrary_types_allowed": True}
