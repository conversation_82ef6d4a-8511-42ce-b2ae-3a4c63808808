import os

import igraph as ig
from loguru import logger


class GraphService:
    def __init__(self, graph_path: str = "similarity_graph.graphml"):
        """
        A service to manage graph persistence and loading.
        """
        self.graph_path = graph_path

    def save_graph(self, graph: ig.Graph) -> None:
        os.makedirs(os.path.dirname(os.path.abspath(self.graph_path)), exist_ok=True)
        graph.write_graphml(self.graph_path)
        logger.info(f"Saved graph to {self.graph_path}")

    def load_graph(self) -> ig.Graph:
        if not os.path.exists(self.graph_path):
            raise FileNotFoundError(f"Graph file not found at {self.graph_path}")

        graph = ig.Graph.Read_GraphML(self.graph_path)
        logger.info(f"Loaded graph from {self.graph_path}")
        return graph

    def graph_exists(self) -> bool:
        return os.path.exists(self.graph_path)
