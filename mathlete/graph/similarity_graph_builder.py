from typing import List, <PERSON><PERSON>

import igraph as ig
import numpy as np
from loguru import logger

from mathlete.clustering.math_clusterer import Math<PERSON>luster<PERSON>
from mathlete.graph.graph_models import Edge, Vertex
from mathlete.utils.problem_manager import ProblemManager
from mathlete.vectors.embedding_reducer import EmbeddingReducer
from mathlete.vectors.similarity_matrix_builder import SimilarityMatrixBuilder


class SimilarityGraphBuilder:
    def __init__(
        self,
        problem_manager: ProblemManager,
        similarity_matrix_builder: SimilarityMatrixBuilder = None,
        similarity_threshold: float = 0.7,
        weight_threshold: float = 0.7,
    ) -> None:
        self.problem_manager = problem_manager
        self.similarity_matrix_builder = (
            similarity_matrix_builder
            if similarity_matrix_builder is not None
            else SimilarityMatrixBuilder(problem_manager)
        )
        self.problem_ids = self.problem_manager.get_problem_ids()
        self.similarity_threshold = similarity_threshold
        self.weight_threshold = weight_threshold
        self._similarity_graph = None

    def get_similarity_matrix(self) -> np.ndarray:
        return self.similarity_matrix_builder.get_similarity_matrix()

    # TODO how to deal with graph with/without difficulty
    def get_similarity_graph(self, use_difficulty=True) -> ig.Graph:
        if self._similarity_graph is None:
            matrix = self.get_similarity_matrix()
            self._similarity_graph = self.build_similarity_graph(
                matrix, use_difficulty=use_difficulty
            )
        return self._similarity_graph

    def _calculate_progression_score(self, diff_gap: float) -> float:
        IDEAL_STEP = 1
        SAME_PENALTY = 0.8
        MIN_SCORE = 0.1
        STD_DEV = 0.9

        if diff_gap > 0:
            return np.exp(-((diff_gap - IDEAL_STEP) ** 2) / (2 * STD_DEV**2))
        elif diff_gap == 0:
            return SAME_PENALTY
        else:
            return MIN_SCORE + (SAME_PENALTY - MIN_SCORE) * np.exp(0.5 * diff_gap)

    def _compute_weight(self, similarity: float, diff_gap: float) -> float:
        progression_score = self._calculate_progression_score(diff_gap)
        return 1 - (similarity * progression_score)

    def build_similarity_graph(
        self, similarity_matrix, use_difficulty: bool = False
    ) -> ig.Graph:
        """Build similarity or difficulty-based distance graph"""
        n = len(self.problem_ids)
        G = ig.Graph(n=n, directed=True)
        G.vs["name"] = self.problem_ids

        edges = []
        weights = []

        for i in range(n):
            for j in range(n):
                if i == j:
                    continue

                similarity = similarity_matrix[i, j]
                if similarity > self.similarity_threshold:
                    if use_difficulty:
                        diff_gap = self.problem_manager.get_problem_difficulty(
                            self.problem_ids[j]
                        ) - self.problem_manager.get_problem_difficulty(
                            self.problem_ids[i]
                        )
                        weight = self._compute_weight(similarity, diff_gap)
                    else:
                        weight = similarity

                    edges.append((i, j))
                    weights.append(weight)

        G.add_edges(edges)
        G.es["weight"] = weights

        return G

    def build_visualization_graph(self) -> Tuple[List[Vertex], List[Edge]]:
        vertices = []
        vertex_map = {}
        vertex_counter = 0
        similarity_matrix = self.get_similarity_matrix()
        reducer = EmbeddingReducer()

        umap_problems = reducer.reduce_with_umap(
            self.problem_manager.problems,
            umap_components=2,
            similarity_matrix=similarity_matrix,
        )
        reduced_problems_manager = ProblemManager(umap_problems)

        logger.info("Clustering...")
        self.math_clusterer = MathClusterer()
        self.math_clusterer.create_clusters(reduced_problems_manager)

        logger.info("Post-processing clustering noise points...")
        self.math_clusterer.post_process_noise_points(
            problem_manager=reduced_problems_manager,
            similarity_matrix=similarity_matrix,
        )

        for problem in umap_problems:
            problem_id = problem.uuid
            embedding = problem.embedding
            if embedding is None or len(embedding) != 2:
                logger.debug(f"Problem {problem_id} has no valid embedding")
                continue

            cluster_id = self.math_clusterer.get_cluster_for_problem(problem_id)
            cluster_label = self.math_clusterer.get_cluster_label(
                cluster_id=cluster_id, problem_manager=reduced_problems_manager
            )

            try:
                vertex = Vertex(
                    id=vertex_counter,
                    x=float(embedding[0]),
                    y=float(embedding[1]),
                    problem_id=problem_id,
                    cluster=cluster_label,
                    cluster_id=str(cluster_id) if cluster_id is not None else "",
                    neighbors=[],
                    similarity_scores={},
                )
                vertices.append(vertex)
                vertex_map[problem_id] = vertex
                vertex_counter += 1
            except Exception as e:
                logger.error(f"Error processing problem {problem_id}: {e}")

        edges = []
        added_edges = set()

        for vertex in vertices:
            problem_id = vertex.problem_id
            problem_idx = self.problem_manager.get_problem_index(problem_id)
            if problem_idx is None:
                continue

            for i, other_id in enumerate(self.problem_ids):
                if other_id == problem_id:
                    continue

                score = similarity_matrix[problem_idx][i]
                if score <= self.similarity_threshold:
                    continue

                vertex.neighbors.append(other_id)
                vertex.similarity_scores[other_id] = float(score)

                edge_key = (min(problem_id, other_id), max(problem_id, other_id))
                if edge_key not in added_edges:
                    diff_gap = self.problem_manager.get_problem_difficulty(
                        other_id
                    ) - self.problem_manager.get_problem_difficulty(problem_id)
                    weight = self._compute_weight(score, diff_gap)

                    other_vertex = vertex_map.get(other_id)
                    if other_vertex:
                        edges.append(
                            Edge(
                                source=vertex.model_dump(),
                                target=other_vertex.model_dump(),
                                weight=float(weight),
                                similarity=float(score),
                            )
                        )
                        added_edges.add(edge_key)

        return vertices, edges
