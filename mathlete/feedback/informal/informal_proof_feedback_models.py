from typing import Any, Dict, List

from pydantic import BaseModel


class MathematicalError(BaseModel):
    severity: str
    location_in_proof: str
    error_type: str
    explanation: str
    suggested_hint: str

    def to_dict(self) -> Dict[str, Any]:
        return {
            "severity": self.severity,
            "location_in_proof": self.location_in_proof,
            "error_type": self.error_type,
            "explanation": self.explanation,
            "suggested_hint": self.suggested_hint,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "MathematicalError":
        return cls(
            severity=data.get("severity", ""),
            location_in_proof=data.get("location_in_proof", ""),
            error_type=data.get("error_type", ""),
            explanation=data.get("explanation", ""),
            suggested_hint=data.get("suggested_hint", ""),
        )


class MathematicalAnalysisResult(BaseModel):
    mathematical_errors: List[MathematicalError]
    summary: str

    def to_dict(self) -> Dict[str, Any]:
        return {
            "mathematical_errors": [
                error.to_dict() for error in self.mathematical_errors
            ],
            "summary": self.summary,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "MathematicalAnalysisResult":
        return cls(
            mathematical_errors=[
                MathematicalError.from_dict(error)
                for error in data.get("mathematical_errors", [])
            ],
            summary=data.get("summary", ""),
        )
