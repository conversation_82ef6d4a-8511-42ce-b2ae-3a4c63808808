from loguru import logger

from mathlete.feedback.informal.informal_proof_feedback_models import (
    MathematicalAnalysisResult,
)
from mathlete.feedback.informal.informal_proof_feedback_prompts import (
    InformalProofFeedbackPromptGenerator,
    ResultRefinementPromptGenerator,
)
from mathlete.llms import llms
from mathlete.utils.constants import UserProofAttempt


class InformalProofAnalyzer:
    def __init__(self):
        pass

    def analyze_informal_proof(
        self, attempt: UserProofAttempt
    ) -> MathematicalAnalysisResult:
        """Analyze only the mathematical reasoning of the informal proof"""
        proof_feedback_prompt_generator = InformalProofFeedbackPromptGenerator()
        proof_feedback_prompt = (
            proof_feedback_prompt_generator.make_prompt_informal_proof_feedback(attempt)
        )

        try:
            proof_analysis = llms.execute_prompt(
                prompt=proof_feedback_prompt,
                output_class=MathematicalAnalysisResult,
                model="together",
            )

            if len(proof_analysis.mathematical_errors) > 0:
                logger.info("Refining Extraction of Proof Analysis...")
                refined_result_prompt_generator = ResultRefinementPromptGenerator()
                refined_result_prompt = (
                    refined_result_prompt_generator.make_prompt_result_refinement(
                        attempt, proof_analysis
                    )
                )

                return llms.execute_prompt(
                    prompt=refined_result_prompt,
                    output_class=MathematicalAnalysisResult,
                )

            else:
                return proof_analysis

        except Exception as e:
            logger.error(f"Error analyzing mathematical proof: {str(e)}")
            raise RuntimeError(f"Mathematical proof analysis failed: {str(e)}")
