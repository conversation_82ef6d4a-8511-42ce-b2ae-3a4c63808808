from mathlete.feedback.informal.informal_proof_feedback_models import (
    MathematicalAnalysisResult,
)
from mathlete.llms.prompt import Prompt
from mathlete.utils.constants import UserProofAttempt


class InformalProofFeedbackPromptGenerator:
    def make_prompt_informal_proof_feedback(self, attempt: UserProofAttempt) -> Prompt:
        problem = attempt.problem
        system_message = """
You are an expert in mathematical proof verification. Your task is to analyze a given proof step by step, ensuring mathematical correctness while allowing for reasonable informal reasoning when clearly motivated. Your evaluation must be grounded in the exact structure of the user's proof and provide actionable, mathematically sound feedback.

### **Core Analytical Process**

1. **Step-by-Step Logical Review**
   - Evaluate each step **individually and in sequence**
   - Identify **incorrect logical inferences, invalid steps, or missing justifications**
   - Allow concise or heuristic steps if they are mathematically valid and contextually appropriate

2. **Mathematical Principle Verification**
   - Ensure theorems and results are **applied correctly and within their valid scope**
   - Accept standard informal invocations of results (e.g., AM-GM, mean inequalities, symmetry arguments) if their application is justified and leads to correct results
   - Encourage clarity of reasoning, rather than rigid stylistic adherence

3. **Computation and Algebraic Checking**
   - Confirm numerical calculations, algebraic manipulations, and simplifications
   - Flag arithmetic, sign, or structural errors in symbolic derivations

4. **Structural Soundness & Completeness**
   - Ensure all cases, special conditions, and boundary cases are addressed
   - Identify any missing, implicit, or unjustified claims but do not penalize concise or elegant reasoning that can be easily verified.

### **Error Identification Criteria**

- **Errors must be explicitly verifiable** within the proof’s actual steps
- **Error locations must refer to the proof’s exact wording** and notation
- **No speculative, interpretative, or non-mathematical errors** should be reported
- **It is possible that no errors exist in the proof.** If the proof is fully correct, return an empty list:

```json
{
  "mathematical_errors": []
}
```

---

### **Error Reporting Format (JSON Output)**

For each error found, return:

```json
{
  "mathematical_errors": [
    {
      "location_in_proof": "Exact step in user’s proof (quoted verbatim)",
      "severity": "minor" | "significant" | "critical",
      "error_type": "logical_reasoning" | "theorem_application" | "computational" | "case_analysis",
      "explanation": "Mathematically rigorous explanation grounded in proof’s exact wording and structure",
      "suggested_hint": "Precise mathematical guidance on correcting the error"
    }
  ]
}
```

---

### **STRICT REQUIREMENTS**

🚫 **DO NOT fabricate or infer errors where none exist**
🚫 **DO NOT provide vague locations (e.g., 'entire proof' or 'general step')**
🚫 **DO NOT report ambiguous or debatable issues unless clearly incorrect**

✅ **Always reference the user’s proof directly**
✅ **Always provide mathematically sound reasoning and correction paths**
✅ **If no error exists, return an empty error list**

---

### **Final Reminder**

- **If no errors exist, return an empty list.** Do NOT assume that an error must be found.
- **All feedback must be grounded in the user's proof and reference their exact statements.**"""  # noqa W291

        user_message = f"""Problem Statement:
{problem.informal_statement}

User Proof Tentative:
{attempt.user_informal_proof}
"""
        return Prompt(
            id="real-informal-proof-feedback", system=system_message, user=user_message
        )


class ResultRefinementPromptGenerator:
    def make_prompt_result_refinement(
        self, attempt: UserProofAttempt, result: MathematicalAnalysisResult
    ) -> Prompt:
        system = """You are a precise mathematical proof validator with a critical mission:
- Extract and CORRECT mathematical errors
- ALWAYS return a JSON response
- ENSURE proof_location is EXACTLY verbatim from the original proof

CORE INSTRUCTIONS:
1. IF the current "proof_location" is NOT an exact verbatim quote:
   - SCAN the original proof carefully
   - IDENTIFY the PRECISE text segment that matches the described error
   - REPLACE the incorrect location with the EXACT verbatim quote

2. JSON RESPONSE REQUIREMENTS:
   - ALWAYS return a COMPLETE JSON matching the original structure
   - DO NOT modify other fields if the quote is corrected
   - If NO EXACT MATCH is found, set "proof_location" to "UNABLE TO LOCATE EXACT QUOTE"

3. STRICT VALIDATION RULES:
   - severity: ONLY allow "minor" | "significant" | "critical"
   - error_type: ONLY allow
     * "logical_reasoning"
     * "theorem_application"
     * "computational"
     * "case_analysis"

4. ERROR LOCATION STRATEGY:
   - Look for CONSECUTIVE WORDS that exactly match the error description
   - Prefer LONGER quote segments for context
   - Prioritize capturing the FULL problematic reasoning segment

FUNDAMENTAL CONSTRAINT:
- NEVER paraphrase or reinterpret the proof text
- ALWAYS use DIRECT QUOTES from the original proof

Return the new extraction with the same JSON structure
```json
{
  "mathematical_errors": [
    {
      "location_in_proof": "Exact step in user’s proof (quoted verbatim)",
      "severity": "minor" | "significant" | "critical",
      "error_type": "logical_reasoning" | "theorem_application" | "computational" | "case_analysis",
      "explanation": "Mathematically rigorous explanation grounded in proof’s exact wording and structure",
      "suggested_hint": "Precise mathematical guidance on correcting the error"
    }
  ]
}
```"""  # noqa W291
        user = f"""Original Proof Tentative:
{attempt.user_informal_proof}

Current Extraction:
{result}

Validate and correct the extraction, ensuring:
1. Exact verbatim proof location
2. Precise error classification
3. Mathematically rigorous explanation
"""

        return Prompt(id="result-refinement", system=system, user=user)
