import os
from typing import List, Optional

from loguru import logger
from openai import OpenAI
from pydantic import BaseModel

from mathlete.autoformalization.proof_formalization import LeanFormalizationResult
from mathlete.feedback.formal.formal_proof_feedback import FormalProofAnalyzer
from mathlete.feedback.formal.formal_proof_feedback_models import FormalizationError
from mathlete.feedback.informal.informal_proof_feedback import InformalProofAnalyzer
from mathlete.feedback.informal.informal_proof_feedback_models import MathematicalError
from mathlete.utils.constants import UserProofAttempt


class ProofAnalysisResult(BaseModel):
    is_valid: bool
    mathematical_errors: List[MathematicalError]
    formalization_errors: List[FormalizationError]
    summary: str


class ProofAnalyzer:
    def __init__(self, api_key: Optional[str] = None):
        self.client = OpenAI(api_key=api_key or os.getenv("OPENAI_API_KEY"))
        self.informal_analyzer = InformalProofAnalyzer()
        self.formal_analyzer = FormalProofAnalyzer()

    def analyze_proof(
        self,
        attempt: UserProofAttempt,
        formalization: LeanFormalizationResult,
        has_error: bool,
        lean_results: List[dict],
    ) -> ProofAnalysisResult:

        if not has_error:
            logger.info("Proof is valid, no analysis needed")
            return ProofAnalysisResult(
                is_valid=True,
                mathematical_errors=[],
                formalization_errors=[],
                summary="The proof is formally verified and correct.",
            )

        logger.info("Analyzing mathematical errors")
        math_result = self.informal_analyzer.analyze_informal_proof(attempt)

        logger.info("Analyzing formalization errors")
        formal_result = self.formal_analyzer.analyze_formal_proof(
            attempt, formalization, lean_results
        )

        is_valid = (
            len(math_result.mathematical_errors) == 0
            and len(formal_result.formalization_errors) == 0
        )

        combined_summary = self._generate_combined_summary(
            math_result.mathematical_errors,
            formal_result.formalization_errors,
            math_result.summary,
            formal_result.summary,
        )

        return ProofAnalysisResult(
            is_valid=is_valid,
            mathematical_errors=math_result.mathematical_errors,
            formalization_errors=formal_result.formalization_errors,
            summary=combined_summary,
        )

    def _generate_combined_summary(
        self,
        mathematical_errors: List[MathematicalError],
        formalization_errors: List[FormalizationError],
        math_summary: str,
        formal_summary: str,
    ) -> str:
        """Generate a comprehensive summary combining both mathematical and formalization analyses"""

        has_math_errors = len(mathematical_errors) > 0
        has_formal_errors = len(formalization_errors) > 0

        if not has_math_errors and not has_formal_errors:
            return "The proof is both mathematically sound and correctly formalized."

        if has_math_errors and not has_formal_errors:
            return f"The proof has mathematical issues but no formalization errors. {math_summary}"

        if not has_math_errors and has_formal_errors:
            return f"The proof is mathematically sound but has formalization issues. {formal_summary}"

        # Both types of errors exist
        return f"The proof has both mathematical and formalization issues. Mathematical issues: {math_summary} Formalization issues: {formal_summary}"
