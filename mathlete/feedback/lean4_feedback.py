# adapted from https://github.com/project-numina/autoformalizer/blob/242b27ae60bede8c62b9d6e480b37dcd7c5a3d4a/autoformalizer/eval_utils/lean_feedback.py#L4
import concurrent.futures
import json
import logging
import os
import signal
import subprocess
import tempfile
import time
from pathlib import Path
from typing import List

from tqdm import tqdm


def lean4_feedback(code):
    if not isinstance(code, list):
        code = [code]

    lean_results = parallel_lean4_feedback(
        lean_codes=code,
        num_workers=30,
        max_retries=1,
        timeout=60,
        memory_limit=32,
    )

    has_error_list = []
    error_only_results = []
    for lean_result in lean_results:
        has_err, error_messages = has_error(
            lean_result, accept_sorry=False, return_error_messages=True
        )
        has_error_list.append(has_err)

        if has_err:
            lean_result["errors"] = error_messages
            error_only_results.append(lean_result)

    return has_error_list, error_only_results


def has_error(
    feedback: dict, accept_sorry: bool = True, return_error_messages: bool = False
):
    """
    Checks if the Lean feedback contains an error.

    Args:
    - feedback: The Lean feedback as a dictionary.
    - accept_sorry: Whether to accept "sorry" statements as "not an error".
    By default, "sorry" statements are not considered errors.
    """

    errors = []

    # Check top-level errors
    if "error" in feedback:
        errors.append(feedback["error"])

    if "stderr" in feedback and feedback["stderr"].strip():
        errors.append(feedback["stderr"].strip())

    # Extract messages with severity "error"
    if "messages" in feedback:
        errors.extend(
            message["data"]
            for message in feedback.get("messages", [])
            if message.get("severity") == "error"
        )

    sorry_warnings = []
    if not accept_sorry and "messages" in feedback:
        sorry_warnings = [
            message["data"]
            for message in feedback.get("messages", [])
            if message.get("severity") == "warning"
            and "declaration uses 'sorry'" in message["data"]
        ]

    has_error = bool(errors or sorry_warnings)
    all_errors = errors + (sorry_warnings if not accept_sorry else [])

    if return_error_messages:
        return has_error, all_errors
    return has_error


def _lean4_feedback(
    lean_code: str,
    header: str = None,
    max_retries: int = 1,
    timeout: int = 60,
    memory_limit: int = 32,
    verbose: bool = False,
):
    """
    Gets Lean 4 feedback for a given Lean code.

    Args:
    - lean_code: The Lean code to get feedback for.
    - header: The header to prepend to the Lean code.
    - max_retries: The maximum number of retries.
    - timeout: The timeout for the Lean feedback command.
    - memory_limit: The memory limit for the Lean feedback command in GB.
    - verbose: Whether to print the feedback.
    """
    base = os.environ.get("AUTOFORMALIZER_WORKSPACE")
    if not base:
        raise EnvironmentError("AUTOFORMALIZER_WORKSPACE is not set.")

    path_to_repl = f"{base}/repl/.lake/build/bin/repl"
    path_to_mathlib = f"{base}/mathlib4"

    if not os.path.exists(path_to_mathlib):
        raise ValueError(f"Path to mathlib does not exist: {path_to_mathlib}")

    directory = path_to_mathlib + "/tmp/"
    Path(directory).mkdir(parents=True, exist_ok=True)
    with tempfile.NamedTemporaryFile(
        dir=directory, mode="w", suffix=".in", delete=True
    ) as in_file, tempfile.NamedTemporaryFile(
        dir=directory, mode="w", suffix=".lean", delete=True
    ) as input_file:

        file_string = f"""{{"path": "{input_file.name}", "allTactics": true}}"""
        in_file.write(file_string)
        in_file.flush()

        if header is not None:
            formalization = header + lean_code
        else:
            formalization = lean_code

        input_file.write(formalization)
        input_file.flush()

        current_env = os.environ.copy()

        mem_limit_bytes = memory_limit * 1024 * 1024  # Convert GB to KB

        command = f"cd {path_to_mathlib} && ulimit -v {mem_limit_bytes} && lake env {path_to_repl} < {in_file.name}"
        data = {"error": "unknown lean4 error"}

        for attempt in range(max_retries):
            try:
                if verbose:
                    logging.info(f"Attempt {attempt + 1} to run command: {command}")

                with subprocess.Popen(
                    command,
                    shell=True,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    preexec_fn=os.setsid,
                    text=True,
                    encoding="utf-8",
                    env=current_env,
                ) as process:
                    try:
                        stdout, stderr = process.communicate(timeout=timeout)
                    except subprocess.TimeoutExpired:
                        # Ensure all relevant processes are termination
                        os.killpg(os.getpgid(process.pid), signal.SIGKILL)
                        stdout, stderr = (
                            process.communicate()
                        )  # Read any remaining output
                        process.wait()
                        logging.error(
                            f"Lean feedback command timed out on attempt {attempt + 1}."
                        )
                        continue  # Retry if within max_retries

                    if process.returncode != 0:
                        message = f"Command failed with return code {process.returncode} on attempt {attempt + 1}"
                        logging.error(message + f": {stderr.strip()}")
                        if attempt < max_retries - 1:
                            time.sleep(0.1)
                            continue  # Retry
                        else:
                            return {
                                "error": "command_failed",
                                "details": stderr.strip(),
                            }

                    feedback = stdout
                    data = json.loads(feedback)
                    if len(stderr.strip()):
                        # This covers the case {'message': "unknown metavariable '?[anonymous]'"}
                        # in which the REPL outputs a bunch of messages to stderr but still returns a valid JSON
                        # logging.error(f"Command returned stderr on attempt {attempt + 1}: {stderr.strip()}")
                        data["stderr"] = stderr.strip()

                    if verbose:
                        logging.info(f"Command executed successfully: {feedback[:20]}")
                    break  # Success, exit retry loop

            except json.JSONDecodeError as e:
                logging.error(f"JSON decoding error on attempt {attempt + 1}: {e}")
                logging.error(
                    f"Faulty JSON content: {stdout.strip() if 'stdout' in locals() else 'No output'}"
                )
                return {"error": "json_decode_error"}
            except Exception as e:
                logging.error(f"Unexpected error on attempt {attempt + 1}: {e}")
                if attempt < max_retries - 1:
                    time.sleep(0.1)
                else:
                    return {"error": "unexpected_error", "details": str(e)}

    return data


def parallel_lean4_feedback(
    lean_codes: List[str],
    headers: List[str] = None,
    num_workers: int = 30,
    max_retries: int = 1,
    timeout: int = 60,
    memory_limit: int = 32,
):
    """
    Parallelizes getting the lean feedback across multiple CPUs using concurrent.futures.ThreadPoolExecutor.

    Args:
    - lean_codes: A list of Lean 4 codes to get feedback for.
    - headers: A list of headers to prepend to the Lean codes.
    - num_workers: The number of workers to use.
    - max_retries: The maximum number of retries.
    - timeout: The timeout for the Lean feedback command.
    - memory_limit: The memory limit for the Lean feedback command in GB.
    """

    # Create a helper function that combines the code and header and calls lean4_feedback
    def feedback_helper(args):
        code, header = args
        return _lean4_feedback(
            lean_code=code,
            header=header,
            max_retries=max_retries,
            timeout=timeout,
            memory_limit=memory_limit,
        )

    # If headers are provided, zip lean_codes and headers together, else zip lean_codes with None
    if headers is None:
        inputs = [(code, None) for code in lean_codes]
    else:
        inputs = zip(lean_codes, headers)

    # Start timing
    start_time = time.time()

    # Use ThreadPoolExecutor with a progress bar
    with concurrent.futures.ThreadPoolExecutor(max_workers=num_workers) as executor:
        # Wrap the executor.map in tqdm for a progress bar
        results = list(
            tqdm(
                executor.map(feedback_helper, inputs),
                total=len(lean_codes),
                desc="Processing Lean 4 Codes",
            )
        )

    # End timing
    end_time = time.time()
    total_time = end_time - start_time

    # Print the total time taken
    logging.info(f"Total time taken: {total_time:.2f} seconds")

    return results
