from typing import List

from loguru import logger

from mathlete.autoformalization.proof_formalization_models import (
    LeanFormalizationResult,
)
from mathlete.feedback.formal.formal_proof_feedback_models import (
    FormalizationAnalysisResult,
)
from mathlete.feedback.formal.formal_proof_feedback_prompts import (
    FormalProofFeedbackPromptGenerator,
)
from mathlete.llms import llms
from mathlete.utils.constants import UserProofAttempt


class FormalProofAnalyzer:
    def __init__(self):
        pass

    def analyze_formal_proof(
        self,
        attempt: UserProofAttempt,
        formalization: LeanFormalizationResult,
        lean_results: List[dict],
    ) -> FormalizationAnalysisResult:
        """Analyze only the Lean4 formalization errors"""
        prompt_generator = FormalProofFeedbackPromptGenerator()
        prompt = prompt_generator.make_prompt_formal_proof_feedback(
            attempt, formalization, lean_results
        )

        try:
            return llms.execute_prompt(
                prompt=prompt, output_class=FormalizationAnalysisResult
            )

        except Exception as e:
            logger.error(f"Error analyzing formalization: {str(e)}")
            raise RuntimeError(f"Formalization analysis failed: {str(e)}")
