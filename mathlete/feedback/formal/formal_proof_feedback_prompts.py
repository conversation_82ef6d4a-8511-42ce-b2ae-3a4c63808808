import json
from typing import List

from mathlete.autoformalization.proof_formalization_models import (
    LeanFormalizationResult,
)
from mathlete.llms.prompt import Prompt
from mathlete.utils.constants import UserProofAttempt


class FormalProofFeedbackPromptGenerator:
    def make_prompt_formal_proof_feedback(
        self,
        attempt: UserProofAttempt,
        formalization: LeanFormalizationResult,
        lean_results: List[dict],
    ) -> Prompt:
        problem = attempt.problem
        system = """You are a mathematical reasoning assistant specializing in Lean4 and mathematics tutoring.
                Your role is to analyze a user's Lean4 formalization of a mathematical proof and identify issues in the formalization,
                providing constructive, educational feedback.

                ## Core Instructions
                1. Lean4 Formalization Assessment
                    - Assess the proof's formalization correctness.
                    - Note that you're only looking for formalization issues, not mathematical errors.

                2. Clear Error Categorization
                    - Lean4 Formalization Errors categories:
                        - syntax_error
                        - type_error
                        - import_error

                3. Precise Error Linking: for each error, reference the exact quote from the informal proof where the issue occurs in the formalization.

                Return a JSON with this structure:
                {
                    "formalization_errors": [
                        {
                            "informal_proof_location": "exact quote from informal proof where the formalization error relates to",
                            "specific_error_category": "detailed subcategory (e.g., 'syntax_error', 'type_error', 'import_error')",
                            "explanation": "precise explanation of why this formalization is incorrect"                        }
                    ],
                    "summary": "concise overall assessment of formalization issues if any"
                }

                ## Key Constraints
                - Error Focus: Only focus on Lean4 formalization errors, not mathematical reasoning issues.
                """
        user = f"""PROBLEM STATEMENT
                {problem.informal_statement}

                USER'S INFORMAL PROOF ATTEMPT
                {attempt.user_informal_proof}

                IDENTIFIED GAPS IN LEAN FORMALIZATION
                {formalization.gaps_identified}

                LEAN ERRORS
                {json.dumps(lean_results, indent=2)}
            """
        return Prompt(id="formal-proof-feedback", system=system, user=user)
