from typing import Any, Dict, List

from pydantic import BaseModel


class FormalizationError(BaseModel):
    informal_proof_location: str
    specific_error_category: str  # e.g., syntax_error, type_error, import_error
    explanation: str

    def to_dict(self) -> Dict[str, Any]:
        return {
            "informal_proof_location": self.informal_proof_location,
            "specific_error_category": self.specific_error_category,
            "explanation": self.explanation,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "FormalizationError":
        return cls(
            informal_proof_location=data.get("informal_proof_location", ""),
            specific_error_category=data.get("specific_error_category", ""),
            explanation=data.get("explanation", ""),
        )


class FormalizationAnalysisResult(BaseModel):
    formalization_errors: List[FormalizationError]
    summary: str

    def to_dict(self) -> Dict[str, Any]:
        return {
            "formalization_errors": [
                error.to_dict() for error in self.formalization_errors
            ],
            "summary": self.summary,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "FormalizationAnalysisResult":
        return cls(
            formalization_errors=[
                FormalizationError.from_dict(error)
                for error in data.get("formalization_errors", [])
            ],
            summary=data.get("summary", ""),
        )
