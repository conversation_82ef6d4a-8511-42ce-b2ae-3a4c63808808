import argparse
import concurrent.futures
from datetime import datetime
from pathlib import Path
from typing import List

import pandas as pd
from loguru import logger

from mathlete.autoformalization.proof_formalization_models import (
    LeanFormalizationResult,
)
from mathlete.autoformalization.proof_formalization_prompts import (
    LeanFormalizationPromptGenerator,
)
from mathlete.llms import llms
from mathlete.utils.constants import MathProblem, UserProofAttempt


class LeanProofFormalizer:
    def __init__(self):
        pass

    def formalize_proof(self, attempt: UserProofAttempt) -> LeanFormalizationResult:
        """
        Formalize an informal proof into Lean4.

        :param attempt: UserProofAttempt instance to formalize
        :return: LeanFormalizationResult with Lean4 formalization
        """
        prompt_generator = LeanFormalizationPromptGenerator()
        prompt = prompt_generator.make_prompt_proof_formalization(attempt)

        try:
            logger.info("Formalizing proof attempt...")
            return llms.execute_prompt(
                prompt=prompt, output_class=LeanFormalizationResult
            )

        except Exception as e:
            logger.error(f"Formalization error: {e}")
            raise RuntimeError(f"Lean4 formalization failed: {str(e)}")


def process_problem(problem, user_proof, formalizer, results_dir):
    user_attempt = UserProofAttempt.create_new(problem, "test_user123", user_proof)

    try:
        result = formalizer.formalize_proof(user_attempt)

        lean_file = results_dir / f"{problem.uuid}.lean"
        with lean_file.open("w") as f:
            if problem.informal_proof:
                f.write("/- Start of correct informal solution for reference\n")
                f.write(problem.informal_proof)
                f.write("\n-/ End of correct informal solution for reference\n\n")

            if problem.lean4_solution:
                f.write("/- Start of correct Lean4 solution for reference\n")
                f.write(problem.lean4_solution)
                f.write("\n-/ End of correct Lean4 solution for reference\n\n")

            f.write("-- Automatically generated Lean4 formalization\n")
            for imp in result.required_imports:
                f.write(f"import {imp}\n")
            f.write("\n")

            f.write(result.lean4_code)

        print(f"Formalization saved: {lean_file}")

    except Exception as e:
        logger.error(f"Formalization failed for {problem.uuid}: {e}")


def main():
    parser = argparse.ArgumentParser(
        description="Formalize mathematical proofs in Lean4"
    )
    parser.add_argument(
        "--input",
        default=str(Path(__file__).parent.parent / "data/final_data.parquet"),
        help="Path to the Parquet file containing MathProblem data",
    )
    parser.add_argument(
        "--output-dir",
        default=str(Path(__file__).parent.parent / "data/formalizations"),
        help="Directory to save Lean4 formalizations",
    )
    parser.add_argument(
        "--limit",
        type=int,
        default=10,
        help="Limit the number of problems processed (default: 10)",
    )
    parser.add_argument(
        "--user-proof",
        type=str,
        help="Optional user-provided informal proof to override dataset proof",
    )

    args = parser.parse_args()

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_dir = Path(args.output_dir) / f"formalization_{timestamp}"
    results_dir.mkdir(parents=True, exist_ok=True)

    try:
        df = pd.read_parquet(args.input)
        df = df.head(args.limit)
        problems: List[MathProblem] = [
            MathProblem.from_dict(row) for _, row in df.iterrows()
        ]
    except Exception as e:
        logger.error(f"Failed to read Parquet file: {e}")
        exit(1)

    formalizer = LeanProofFormalizer()

    user_proofs = [
        args.user_proof if args.user_proof else problem.informal_proof
        for problem in problems
    ]

    with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
        futures = {
            executor.submit(
                process_problem, problem, user_proof, formalizer, results_dir
            )
            for problem, user_proof in zip(problems, user_proofs)
        }

        for future in concurrent.futures.as_completed(futures):
            try:
                future.result()
            except Exception as e:
                logger.error(f"Unexpected error in processing: {e}")


if __name__ == "__main__":
    main()
