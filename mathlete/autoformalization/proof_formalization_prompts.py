from mathlete.llms.prompt import Prompt
from mathlete.utils.constants import UserProofAttempt


class LeanFormalizationPromptGenerator:
    def make_prompt_proof_formalization(self, attempt: UserProofAttempt) -> Prompt:
        system = """You are an expert mathematical proof formalizer specializing in Lean4.

                Your task is to:
                1. Analyze the user's informal proof attempt
                2. Use the official informal proof as a guideline to determine if minor gaps in the user's attempt can be bridged.
                3. For minor gaps or notation issues, bridge these in your formalization
                4. For significant conceptual gaps or errors in the proof, DO NOT add missing steps or correct the proof logic
                5. Identify but DO NOT FILL significant gaps
                6. Formalize ONLY the user's existing steps into Lean4


                Requirements:
                - Use ONLY Lean4 syntax
                - Include all necessary mathlib4 imports
                - Translate user's steps literally where possible
                - For missing critical steps: use `sorry` or leave holes
                - Preserve any mistakes in logical flow from the user's attempt
                - Never introduce new lemmas/theorems not mentioned by the user
                - Add comments indicating potential gaps using -- GAP: notation

                Output a JSON with the following structure:
                {
                    "lean4_code": "Complete Lean4 proof code based on user's attempt",
                    "required_imports": ["Mathlib4.SomeModule", "Mathlib4.AnotherModule"],
                    "gaps_identified": ["Description of any significant gaps in the user's proof that were preserved in the formalization"]
                }
                """
        problem = attempt.problem
        user = f""""Problem Statement (Informal): {problem.informal_statement}
                Formalized Problem: {problem.formal_statement}

                Reference Materials:
                - Correct Informal Proof: {problem.informal_proof}
                - Formal Solution: {problem.lean4_solution or 'Not available'}

                User's Proof Attempt:
                {attempt.user_informal_proof}
                """

        return Prompt(id="proof_formalization", system=system, user=user)
