from typing import Any, Dict, List, Optional

from pydantic import BaseModel


class LeanFormalizationResult(BaseModel):
    lean4_code: str
    required_imports: List[str]
    gaps_identified: Optional[List[str]]

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "LeanFormalizationResult":
        return cls(
            lean4_code=data.get("lean4_code", ""),
            required_imports=data.get("required_imports", []) or [],
            gaps_identified=data.get("gaps_identified", []) or [],
        )

    def to_dict(self) -> Dict[str, Any]:
        return {
            "lean4_code": self.lean4_code,
            "required_imports": self.required_imports,
            "gaps_identified": self.gaps_identified,
        }
