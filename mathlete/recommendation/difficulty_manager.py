from typing import List

from mathlete.utils.problem_manager import ProblemManager


class DifficultyManager:
    def __init__(self, problem_manager: ProblemManager) -> None:
        self.problem_manager = problem_manager

    def suggest_target_difficulty(self, current_problems: List[str]) -> float:
        if not current_problems:
            return 3.0

        current_max = max(
            self.problem_manager.get_problem_difficulty(p) for p in current_problems
        )

        if current_max < 3:
            return min(current_max + 2, 10)  # <PERSON><PERSON> jumps for beginners
        else:
            return min(current_max * 1.3, 10)  # Percentage-based progression
