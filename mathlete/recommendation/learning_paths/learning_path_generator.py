from typing import List, <PERSON><PERSON>

from loguru import logger

from mathlete.utils.constants import MathProblem

from ...graph.graph_service import GraphService
from ...graph.similarity_graph_builder import SimilarityGraphBuilder
from ...utils.problem_manager import ProblemManager
from ..cold_start_manager import ColdStartManager
from ..difficulty_manager import DifficultyManager


class LearningPathGenerator:
    def __init__(
        self,
        problems: List[MathProblem],
        similarity_threshold: float = 0.7,
        weight_threshold: float = 0.7,
        graph_service: GraphService = None,
        graph_path: str = "similarity_graph.graphml",
    ) -> None:
        self.problem_manager = ProblemManager(problems)
        self.graph_service = graph_service
        if self.graph_service and self.graph_service.graph_exists():
            logger.info("Loading graph and similarity matrix from disk")
            self.distance_graph = self.graph_service.load_graph()

        else:
            logger.info("Building similarity graph and similarity matrix")
            self.similarity_graph_builder = SimilarityGraphBuilder(
                problem_manager=self.problem_manager,
                similarity_threshold=similarity_threshold,
                weight_threshold=weight_threshold,
            )
            self.distance_graph = self.similarity_graph_builder.get_similarity_graph(
                use_difficulty=True
            )
            if self.graph_service:
                logger.info(f"Saving graph matrix to disk at {graph_path}")
                self.graph_service.save_graph(self.distance_graph)

        self.cold_start_manager = ColdStartManager(self.problem_manager)
        self.difficulty_manager = DifficultyManager(self.problem_manager)

    def _find_bridging_path(self, sources: List[str], targets: List[str]) -> List[str]:
        """
        Find an optimal bridging path in the similarity graph from sources to targets.

        This method attempts to connect the given source problems to target problems
        by finding the shortest path in the similarity graph.

        Args:
            sources (List[str]): List of source problem IDs to start the path from.
            targets (List[str]): List of target problem IDs to connect the path to.

        Returns:
            List[str]: A list of problem IDs representing the bridging path from
                    sources to targets, excluding the start and end nodes. Returns
                    an empty list if no valid path is found.
        """
        G = self.distance_graph.copy()

        G.add_vertices(2)
        start_idx = G.vcount() - 2
        end_idx = G.vcount() - 1

        G.vs[start_idx]["name"] = "__start__"
        G.vs[end_idx]["name"] = "__end__"

        edges_to_add: List[Tuple[int, int]] = []
        weights_to_add: List[float] = []

        for source in sources:
            try:
                source_idx = G.vs.find(name=source).index
                edges_to_add.append((start_idx, source_idx))
                weights_to_add.append(0.0)
            except ValueError:
                logger.warning(f"Source node '{source}' not found in graph")

        for target in targets:
            try:
                target_idx = G.vs.find(name=target).index
                edges_to_add.append((target_idx, end_idx))
                weights_to_add.append(0.0)
            except ValueError:
                logger.warning(f"Target node '{target}' not found in graph")

        G.add_edges(edges_to_add)

        for i, edge in enumerate(G.es[G.ecount() - len(edges_to_add) :]):  # noqa: E203
            edge["weight"] = weights_to_add[i]

        try:
            path_indices = G.get_shortest_paths(
                start_idx, to=end_idx, weights="weight", output="vpath"
            )[0]

            if not path_indices or len(path_indices) <= 2:
                logger.info("No valid path found connecting sources to targets")
                return []

            path = [G.vs[idx]["name"] for idx in path_indices[1:-1]]
            return path

        except Exception as e:
            logger.error(f"Error finding shortest path: {e}")
            return []

    def find_bridging_path(
        self, sources: List[str], metadata_field: str, metadata_value: str
    ) -> List[str]:
        """
        Find a bridging path connecting the given sources to problems
        with the given metadata value.

        Args:
            sources: A list of problem IDs to find a bridging path from.
            metadata_field: The name of the metadata field to use for the bridging
                path.
            metadata_value: The value of the metadata field to look for in the
                target problems.

        Returns:
            A list of problem IDs representing a bridging path between the given
            sources and the target problems with the given metadata value.

        Raises:
            ValueError: If the dataset is empty or if the given metadata field
                does not exist in the problem metadata.
        """
        if not self.problem_manager.has_problems:
            raise ValueError("No problems available in the dataset.")

        sample_problem = self.problem_manager.problems[0]

        valid_fields = set()
        if sample_problem.metadata:
            valid_fields.update(sample_problem.metadata.model_fields.keys())
        if sample_problem.exam_info:
            valid_fields.update(sample_problem.exam_info.model_fields.keys())

        if metadata_field not in valid_fields:
            raise ValueError(
                f"Invalid metadata field: '{metadata_field}' does not exist in problem metadata."
            )

        def get_metadata_value(problem: MathProblem, field: str):
            if problem.metadata and field in problem.metadata.model_fields:
                return getattr(problem.metadata, field)
            if problem.exam_info and field in problem.exam_info.model_fields:
                return getattr(problem.exam_info, field)
            return None

        target_ids = [
            p.uuid
            for p in self.problem_manager.problems
            if get_metadata_value(p, metadata_field) == metadata_value
        ]

        logger.info(f"source {sources}, target {target_ids}")
        if not target_ids:
            logger.debug("No target problems found")
            return []

        return self._find_bridging_path(sources, target_ids)

    def suggest_target_difficulty(self, current_problems: List[str]) -> float:
        return self.difficulty_manager.suggest_target_difficulty(current_problems)
