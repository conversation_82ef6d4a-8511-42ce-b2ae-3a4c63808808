from typing import List, Optional, Set

import numpy as np
from loguru import logger

from mathlete.recommendation.cold_start_manager import ColdStartManager
from mathlete.recommendation.difficulty_manager import DifficultyManager
from mathlete.utils.problem_manager import ProblemManager
from mathlete.vectors.embedding_generator import EmbeddingGenerator
from mathlete.vectors.matrix_service import MatrixService
from mathlete.vectors.similarity_matrix_builder import SimilarityMatrixBuilder


class ProblemRecommender:
    def __init__(
        self,
        problem_manager: ProblemManager = None,
        embedding_generator: EmbeddingGenerator = None,
        matrix_service: MatrixService = None,
        matrix_path: str = "similarity_matrix.npy",
    ) -> None:
        self.problem_manager = problem_manager
        self.embedding_generator = embedding_generator
        self.matrix_service = matrix_service
        if self.matrix_service and self.matrix_service.matrix_exists():
            logger.info("Loading similarity matrix from disk")
            self.similarity_matrix = self.matrix_service.load_similarity_matrix()

        else:
            logger.info("Building similarity matrix")
            self.similarity_graph_builder = SimilarityMatrixBuilder(
                self.problem_manager, self.embedding_generator
            )
            self.similarity_matrix = (
                self.similarity_graph_builder.get_similarity_matrix()
            )
            if self.matrix_service:
                logger.info(f"Saving similarity matrix to disk at {matrix_path}")
                self.matrix_service.save_similarity_matrix(self.similarity_matrix)

        self.cold_start_manager = ColdStartManager(self.problem_manager)
        self.difficulty_manager = DifficultyManager(self.problem_manager)

    def _find_next_problem(
        self,
        current_problems: List[str],
        eligible_problems: List[str],
        excluding: Set[str],
        recency_window: int = 5,
        lambda_param: float = 0.7,
    ) -> Optional[str]:
        """
        Finds the next problem to recommend based on current problems, eligible problems,
        and diversity considerations using the Maximal Marginal Relevance (MMR) algorithm.

        Args:
            current_problems (List[str]): List of problems uuid the user has already interacted with.
            eligible_problems (List[str]): List of problems uuid that are eligible for recommendation.
            excluding (Set[str]): Set of problems uuid to exclude from recommendation.
            recency_window (int, optional): Number of most recent problems to consider for diversity calculation. Defaults to 5.
            lambda_param (float, optional): Parameter controlling the trade-off between relevance and diversity. Defaults to 0.7.

        Returns:
            Optional[str]: The next recommended problem or None if no suitable problem is found.
        """

        recent_problems = (
            current_problems[-recency_window:]
            if len(current_problems) > recency_window
            else current_problems
        )
        logger.debug(
            f"Using {len(recent_problems)} recent problems for diversity calculation"
        )
        candidates = [p for p in eligible_problems if p not in excluding]
        logger.debug(
            f"Found {len(candidates)} candidate problems after exclusion filtering"
        )

        if not candidates:
            logger.warning("No candidate problems available")
            return None

        candidate_indices = np.array(
            self.problem_manager.get_problem_indices(candidates)
        )
        current_indices = np.array(
            self.problem_manager.get_problem_indices(current_problems)
        )
        recent_indices = np.array(
            self.problem_manager.get_problem_indices(recent_problems)
        )

        valid_candidates = [
            c for c in candidates if c in self.problem_manager.problem_ids
        ]

        logger.debug(f"Found {len(valid_candidates)} valid candidates with indices")

        if not valid_candidates or not current_indices.size:
            logger.warning("No valid candidates or current problems with indices")
            return None

        relevance_matrix = self.similarity_matrix[
            np.ix_(candidate_indices, current_indices)
        ]
        relevance_scores = np.mean(relevance_matrix, axis=1)

        diversity_penalty = np.zeros(len(candidate_indices))
        if recent_indices.size > 0:
            diversity_matrix = self.similarity_matrix[
                np.ix_(candidate_indices, recent_indices)
            ]
            diversity_penalty = np.max(diversity_matrix, axis=1)

        logger.debug(f"Calculating MMR scores with lambda={lambda_param}")
        mmr_scores = (
            lambda_param * relevance_scores - (1 - lambda_param) * diversity_penalty
        )

        if mmr_scores.size > 0:
            best_idx = np.argmax(mmr_scores)
            best_problem = valid_candidates[best_idx]
            best_score = mmr_scores[best_idx]
            logger.info(
                f"Selected problem {best_problem} with MMR score {best_score:.4f}"
            )
            logger.debug(
                f"Relevance: {relevance_scores[best_idx]:.4f}, Diversity penalty: {diversity_penalty[best_idx]:.4f}"
            )
            return best_problem

        logger.warning("No valid MMR scores calculated")
        return None

    def recommend_problems(
        self,
        start_problems: List[str],
        recency_window: int = 5,
        max_problems: int = 10,
        lambda_param: float = 0.7,
    ) -> List[str]:
        """
        Recommends a sequence of problems starting from a set of problems.

        Args:
            start_problems (List[str]): A list of problem UUIDs to start recommendations from.
            recency_window (int, optional): Number of recent problems to use for diversity. Defaults to 5.
            max_problems (int, optional): Max number of problems to recommend. Defaults to 10.
            lambda_param (float, optional): Balances relevance vs. diversity. Defaults to 0.7.

        Returns:
            List[str]: A list of recommended problem UUIDs.
        """
        logger.info(
            f"Recommending problems based on {len(start_problems)} start problems"
        )

        if not start_problems:
            logger.warning("No start problems provided; returning empty recommendation")
            return []

        excluding = set(start_problems)
        eligible_problems = [
            p.uuid for p in self.problem_manager.problems if p.uuid not in excluding
        ]
        logger.info(f"{len(eligible_problems)} problems eligible for recommendation")

        path: List[str] = []
        current_problems = start_problems.copy()

        while len(path) < max_problems:
            next_problem = self._find_next_problem(
                current_problems=current_problems,
                eligible_problems=eligible_problems,
                excluding=excluding,
                recency_window=recency_window,
                lambda_param=lambda_param,
            )

            if not next_problem:
                logger.info("No more suitable problems found")
                break

            path.append(next_problem)
            current_problems = [next_problem]
            excluding.add(next_problem)

            logger.info(f"Added problem {next_problem} to recommendation list")

        logger.success(f"Recommended {len(path)} problems")
        return path

    def _find_learning_path(
        self,
        start_problems: List[str],
        target_difficulty: int = 5,
        recency_window: int = 5,
        max_problems: int = 10,
        lambda_param: float = 0.7,
    ) -> List[str]:
        """
        Generates a learning path from given start problems to reach a target difficulty.

        Args:
            start_problems (List[str]): A list of problem uuid identifiers to start the path from.
            target_difficulty (int, optional): The target difficulty level to reach. Defaults to 5.
            recency_window (int, optional): The number of recent problems to consider for recency effects. Defaults to 5.
            max_problems (int, optional): The maximum number of problems to include in the path. Defaults to 10.
            lambda_param (float, optional): The parameter for balancing relevance and diversity. Defaults to 0.7.

        Returns:
            List[str]: A list of problem identifiers forming the generated learning path.

        Raises:
            ValueError: If the minimum difficulty of recent problems exceeds the target difficulty.
        """
        if target_difficulty is None:
            logger.info(
                "Target difficulty not provided, using difficulty manager suggestion"
            )
            difficulty_manager = DifficultyManager(problem_manager=self.problem_manager)
            target_difficulty = difficulty_manager.suggest_target_difficulty(
                start_problems
            )

        logger.info(
            f"Finding learning path from {len(start_problems)} start problems to difficulty {target_difficulty}"
        )

        if not start_problems:
            logger.info("No start problems provided, using cold start approach")
            return []

        max_difficulty = max(
            self.problem_manager.get_problem_difficulty(p) for p in start_problems
        )
        recent_difficulties = [
            self.problem_manager.get_problem_difficulty(p)
            for p in start_problems[-recency_window:]
        ]
        min_difficulty = max(1, min(recent_difficulties) - 2)
        logger.info(f"Minimum difficulty is {min_difficulty}")
        if min_difficulty >= target_difficulty:
            raise ValueError(
                f"Minimum difficulty {min_difficulty} is higher than target difficulty {target_difficulty}"
            )

        eligible_problems = [
            p.uuid
            for p in self.problem_manager.problems
            if min_difficulty
            <= self.problem_manager.get_problem_difficulty(p.uuid)
            <= target_difficulty
        ]
        logger.info(
            f"Found {len(eligible_problems)} eligible problems within difficulty threshold"
        )

        path: List[str] = []
        current_problems = start_problems.copy()
        excluding = set(current_problems)

        logger.info(
            f"Starting learning path construction with max {max_problems} problems"
        )

        while len(path) < max_problems:
            next_problem = self._find_next_problem(
                current_problems,
                eligible_problems,
                excluding,
                recency_window,
                lambda_param,
            )

            if not next_problem:
                logger.warning("No suitable next problem found, terminating path")
                break

            path.append(next_problem)
            current_problems = [next_problem]
            excluding.add(next_problem)

            problem_difficulty = self.problem_manager.get_problem_difficulty(
                next_problem
            )
            logger.info(
                f"Added problem {next_problem} with difficulty {problem_difficulty} to path"
            )
            max_difficulty = max(max_difficulty, problem_difficulty)

        if max_difficulty < target_difficulty:
            logger.info(
                f"Did not reach target difficulty {target_difficulty} after terminating path"
            )

        logger.success(f"Generated learning path with {len(path)} problems")
        return path

    def find_learning_path(
        self,
        start_problems: List[str] = None,
        target_difficulty: int = 5,
        recency_window: int = 5,
        max_problems: int = 10,
        lambda_param: float = 0.7,
    ) -> List[str]:
        if not start_problems:
            return self.cold_start_manager.get_cold_start_problems(
                max_problems, target_difficulty
            )
        return self._find_learning_path(
            start_problems,
            target_difficulty,
            recency_window,
            max_problems,
            lambda_param,
        )
