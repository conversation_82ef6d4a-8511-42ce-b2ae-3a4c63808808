from typing import Dict, List, <PERSON><PERSON>

import numpy as np
from loguru import logger
from sklearn.cluster import KMeans
from sklearn.decomposition import PCA
from sklearn.metrics import pairwise_distances_argmin_min

from mathlete.utils.problem_manager import ProblemManager


class ColdStartManager:
    def __init__(self, problem_manager: <PERSON>Manager) -> None:
        self.problem_manager = problem_manager

    def get_cold_start_problems(
        self, n_problems: int = 10, target_difficulty: int = 5
    ) -> List[str]:
        if not 1 <= target_difficulty <= 10:
            raise ValueError("target_difficulty must be between 1 and 10")

        difficulty_ranges: Dict[str, Tuple[int, int]] = {
            "easy": (1, 3),
            "medium": (4, 7),
            "hard": (8, 10),
        }

        if target_difficulty <= 3:
            allocation_percentages: Dict[str, float] = {
                "easy": 0.7,
                "medium": 0.2,
                "hard": 0.1,
            }
        elif target_difficulty <= 7:
            allocation_percentages = {"easy": 0.3, "medium": 0.5, "hard": 0.2}
        else:  # 8-10
            allocation_percentages = {"easy": 0.1, "medium": 0.3, "hard": 0.6}

        allocation = {
            level: max(1, int(n_problems * percentage))
            for level, percentage in allocation_percentages.items()
        }

        total_allocated = sum(allocation.values())

        if target_difficulty <= 3:
            adjust_stratum = "easy"
        elif target_difficulty <= 7:
            adjust_stratum = "medium"
        else:
            adjust_stratum = "hard"

        allocation[adjust_stratum] += n_problems - total_allocated
        logger.info(f"Problem difficulty allocation is {allocation}")

        difficulty_strata: Dict[str, List[str]] = {
            level: [] for level in difficulty_ranges
        }
        for problem_id in self.problem_manager.get_problem_ids():
            difficulty = self.problem_manager.get_problem_difficulty(problem_id)
            for level, (min_diff, max_diff) in difficulty_ranges.items():
                if min_diff <= difficulty <= max_diff:
                    difficulty_strata[level].append(problem_id)
                    break

        selected_problems: List[str] = []

        for level, count in allocation.items():
            if count <= 0 or not difficulty_strata[level]:
                logger.debug(f"No problems in difficulty stratum {level}")
                continue

            candidates = difficulty_strata[level]
            if len(candidates) <= count:
                logger.debug(f"Not enough problems in difficulty stratum {level}")
                selected_problems.extend(candidates[:count])
                continue

            embeddings = np.array(
                [self.problem_manager.get_problem_embedding(p) for p in candidates]
            )
            n_samples, n_features = embeddings.shape
            pca_components = min(50, n_samples, n_features)
            pca = PCA(n_components=pca_components)
            embeddings_pca = pca.fit_transform(embeddings)
            kmeans = KMeans(n_clusters=count, random_state=42).fit(embeddings_pca)
            centroids = kmeans.cluster_centers_
            closest_indices, _ = pairwise_distances_argmin_min(
                centroids, embeddings_pca
            )
            selected_problems.extend([candidates[i] for i in closest_indices])

        return selected_problems
