from unittest.mock import AsyncMock, MagicMock, patch

import pandas as pd
import pytest

from mathlete.search.filtering import FilterError, apply_filters
from mathlete.search.tags.tags_matcher import MetadataFuzzyMatcher


@pytest.fixture
def sample_df():
    """Create a sample DataFrame for testing"""
    return pd.DataFrame(
        {
            "problem_type": [
                "Algebra",
                "Geometry",
                "Calculus",
                "Trigonometry",
                "Algebra",
            ],
            "question_type": [
                "Multiple Choice",
                "Short Answer",
                "Proof",
                "Multiple Choice",
                "Short Answer",
            ],
            "exam_category": ["SAT", "ACT", "AP", "SAT", "ACT"],
            "difficulty": [5.0, 7.5, 9.0, 6.5, 8.0],
        }
    )


@pytest.fixture
def mock_metadata_matcher():
    """Mock the MetadataFuzzyMatcher"""
    matcher = MagicMock(spec=MetadataFuzzyMatcher)
    matcher.normalize_filter_values = AsyncMock(
        side_effect=lambda field, values: values
    )
    matcher.match_thresholds = {}
    matcher.metadata_options = {
        "problem_type": ["Algebra", "Geometry", "Calculus", "Trigonometry"],
        "question_type": ["Multiple Choice", "Short Answer", "Proof"],
        "exam_category": ["SAT", "ACT", "AP"],
    }
    return matcher


@pytest.mark.asyncio
async def test_apply_filters_empty_df():
    """Test that empty DataFrame raises FilterError"""
    with pytest.raises(FilterError, match="Empty or None DataFrame provided"):
        await apply_filters({}, pd.DataFrame())

    with pytest.raises(FilterError, match="Empty or None DataFrame provided"):
        await apply_filters({}, None)


@pytest.mark.asyncio
async def test_apply_filters_no_filters(sample_df):
    """Test with no filters should return copy of original df"""
    result = await apply_filters({}, sample_df)
    pd.testing.assert_frame_equal(result, sample_df)
    assert result is not sample_df  # Should be a copy


@pytest.mark.asyncio
async def test_apply_filters_single_field(sample_df, mock_metadata_matcher):
    """Test filtering on a single field"""
    filters = {"problem_type": "Algebra"}
    result = await apply_filters(filters, sample_df)
    assert len(result) == 2
    assert all(result["problem_type"] == "Algebra")


@pytest.mark.asyncio
async def test_apply_filters_multiple_values(sample_df, mock_metadata_matcher):
    """Test filtering with multiple values for a field"""
    filters = {"question_type": ["Multiple Choice", "Short Answer"]}
    result = await apply_filters(filters, sample_df)
    assert len(result) == 4
    assert set(result["question_type"]) == {"Multiple Choice", "Short Answer"}


@pytest.mark.asyncio
async def test_apply_filters_difficulty_range(sample_df):
    """Test filtering with difficulty range"""
    filters = {"difficulty": [7, 8.5]}
    result = await apply_filters(filters, sample_df)
    assert len(result) == 2
    assert all(result["difficulty"] >= 7)
    assert all(result["difficulty"] <= 8.5)


@pytest.mark.asyncio
async def test_apply_filters_multiple_fields(sample_df, mock_metadata_matcher):
    """Test filtering with multiple fields"""
    filters = {
        "problem_type": "Algebra",
        "question_type": "Multiple Choice",
        "difficulty": [5, 7],
    }
    result = await apply_filters(filters, sample_df)
    assert len(result) == 1
    assert result.iloc[0]["problem_type"] == "Algebra"
    assert result.iloc[0]["question_type"] == "Multiple Choice"
    assert 5 <= result.iloc[0]["difficulty"] <= 7


@pytest.mark.asyncio
async def test_apply_filters_invalid_field(sample_df, mock_metadata_matcher):
    """Test that invalid fields are skipped and original DataFrame is returned"""
    with patch(
        "mathlete.search.tags.tags_matcher.MetadataFuzzyMatcher",
        return_value=mock_metadata_matcher,
    ):
        filters = {
            "invalid_field": "value",  # Should be skipped
            "problem_type": "Algebra",  # Valid field that should work
        }

        result = await apply_filters(filters, sample_df)

        assert result is not None
        assert len(result) == 2
        assert all(result["problem_type"] == "Algebra")


@pytest.mark.asyncio
async def test_apply_filters_invalid_difficulty(sample_df, mock_metadata_matcher):
    """Test that invalid difficulty format is skipped and original DataFrame is returned"""
    with patch(
        "mathlete.search.tags.tags_matcher.MetadataFuzzyMatcher",
        return_value=mock_metadata_matcher,
    ):
        # Test with string instead of list
        filters = {"difficulty": "not a range"}
        result = await apply_filters(filters, sample_df)
        pd.testing.assert_frame_equal(result, sample_df)

        # Test with wrong number of elements
        filters = {"difficulty": [7]}
        result = await apply_filters(filters, sample_df)
        pd.testing.assert_frame_equal(result, sample_df)

        # Test with non-numeric values
        filters = {"difficulty": ["a", "b"]}
        result = await apply_filters(filters, sample_df)
        pd.testing.assert_frame_equal(result, sample_df)

        # Test that valid difficulty still works
        filters = {"difficulty": [7, 8]}
        result = await apply_filters(filters, sample_df)
        assert not result.empty
        assert all((result["difficulty"] >= 7) & (result["difficulty"] <= 8))

        # Test with float-like string
        filters = {"difficulty": ["7", "8.5"]}
        result = await apply_filters(filters, sample_df)
        assert len(result) == 2
        assert all(result["difficulty"] >= 7)
        assert all(result["difficulty"] <= 8.5)


@pytest.mark.asyncio
async def test_apply_filters_empty_result(sample_df, mock_metadata_matcher):
    """Test with filters that result in empty DataFrame"""
    filters = {"problem_type": "Non-existent Type", "difficulty": [10, 20]}
    result = await apply_filters(filters, sample_df)
    assert len(result) == 0
