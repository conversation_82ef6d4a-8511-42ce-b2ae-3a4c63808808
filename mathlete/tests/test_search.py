import pandas as pd
import pytest

from mathlete.search.search import MathProblemSearch, QueryDecomposition


@pytest.fixture
def sample_data():
    return pd.DataFrame(
        {
            "problem": [
                "Find x in the equation 2x + 3 = 7",
                "Prove that 2^n > n^2 for n > 4",
            ],
            "informal_solution": ["x = 2", "Use mathematical induction"],
            "problem_type": ["Algebra", "Number Theory"],
            "question_type": ["math-word-problem", "proof"],
            "exam_category": [
                "International Contests",
                "National And Regional Contests",
            ],
            "mathematical_techniques": [["factorization"], ["mathematical induction"]],
            "mathematical_results": [["None"], ["None"]],
        }
    )


@pytest.fixture
def searcher(sample_data):
    search_instance = MathProblemSearch()
    search_instance.fit_retriever(sample_data)
    return search_instance


def test_retriever_fitting(searcher):
    """Ensure retriever is fitted properly."""
    assert searcher.retriever_fitted is True


def test_decompose_query_valid(searcher):
    # Test a valid query decomposition
    query = (
        "Find problems with problem_type=Algebra and question_type=math-word-problem"
    )
    decomposition = searcher.decompose_query(query).decomposition
    assert isinstance(decomposition, QueryDecomposition)
    assert decomposition.valid_query
    assert decomposition.filters == {
        "problem_type": "Algebra",
        "question_type": "math-word-problem",
    }


def test_decompose_query_invalid(searcher):
    # Test an invalid query decomposition
    query = "Invalid query syntax"
    decomposition = searcher.decompose_query(query).decomposition
    assert isinstance(decomposition, QueryDecomposition)
    assert not decomposition.valid_query
    assert decomposition.reason is not None


def test_search_valid_query(searcher):
    # Test a valid search query
    query = (
        "Find problems with problem_type=Algebra and question_type=math-word-problem"
    )
    results = searcher.search(query, top_k=5)
    assert isinstance(results, dict)
    assert "results" in results
    assert "filters" in results
    assert len(results["results"]) <= 5


def test_search_invalid_query(searcher):
    # Test an invalid search query
    query = "Invalid query syntax"
    results = searcher.search(query, top_k=5)
    assert isinstance(results, dict)
    assert "results" in results
    assert "filters" in results
    assert len(results["results"]) == 0


def test_search_empty_query(searcher):
    # Test an empty search query
    query = ""
    results = searcher.search(query, top_k=5)
    assert isinstance(results, dict)
    assert "results" in results
    assert "filters" in results
    assert len(results["results"]) == 0


@pytest.mark.parametrize(
    "query, expected_filters",
    [
        ("Find problems with problem_type=Algebra", {"problem_type": "Algebra"}),
        (
            "Find problems with question_type=math-word-problem",
            {"question_type": "math-word-problem"},
        ),
    ],
)
def test_decompose_query_multiple_filters(searcher, query, expected_filters):
    decomposition = searcher.decompose_query(query).decomposition
    assert isinstance(decomposition, QueryDecomposition)
    assert decomposition.valid_query
    assert decomposition.filters == expected_filters


@pytest.mark.parametrize(
    "query, top_k, max_expected_results_length",
    [
        ("Find problems with problem_type=Algebra", 5, 5),
        ("Find problems with question_type=math-word-problem", 10, 10),
    ],
)
def test_search_multiple_top_k(searcher, query, top_k, max_expected_results_length):
    results = searcher.search(query, top_k=top_k)
    assert isinstance(results, dict)
    assert "results" in results
    assert "filters" in results
    assert len(results["results"]) <= max_expected_results_length
