import pandas as pd
import pytest

from mathlete.data_processing.infer_metadata.infer_metadata import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mathlete.data_processing.infer_metadata.infer_metadata_models import (
    MathematicalAnalysis,
    ProblemAnalysis,
)


@pytest.fixture
def sample_data():
    return pd.DataFrame(
        [
            {
                "uuid": "405ac224-e87e-5ffc-ae8b-a0418c954fc4",
                "problem": "How many positive integers have exactly three proper divisors (positive integral divisors excluding itself), each of which is less than 50?",
                "informal_solution": "Suppose $n$ is such an integer. Because $n$ has $3$ proper divisors, it must have $4$ divisors, so $n$ must be in the form $n=p\\cdot q$ or $n=p^3$ for distinct prime numbers $p$ and $q$. In the first case, the three proper divisors of $n$ are $1$, $p$ and $q$. Thus, we need to pick two prime numbers less than $50$. There are fifteen of these ($2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43$ and $47$) so there are ${15 \\choose 2} =105$ ways to choose a pair of primes from the list and thus $105$ numbers of the first type. In the second case, the three proper divisors of $n$ are 1, $p$ and $p^2$. Thus we need to pick a prime number whose square is less than $50$. There are four of these ($2, 3, 5,$ and $7$) and so four numbers of the second type. Thus there are $105+4=\\boxed{109}$ integers that meet the given conditions. ~lpieleanu (Minor editing) ~ rollover2020 (extremely minor editing)",
                "languages": ["English"],
                "non_mathematical_text": [
                    "~lpieleanu (Minor editing)",
                    "~ rollover2020 (extremely minor editing)",
                ],
            },
            {
                "problem": "For any odd prime $p$ and any integer $n$, let $d_{p}(n) \\in\\{0,1, \\ldots, p-1\\}$ denote the remainder when $n$ is divided by $p$. We say that $\\left(a_{0}, a_{1}, a_{2}, \\ldots\\right)$ is a $p$-sequence, if $a_{0}$ is a positive integer coprime to $p$, and $a_{n+1}=a_{n}+d_{p}\\left(a_{n}\\right)$ for $n \\geqslant 0$. (a) Do there exist infinitely many primes $p$ for which there exist $p$-sequences $\\left(a_{0}, a_{1}, a_{2}, \\ldots\\right)$ and $\\left(b_{0}, b_{1}, b_{2}, \\ldots\\right)$ such that $a_{n}>b_{n}$ for infinitely many $n$, and $b_{n}>a_{n}$ for infinitely many $n$ ? (b) Do there exist infinitely many primes $p$ for which there exist $p$-sequences $\\left(a_{0}, a_{1}, a_{2}, \\ldots\\right)$ and $\\left(b_{0}, b_{1}, b_{2}, \\ldots\\right)$ such that $a_{0}<b_{0}$, but $a_{n}>b_{n}$ for all $n \\geqslant 1$ ? (United Kingdom)",
                "informal_solution": "Fix some odd prime $p$, and let $T$ be the smallest positive integer such that $p \\mid 2^{T}-1$; in other words, $T$ is the multiplicative order of 2 modulo $p$. Consider any $p$-sequence $\\left(x_{n}\\right)=\\left(x_{0}, x_{1}, x_{2}, \\ldots\\right)$. Obviously, $x_{n+1} \\equiv 2 x_{n}(\\bmod p)$ and therefore $x_{n} \\equiv 2^{n} x_{0}(\\bmod p)$. This yields $x_{n+T} \\equiv x_{n}(\\bmod p)$ and therefore $d\\left(x_{n+T}\\right)=d\\left(x_{n}\\right)$ for all $n \\geqslant 0$. It follows that the sum $d\\left(x_{n}\\right)+d\\left(x_{n+1}\\right)+\\ldots+d\\left(x_{n+T-1}\\right)$ does not depend on $n$ and is thus a function of $x_{0}$ (and $p$ ) only; we shall denote this sum by $S_{p}\\left(x_{0}\\right)$, and extend the function $S_{p}(\\cdot)$ to all (not necessarily positive) integers. Therefore, we have $x_{n+k T}=x_{n}+k S_{p}\\left(x_{0}\\right)$ for all positive integers $n$ and $k$. Clearly, $S_{p}\\left(x_{0}\\right)=S_{p}\\left(2^{t} x_{0}\\right)$ for every integer $t \\geqslant 0$. In both parts, we use the notation $$ S_{p}^{+}=S_{p}(1)=\\sum_{i=0}^{T-1} d_{p}\\left(2^{i}\\right) \\quad \\text { and } \\quad S_{p}^{-}=S_{p}(-1)=\\sum_{i=0}^{T-1} d_{p}\\left(p-2^{i}\\right) $$ (a) Let $q>3$ be a prime and $p$ a prime divisor of $2^{q}+1$ that is greater than 3 . We will show that $p$ is suitable for part (a). Notice that $9 \\nmid 2^{q}+1$, so that such a $p$ exists. Moreover, for any two odd primes $q<r$ we have $\\operatorname{gcd}\\left(2^{q}+1,2^{r}+1\\right)=2^{\\operatorname{gcd}(q, r)}+1=3$, thus there exist infinitely many such primes $p$. For the chosen $p$, we have $T=2 q$. Since $2^{q} \\equiv-1(\\bmod p)$, we have $S_{p}^{+}=S_{p}^{-}$. Now consider the $p$-sequences $\\left(a_{n}\\right)$ and $\\left(b_{n}\\right)$ with $a_{0}=p+1$ and $b_{0}=p-1$; we claim that these sequences satisfy the required conditions. We have $a_{0}>b_{0}$ and $a_{1}=p+2<b_{1}=2 p-2$. It follows then that $$ a_{k \\cdot 2 q}=a_{0}+k S_{p}^{+}>b_{0}+k S_{p}^{+}=b_{k \\cdot 2 q} \\quad \\text { and } \\quad a_{k \\cdot 2 q+1}=a_{1}+k S_{p}^{+}<b_{1}+k S_{p}^{+}=b_{k \\cdot 2 q+1} $$ for all $k=0,1, \\ldots$, as desired. (b) Let $q$ be an odd prime and $p$ a prime divisor of $2^{q}-1$; thus we have $T=q$. We will show that $p$ is suitable for part (b). Notice that the numbers of the form $2^{q}-1$ are pairwise coprime (since $\\operatorname{gcd}\\left(2^{q}-1,2^{r}-1\\right)=2^{\\operatorname{gcd}(q, r)}-1=1$ for any two distinct primes $q$ and $r$ ), thus there exist infinitely many such primes $p$. Notice that $d_{p}(x)+d_{p}(p-x)=p$ for all $x$ with $p \\nmid x$, so that the sum $S_{p}^{+}+S_{p}^{-}=p q$ is odd, which yields $S_{p}^{+}=S_{p}(1) \\neq S_{p}(-1)=S_{p}^{-}$. Assume that $\\left(x_{n}\\right)$ and $\\left(y_{n}\\right)$ are two $p$-sequences with $S_{p}\\left(x_{0}\\right)>S_{p}\\left(y_{0}\\right)$ but $x_{0}<y_{0}$. The first condition yields that $$ x_{M q+r}-y_{M q+r}=\\left(x_{r}-y_{r}\\right)+M\\left(S_{p}\\left(x_{0}\\right)-S_{p}\\left(y_{0}\\right)\\right) \\geqslant\\left(x_{r}-y_{r}\\right)+M $$ for all nonnegative integers $M$ and every $r=0,1, \\ldots, q-1$. Thus, we have $x_{n}>y_{n}$ for every $n \\geqslant q+q \\cdot \\max \\left\\{y_{r}-x_{r}: r=0,1, \\ldots, q-1\\right\\}$. Now, since $x_{0}<y_{0}$, there exists the largest $n_{0}$ with $x_{n_{0}}<y_{n_{0}}$. In this case the $p$-sequences $a_{n}=x_{n-n_{0}}$ and $b_{n}=y_{n-n_{0}}$ possess the desired property (notice here that $x_{n} \\neq y_{n}$ for all $n \\geqslant 0$, as otherwise we would have $\\left.S_{p}\\left(x_{0}\\right)=S_{p}\\left(x_{n}\\right)=S_{p}\\left(y_{n}\\right)=S_{p}\\left(y_{0}\\right)\\right)$. It remains to find $p$-sequences $\\left(x_{n}\\right)$ and $\\left(y_{n}\\right)$ satisfying the two conditions. Recall that $S_{p}^{+} \\neq S_{p}^{-}$. Now, if $S_{p}^{+}>S_{p}^{-}$, then we can put $x_{0}=1$ and $y_{0}=p-1$. Otherwise, if $S_{p}^{+}<S_{p}^{-}$, then we put $x_{0}=p-1$ and $y_{0}=p+1$. This page is intentionally left blank",
                "languages": ["English"],
                "mathematical_techniques": [
                    "Direct proof",
                    "Modular arithmetic",
                    "Divisibility tests",
                    "Variable substitution",
                ],
                "mathematical_results": ["Multiplicative order", "GCD computation"],
                "non_mathematical_text": ["This page is intentionally left blank"],
                "uuid": "d3f8731b-51c8-87c-8e29-1eb67c0db32b",
            },
        ]
    )


@pytest.fixture
def analyzer():
    return ProblemAnalyzer()


def test_analyze_mathematical_content(analyzer, sample_data):
    for _, row in sample_data.iterrows():
        result = analyzer.analyze_mathematical_content(
            row["problem"], row["informal_solution"]
        )
        assert isinstance(result, MathematicalAnalysis)
        assert result.languages == row["languages"]


def test_extract_non_mathematical_text(analyzer, sample_data):
    for _, row in sample_data.iterrows():
        result = analyzer.extract_non_mathematical_text(row["informal_solution"])
        assert isinstance(result.non_mathematical_text, list)
        assert result.non_mathematical_text == row["non_mathematical_text"]


def test_analyze_problem(analyzer, sample_data):
    for _, row in sample_data.iterrows():
        result = analyzer.analyze_problem(row["problem"], row["informal_solution"])
        assert isinstance(result, ProblemAnalysis)
        assert result.languages == row["languages"]
        assert result.non_mathematical_text == row["non_mathematical_text"]


@pytest.mark.parametrize(
    "input_text,expected",
    [
        ("Solution by Alice", ["Solution by Alice"]),
        ("Standard proof", []),
    ],
)
def test_non_math_text_extraction_parametrized(analyzer, input_text, expected):
    result = analyzer.extract_non_mathematical_text(input_text)
    assert result.non_mathematical_text == expected
