uuid,query,query_decomposition,results,problem_position
01110e10-7321-5fab-8c12-7bf02e840f2a,problems using consecutive integer sum techniques,filters={} text_search='consecutive integer sum techniques' valid_query=True reason='',"['47ca2c5f-ab87-555d-b9d8-14a97006ec41', '69647e92-cd73-5fb0-8e6e-d8bef107c5c4', '7d1674f4-e503-5b63-80ca-3b1f183022a8', '5af70549-622d-58b1-a17e-a447ed11b9c7', 'ffe8424b-1c83-515c-aaa0-d66da2619a59', '7f100bae-027a-5e63-9927-aff571c5573d', '7faba3f1-a870-5d48-aa20-1d65d498cfff', '6f941785-eee5-5e37-b7a6-1830407c023e', 'c81c873a-ee91-50f1-b6a4-c2b0e5faaef6', '5ea1cc0d-27a2-5cc3-a995-ec68a0415890']",-1
01110e10-7321-5fab-8c12-7bf02e840f2a,problems on consecutive integer sums,filters={} text_search='consecutive integer sums' valid_query=True reason='',"['7d1674f4-e503-5b63-80ca-3b1f183022a8', '47ca2c5f-ab87-555d-b9d8-14a97006ec41', '83aa3af0-fb56-52b6-bc33-85c1290858fc', '7f47ca12-50e9-5704-a3ac-baff08a64a01', '5af70549-622d-58b1-a17e-a447ed11b9c7', '940d66f0-85a5-5d3e-a762-8681a9694d8c', 'ffe8424b-1c83-515c-aaa0-d66da2619a59', '6f941785-eee5-5e37-b7a6-1830407c023e', '12664de2-6cd8-5639-b6da-39df96e0876f', '69647e92-cd73-5fb0-8e6e-d8bef107c5c4']",-1
01110e10-7321-5fab-8c12-7bf02e840f2a,solving problems using arithmetic sequence properties,filters={} text_search='solving problems using arithmetic sequence properties' valid_query=True reason='',"['7f5014b6-a103-533a-b2b6-01fc8962511d', '81325d2e-e5ae-55dd-8171-a032e21eba26', 'e741ccbc-e1fd-54e3-bb94-eeb0f4c538ab', 'c78bf3ac-3b47-522f-84e2-d80649f619b2', 'e57a5f60-86c0-5392-8378-26eb6d13fa87', '24c75cca-8c6f-5e0e-97ff-7d77c144098f', '85e63a5d-a598-5c02-808a-9ae4b63c91ac', 'e48c704c-e55d-5e10-951d-fce0f979bdb2', '613cd60d-faee-5676-8770-5df06c229a19', '1a81bfe9-09fb-591b-92a4-9cea1e7fa988']",-1
01110e10-7321-5fab-8c12-7bf02e840f2a,sets with specific integer differences,filters={} text_search='sets with specific integer differences' valid_query=True reason='',"['47e66581-0b42-5bf7-a3e1-8f362c39dc0f', '7b3521b6-b4b9-5bf9-a620-6f6030bf0afe', '178c7166-00ec-50cc-a629-195aed743739', 'c2848a19-5382-5399-8502-9e84050fb9f6', '502e3c6d-0a6f-5c2d-8912-8c944b32771b', 'b19bde7e-bf4f-5f1d-a9d9-b0646f6d2d50', 'bc890959-1a96-5154-846e-3b14e84ef4d2', '44d7216a-d3e3-530c-9350-9d39ac962557', '04f1b45c-0541-533a-a7a6-3712264d91b9', 'fa616211-2062-5e30-b3bf-1d8260fd9fd4']",-1
06f83365-ada4-5979-9513-f3a48545a9e8,simplifying fractions with reciprocals,filters={} text_search='simplifying fractions with reciprocals' valid_query=True reason='',"['d3ea00a1-48b2-5808-85c0-96e73e21a91c', 'f5543c0c-2979-537c-942c-f20ce5d3d87c', '3d81717b-1153-5bc2-a0ee-9da2515f4ba0', '2e5af875-f56d-5a1a-8c69-1c5b7829e2a1', '77d438ad-4203-5b74-a77c-c08092a9c5f5', '2e0bb6d0-0e35-5b59-a059-41124a8b8e15', '6afca35e-f744-55ea-98bd-88fea106cf72', '8cf171b5-b26c-5e28-9957-1800f8b46855', '06f83365-ada4-5979-9513-f3a48545a9e8', '05d55f6d-3cfc-596d-ad62-4f053cfdacb1']",9
06f83365-ada4-5979-9513-f3a48545a9e8,how to find the reciprocal of a sum,filters={} text_search='reciprocal of a sum' valid_query=True reason='',"['d3ea00a1-48b2-5808-85c0-96e73e21a91c', 'c8eac8ec-382d-56c6-847f-96a1f3749d1a', '45e425b4-3d4b-5c25-b922-7a6867af9b56', '77d438ad-4203-5b74-a77c-c08092a9c5f5', '8863011d-c1f0-5541-a1b6-c9d62fea1325', 'fc01e929-0ded-5732-a041-4ae5ea753574', '06f83365-ada4-5979-9513-f3a48545a9e8', '35f6682d-5864-5c2f-be1a-5456efbb1f74', '6bb65a02-91f9-59f9-a47c-1eb502f4b332', 'caef242f-2f87-5191-810a-55b36aea34d8']",7
06f83365-ada4-5979-9513-f3a48545a9e8,fraction and reciprocal problems,filters={} text_search='fraction and reciprocal problems' valid_query=True reason='',"['d3ea00a1-48b2-5808-85c0-96e73e21a91c', '3d81717b-1153-5bc2-a0ee-9da2515f4ba0', '82f26bd9-6f81-5479-961f-08d3d6b03fa1', '06f83365-ada4-5979-9513-f3a48545a9e8', '8cf171b5-b26c-5e28-9957-1800f8b46855', '49737f41-e2bd-52ce-8d64-d97749ec31d2', 'bbd090b3-f07b-5788-9c26-6f7aa82a4f11', 'c319631a-28d8-54cf-ba6d-1635fafabb09', '282cea33-7b55-58bd-933e-aa1f028853da', '05369bf9-74d4-5cdf-bd92-7d569d34ae59']",4
06f83365-ada4-5979-9513-f3a48545a9e8,reciprocal math problems,filters={} text_search='reciprocal' valid_query=True reason='',"['d3ea00a1-48b2-5808-85c0-96e73e21a91c', '06f83365-ada4-5979-9513-f3a48545a9e8', '82f26bd9-6f81-5479-961f-08d3d6b03fa1', '05369bf9-74d4-5cdf-bd92-7d569d34ae59', '6bb65a02-91f9-59f9-a47c-1eb502f4b332', '3d81717b-1153-5bc2-a0ee-9da2515f4ba0', '34612e83-a0f2-5295-954c-8bd95c092b07', '8c1a91ee-a2fe-5407-9f31-a5d207fcee71', 'bbd090b3-f07b-5788-9c26-6f7aa82a4f11', '7bbce5ab-871b-5bbf-a445-4c4881db0202']",2
19c06115-86c2-5ef9-ac88-81ebfd00be7e,equations involving exponents and roots,filters={} text_search='equations involving exponents and roots' valid_query=True reason='',"['9d5bb718-c314-5912-a14a-e13be3d37cb7', 'd0da673d-f40f-50a0-8f82-51de26989312', '6e796a99-d2da-5804-8c43-c73f709dd07d', 'ea31b2fb-5d08-50ee-b23d-baed4542c4fe', 'e850846a-b509-5cac-ac0a-a1a19ccfbe6d', 'cac85e0e-1c5b-5ece-b88b-99cab24a44f6', '303855ed-5724-552f-9135-0a0dadd13f02', '11b127b9-640e-5631-ad2f-3d68645ddb39', 'bd21c030-85ac-5bef-a874-9fc64064d09f', 'c539f2f2-bc7b-5f8a-befe-4a5b0891a1e2']",-1
19c06115-86c2-5ef9-ac88-81ebfd00be7e,using change of variables in equations problems,filters={'problem_type': 'Algebra'} text_search='change of variables in equations' valid_query=True reason='',"['e503ebcb-4341-5253-a8ab-d39ec09cefa5', '9a7569b0-be50-5ebb-b385-030c19d710f5', '5b39fd56-106e-5acd-a456-7a75364d23b6', '8de1d1dd-1734-5895-aa07-2990fe338f1f', 'ef370e6f-ecef-5feb-b31c-c02e9fb24fd3', 'e140f5ff-c904-5b63-8ac5-2a6de9a48f95', 'f1d0fa13-8041-5b66-8a87-633b4a1f3830', 'b526d4b4-4f94-5f7e-b0d0-5e19ec1acc74', '17a2a903-a841-5ec7-a756-38a9882bbdb0', '6cdaca2f-25ef-5a38-9ab7-4879c2fb15db']",-1
19c06115-86c2-5ef9-ac88-81ebfd00be7e,vieta's formulas root equations problems,"filters={} text_search=""vieta's formulas root equations problems"" valid_query=True reason=''","['efe29293-05c3-590c-8a69-6ac2479a44d7', '25b4a354-e3d8-513e-b48c-f9419fb7c2d6', 'd47a5a23-6be0-54ee-a508-39cbcd216558', '1a18a9a8-7850-5f7a-b169-1fc8bd14be45', '982fcfdf-fd73-525f-a649-df06a8613921', '7631bb8f-e3e1-54d1-934c-1b6ce1a4965b', '62ef8611-afc1-5a04-a461-49d4e56f2eea', 'f557d384-1d6e-5b02-bc51-ea35c9e72407', '745791df-6e04-5a4b-9316-c28ebe59440c', '87cedfa3-f938-5dd6-a842-210b1abeaa09']",-1
19c06115-86c2-5ef9-ac88-81ebfd00be7e,problems on real roots of equations,filters={} text_search='real roots of equations' valid_query=True reason='',"['096fbdee-8e33-5dde-98ec-43b8ddd31c94', '8d7d6285-3d25-5e52-9714-580574e1d807', '982e0744-4d73-523b-a5e8-d5e0bd5a93bb', '7ffb905b-56e6-53a1-9c35-93f73f8ae75e', 'ae7f074e-f3c3-5f1e-ae3a-b575163b41e5', 'd575834b-f9f3-5979-b830-cb9bf74556a3', '40ea41c5-d848-547f-9c3c-b0c8b6b8ccb6', '755b0ff5-5e65-5a03-b5c5-413a666a41ae', '38f8e792-c96c-5ee2-b0a4-4ca0ecba42b5', 'bd21c030-85ac-5bef-a874-9fc64064d09f']",-1
26838677-79f9-522d-aa75-7b613beedbd0,infinit series questions involving sums,filters={} text_search='infinit series questions involving sums' valid_query=True reason='',"['ae818283-fb35-512d-8f82-e92d553aba1a', '6a08e111-8604-5afc-84a6-bf9dccc32661', '6e796a99-d2da-5804-8c43-c73f709dd07d', '1c04a9ae-82c8-5c6b-b981-a06e0b252a6d', '5f0e7213-de6e-5e5b-9d5d-a97d45a6849c', '26838677-79f9-522d-aa75-7b613beedbd0', 'e84ea3b4-dc99-5672-b296-37d7fc051f80', '0ba5e01e-acd6-502c-9e75-1c8262b59c0f', 'ba5429b8-d956-5f10-8063-93c18360bf80', 'ce3703eb-3f5f-54e4-9aa6-83453e2ca965']",6
26838677-79f9-522d-aa75-7b613beedbd0,geometric series problems,filters={'problem_type': 'Geometry'} text_search='series' valid_query=True reason='',"['026d532e-ca94-53fa-a7fb-a4ffaede605a', '9368c8c1-45b9-5628-a2f2-e9956e1767ff', '6e796a99-d2da-5804-8c43-c73f709dd07d', '35f6682d-5864-5c2f-be1a-5456efbb1f74', '30453ec4-2607-5cc7-a000-a8501a4694c6', 'fd757c39-3929-50f1-99e8-81c6a6caa0ee', '3e24508f-cf2c-502b-a1ea-d94a0c2e1dc9', 'a489e1cf-5dbc-5239-b04f-6d421afa0a54', '004a8a74-3628-5b31-a4d2-b9a893de0aa8', 'a409f471-6cd1-5c6e-a054-68eb0d44a9e3']",-1
26838677-79f9-522d-aa75-7b613beedbd0,summation technique in series,filters={} text_search='summation technique in series' valid_query=True reason='',"['f1d0fa13-8041-5b66-8a87-633b4a1f3830', '004a8a74-3628-5b31-a4d2-b9a893de0aa8', 'a6dbe2a4-7bd6-517e-bd24-b97cdd0fe276', '026d532e-ca94-53fa-a7fb-a4ffaede605a', '7f5014b6-a103-533a-b2b6-01fc8962511d', '45e425b4-3d4b-5c25-b922-7a6867af9b56', '1a0125f9-a959-5da9-9955-66acab7b590a', 'feb0f4e2-c8eb-558f-91f0-8df8314144d8', 'c6ce15b0-a7d9-5bd7-8c46-2285cde9ff54', 'dda9c6b3-eab5-51f2-bf83-3003db716769']",-1
26838677-79f9-522d-aa75-7b613beedbd0,technques for finding sum of odd and even series separately,filters={} text_search='techniques for finding sum of odd and even series separately' valid_query=False reason='Query is more about general mathematical techniques and exploration rather than specific problem dataset criteria.',"['42730bdd-73c2-5906-88a4-6d1d23b60086', '6e796a99-d2da-5804-8c43-c73f709dd07d', 'f94721b4-4085-5043-bdc3-3ef976cd29cb', '64a35535-1f20-5461-b4d6-08dd2bab26cf', '1bcd1444-b7eb-5b55-a587-108645bcf498', 'e0d91051-04a8-5e55-8ee6-cc58736c29ec', '0a0d7ef1-9e0b-5ba7-be60-bcd1ecf81cc2', '25bf8799-b96a-5c32-9988-855fcbfd9b0f', '56221f18-833f-59c2-b1d6-9b8bc870ab9d', 'a6dbe2a4-7bd6-517e-bd24-b97cdd0fe276']",-1
350d6834-3231-5d23-89e9-c7dc0f3fde0b,Problems involving function properties,filters={} text_search='function properties' valid_query=True reason='',"['df05f507-a5c2-53fe-8887-303deef215a2', '15c56e3d-2a0d-5043-b238-d5a084348d3c', 'ad87bfa3-c0fb-5eb6-b97c-e9e6ae8cd285', '6db019b1-708c-5ef4-a860-bad9b71a4807', '098929d4-9886-5765-9fb7-128debc353d9', 'b8342aa7-85fc-58b3-af2a-65372001f768', 'eb8281ab-886f-5520-a607-a04314795687', '7f66be2c-8966-5949-8702-7bcc0358066f', '9a445e92-7566-5386-8027-2fe62843df17', '0df2c23d-c02a-50eb-b41f-82aabe98a196']",-1
350d6834-3231-5d23-89e9-c7dc0f3fde0b,Techniques for analyzing function simplicity,filters={} text_search='Techniques for analyzing function simplicity' valid_query=False reason='Query is not directly related to searching for a mathematical problem within the specified dataset fields.',"['1a590189-219e-5a37-9d67-a3b5e9ee4623', 'b8342aa7-85fc-58b3-af2a-65372001f768', '3ea11fa4-a7be-559d-8c60-ac1f4e63a624', 'fb84d4bd-8f31-5563-99e8-258efb0db836', '759429e3-b57c-5ebf-b543-d357c5d96553', '51c234af-972e-5328-9bf6-4e984b6c0b31', '098929d4-9886-5765-9fb7-128debc353d9', 'a458ddc7-2d86-5bb0-898d-183df37be274', '2ff18b5a-26c0-5d98-99d1-4854ba747a66', '57515908-0e15-5be9-a4f7-4cf05d470093']",-1
350d6834-3231-5d23-89e9-c7dc0f3fde0b,Functional equation problems,filters={} text_search='Functional equation problems' valid_query=True reason='',"['c2f4182e-97a5-5501-9e9c-2fe1a992ee46', '2af90b8d-e7f8-507f-900a-8f7992e4bf75', '5cfbe343-b06d-5fce-83c8-141f9db35588', 'aa4309a5-4c67-563d-9d0b-723e7b674680', '9c2ce03e-8910-58d3-9c76-b80ba46c0959', 'fa9a8040-b289-56b2-951d-20fe0e4fc10c', '0df2c23d-c02a-50eb-b41f-82aabe98a196', 'ff1eb9a8-2e7d-57bf-9e6f-b79d619ce5a2', 'aabaf4c9-bf57-555f-bd53-f3ca8c2f90ba', 'a246c5a4-e7d1-53e7-afbe-23991153f6e1']",-1
350d6834-3231-5d23-89e9-c7dc0f3fde0b,Methodology for solving functional equations,filters={} text_search='Methodology for solving functional equations' valid_query=True reason='',"['1e7e5ee0-cf0d-5d8c-b1ea-771fbc5117b2', 'c2f4182e-97a5-5501-9e9c-2fe1a992ee46', '5cfbe343-b06d-5fce-83c8-141f9db35588', 'aabaf4c9-bf57-555f-bd53-f3ca8c2f90ba', 'ff1eb9a8-2e7d-57bf-9e6f-b79d619ce5a2', 'a3b92d1e-5cb1-5deb-b40a-8c99cbac07ba', 'd7a97d93-d0b2-555f-8530-3e7e3f0c4012', '5b5988e9-e158-5484-8367-85c040e888a9', '3f28f215-ad37-5f77-b503-5c3cf3cc6ffe', 'a246c5a4-e7d1-53e7-afbe-23991153f6e1']",-1
405ac224-e87e-5ffc-ae8b-a0418c954fc4,how to count integers with specific divisors using combinatorial,filters={'problem_type': 'Combinatorics'} text_search='count integers with specific divisors using combinatorial' valid_query=True reason='',"['8ee739ec-010b-580b-8377-d8e99674db1c', 'a25cfb80-09e0-54b5-af1f-fa43b9d786b3', 'd562e371-4c44-538e-859e-49a13edd0a43', '11994b38-14cb-5209-a412-c1cb8dcd3b44', '5a78eef6-b6db-5e7b-9a88-e1b698112d21', 'caf4eb0d-8079-5a9b-84b3-51e0cd4aeb9e', '7489e051-b0a0-52b1-97d4-ebff90d24955', 'a6dbe2a4-7bd6-517e-bd24-b97cdd0fe276', 'e79477db-0a37-527c-9dd4-d8ac2176ad01', '985832d0-a242-5ad4-bd07-9e6b60b93dae']",-1
405ac224-e87e-5ffc-ae8b-a0418c954fc4,finding integers based on divisor conditions,filters={} text_search='finding integers based on divisor conditions' valid_query=True reason='',"['78f6c5a8-ab8c-591f-abf5-455d7188b0b6', 'e13a8fd1-850a-593d-a874-d7d99d7438de', 'aaf26a33-232b-5bff-9e64-2bb59352b5ac', 'c5664114-c0ff-5e14-8ebf-eec6f9b8ea08', '5597ce3b-b988-5a5e-8eec-63787cba9aed', 'ea3f3f8d-2c9f-52c7-9d4e-85f3e0737dd8', '8fbe9093-0fbc-53b0-8e44-74f05c9b22e1', 'a25cfb80-09e0-54b5-af1f-fa43b9d786b3', '37d880cd-27a6-56c3-bdd1-55a407ebff8d', '7489e051-b0a0-52b1-97d4-ebff90d24955']",-1
405ac224-e87e-5ffc-ae8b-a0418c954fc4,integer problems with limited divisors,filters={} text_search='integer problems with limited divisors' valid_query=True reason='',"['7489e051-b0a0-52b1-97d4-ebff90d24955', 'cb01d2db-770a-5451-abc3-0bc987411dfd', '36bce896-2ceb-5c14-a85d-3ceebed32e90', 'd5a4e2fc-0d38-539e-9afa-ce74d42369d7', 'ec4d5177-e9e6-5d7d-8313-ca5e16c928ad', '1e6a32e9-304b-5677-9b1b-dcc363487bab', '507bdc42-cc1b-5f18-a826-67bbaa6bb373', '98634b9e-dd7d-5407-b14f-181085322a0f', 'cbff9ffc-b730-514b-83a0-8873f5d2f3e0', '19d2d154-feca-59ae-8b0b-02aaf59be4b8']",-1
405ac224-e87e-5ffc-ae8b-a0418c954fc4,problems on divisors of integers,filters={'problem_type': 'Number Theory'} text_search='divisors of integers' valid_query=True reason=None,"['19d2d154-feca-59ae-8b0b-02aaf59be4b8', 'a04e32c3-0f72-5289-8003-694771788eba', '7489e051-b0a0-52b1-97d4-ebff90d24955', '9e367d1c-da92-59ac-a849-c3d22665bd77', 'ec4d5177-e9e6-5d7d-8313-ca5e16c928ad', '36bce896-2ceb-5c14-a85d-3ceebed32e90', 'ce55699b-39dd-5641-a791-0509e4243894', 'e9e4fe91-6387-5f5c-bc5f-b7dc581c8bb2', '9040f237-5c97-5091-8fca-1d0b8a83c831', 'ed6d5dd5-c52c-5bae-9fa2-5e8342e5957d']",-1
4a8ea74e-ffd0-580f-a427-b7c7ea67e534,solving equations with polynomial division,filters={} text_search='solving equations with polynomial division' valid_query=True reason='',"['6b74a3d5-e5e8-5048-940d-d3ff7eb9b024', '7f6846a6-d9e8-5daa-952e-7612d52e7220', '4bab0c1e-5987-5006-99e7-15dcd001592c', '304a5feb-cb4c-5c87-98ce-9dffca3b16a8', 'fb8e3f8b-1751-53d4-bfcf-563f29ecb4ff', 'b8f6a049-ca7d-527d-8610-cce9fccacd67', '87a2708c-369d-578d-8e76-2d4c15d24f4d', 'e197a8af-7bf8-5ecc-96e4-9007798866fc', 'f255adb6-7edd-5aa1-9ce4-260582afcb67', 'ec0d3c75-eccc-5d4b-bf8b-ca3239c4c417']",-1
4a8ea74e-ffd0-580f-a427-b7c7ea67e534,common root identification in cubic equations,filters={} text_search='common root identification in cubic equations' valid_query=True reason='',"['94579e4a-6b00-54f5-a011-df2039ee9dc1', 'd4f35528-8f1b-5925-8f65-1a703f959197', '272cc322-d72c-5e5a-9eff-fd2e4f348946', '04b31253-5dde-5ac1-a8fc-80b8bf8114c7', '9eb2ddc1-4d95-53e9-b471-67e4f7eb7baf', 'ae7f074e-f3c3-5f1e-ae3a-b575163b41e5', 'dc0a3748-341c-531d-a1a4-f30a7898d1ab', 'd370c407-957b-53ec-b80d-d6cb7ef19867', '34981a97-6ecb-5d9c-8c0a-441e8d89d4d8', 'dcf18c3a-0eb0-5355-96ef-70769990dfb2']",-1
4a8ea74e-ffd0-580f-a427-b7c7ea67e534,common roots in polynomial equations,filters={} text_search='common roots in polynomial equations' valid_query=True reason='',"['efe29293-05c3-590c-8a69-6ac2479a44d7', '7cf1c36a-a268-5fbe-bf35-d8bc60f87e8e', '94579e4a-6b00-54f5-a011-df2039ee9dc1', '096fbdee-8e33-5dde-98ec-43b8ddd31c94', 'd4f35528-8f1b-5925-8f65-1a703f959197', 'ae7f074e-f3c3-5f1e-ae3a-b575163b41e5', '13e33285-cec0-535b-8e11-102b937ec26f', '744b2318-d5b5-5538-a068-04e9bd993766', '25b4a354-e3d8-513e-b48c-f9419fb7c2d6', '982e0744-4d73-523b-a5e8-d5e0bd5a93bb']",-1
4a8ea74e-ffd0-580f-a427-b7c7ea67e534,cubic polynomial equations with shared roots,filters={} text_search='cubic polynomial equations with shared roots' valid_query=True reason='',"['011f3537-705e-510c-9de5-4e4af1352e94', 'b71b0cf5-9d39-5907-a286-a82d87500f3b', '94579e4a-6b00-54f5-a011-df2039ee9dc1', '46747e91-c134-5705-b140-2c13c85c76fb', 'fb4b6e9f-cb4e-5ae4-af47-e573b48ba9aa', 'd4f35528-8f1b-5925-8f65-1a703f959197', '34981a97-6ecb-5d9c-8c0a-441e8d89d4d8', 'd370c407-957b-53ec-b80d-d6cb7ef19867', '88937e87-d510-52d9-ba22-75a8b28854ff', '7cf1c36a-a268-5fbe-bf35-d8bc60f87e8e']",-1
6c83a30c-82a6-5060-ad42-546860b1ced8,logarithms progressions problem simplification,filters={} text_search='logarithms progressions problem simplification' valid_query=True reason='',"['937d5d0c-0204-51df-8f41-b4bb9d8e126f', 'dd868f26-751e-55e0-a1e1-c6dd3689f2f7', '314da220-f91f-5c3e-a04d-81f724572a69', '99a940aa-5582-579d-8f1d-e4a1306a5ba7', '6c83a30c-82a6-5060-ad42-546860b1ced8', '529ce064-9a2a-5ecd-a2a9-42a6fadaa27b', '9a1fb1f1-eee8-54dc-b0f9-f7f1e60a8c70', '9681fb41-019d-5ff6-ac2b-29b06511b8ca', 'ea0b69a6-6c9c-5397-85f0-52911c08b800', '445059b3-aa1d-564a-9a09-37b0a484db9b']",5
6c83a30c-82a6-5060-ad42-546860b1ced8,arithmetic sequence problems with logarithms,filters={} text_search='arithmetic sequence problems logarithms' valid_query=True reason='',"['6c83a30c-82a6-5060-ad42-546860b1ced8', 'c9586494-aec1-5784-a3f8-fb24d4a92656', '445059b3-aa1d-564a-9a09-37b0a484db9b', '9681fb41-019d-5ff6-ac2b-29b06511b8ca', '7c84c536-d4d3-5586-8b7e-ef40c93805e0', '86720195-fdba-5b38-a653-3797217aa667', '8de1d1dd-1734-5895-aa07-2990fe338f1f', '7311f010-f545-5af3-8011-ef812fb09db6', 'd40078ca-0619-5798-8c37-11bcefbe363c', 'ebf13f28-03e0-52d7-a516-d4bacdcdde7c']",1
6c83a30c-82a6-5060-ad42-546860b1ced8,logarithmic arithmetic progression questions,filters={} text_search='logarithmic arithmetic progression questions' valid_query=True reason='',"['6c83a30c-82a6-5060-ad42-546860b1ced8', 'c713092a-099a-5ebd-acbf-6c1322587bef', 'af7d6a36-9e02-54bd-865c-a6e0c183c092', '5ff14a05-c3c4-5a96-97e1-0a25d2ddae3c', 'f9e6992e-cfb2-55dd-8175-d5af3fe3585e', '11b127b9-640e-5631-ad2f-3d68645ddb39', '1c04a9ae-82c8-5c6b-b981-a06e0b252a6d', 'd40078ca-0619-5798-8c37-11bcefbe363c', '7c84c536-d4d3-5586-8b7e-ef40c93805e0', 'bf183159-71c7-53bf-8974-9fda0562014f']",1
6c83a30c-82a6-5060-ad42-546860b1ced8,how to solve problems with log arithmetic sequences,filters={} text_search='log arithmetic sequences solve' valid_query=True reason='',"['6c83a30c-82a6-5060-ad42-546860b1ced8', 'ef3ae19e-d30e-5043-bc7a-8167f2596e4f', '9681fb41-019d-5ff6-ac2b-29b06511b8ca', 'cd1407ba-ee59-5381-acd3-44893d744b0c', '048c3cd5-22d3-5fb7-adbd-dff2040e221d', '773997c7-20dc-52e0-8565-042711bb093a', '35c9396f-9ee8-5563-bc06-80cebf1796fe', '7c84c536-d4d3-5586-8b7e-ef40c93805e0', 'dfbfd55d-05c2-54ec-9827-1e2924673d93', 'f9e6992e-cfb2-55dd-8175-d5af3fe3585e']",1
71d9f19c-6c01-56b1-bd01-91e8567ed656,trigonometry substitution problem,filters={} text_search='trigonometry substitution problem' valid_query=True reason='',"['4cb9e37f-84cf-596b-91ed-302c9cbbb655', '1be405c9-6db2-527a-b8b2-da3af2512cda', 'b285666e-1a6f-5184-98a6-859185673241', 'a64e3533-daae-569e-b59e-1c2099f71128', '7f878932-3585-5c21-a896-f4f52c6b419d', 'a703f659-2d2e-5e00-bd94-d31c82a871c7', 'cf982eca-5773-5f79-b233-a8fbfcc51b26', 'd146ca4f-41ba-5101-8361-9b02da989548', 'f624aeff-9482-51bb-8c47-69563791b46b', 'bc890959-1a96-5154-846e-3b14e84ef4d2']",-1
71d9f19c-6c01-56b1-bd01-91e8567ed656,simplifying trigonometric expressions,filters={} text_search='simplifying trigonometric expressions' valid_query=True reason='',"['0694d164-844e-5e3f-83f6-f1531966369e', 'f5543c0c-2979-537c-942c-f20ce5d3d87c', '9cbf5853-6d66-5b0b-9c1c-da4521059155', 'a8187d9b-2e43-594f-ba21-e638040b608a', '07686ebf-77cf-52ab-bc4c-6c3bdcd1e6bb', '1b0854cd-c4e0-5293-93a4-32f932a73479', '8af85421-a00f-5eda-88fe-60ed0b2a9036', '44450537-c236-58d7-9a40-a1afa0c032d2', 'ed4ea8c0-eac8-50b6-9179-a237cfcf8715', 'f5a9005d-81d1-5d28-9d10-aff0606b1c5a']",-1
71d9f19c-6c01-56b1-bd01-91e8567ed656,basic trigonometry problems high school,filters={} text_search='basic trigonometry problems high school' valid_query=True reason='',"['2d664ac4-9bb7-5715-8a50-f41d43b1335a', '9ad38378-d78e-5bc5-81b5-515fd54f65c8', 'e32dc9a2-dede-5c26-ba6a-321ca0169e25', 'a8187d9b-2e43-594f-ba21-e638040b608a', 'c92e6cc4-0b35-5777-90a2-b49c73c02f8d', '44450537-c236-58d7-9a40-a1afa0c032d2', 'd59f8c9f-7374-5062-a8b3-388864deefe8', 'b8342aa7-85fc-58b3-af2a-65372001f768', 'dbd271c9-7769-5b82-aabf-21be5056817d', '069e463f-8f92-5ef3-9a9a-92874742ce9e']",-1
71d9f19c-6c01-56b1-bd01-91e8567ed656,tan alpha equals one problems,filters=None text_search='tan alpha equals one problems' valid_query=True reason='',"['acc92eae-6e82-509e-8ced-c36aafe48b40', 'd0c4d578-c4af-5760-9aed-dff9829f29f9', '74d8002c-3a96-54b9-8583-9838c3b714a7', '71e02008-deae-5ce9-b3ec-3291fad9141d', '71d9f19c-6c01-56b1-bd01-91e8567ed656', 'a53d5a58-3b09-5a34-9354-79417b457c00', '7baa7590-2cbf-5365-a573-6e70d7b74c4e', '57925ce8-8275-5e1f-b192-0ed0ecf1d35f', 'dfd85665-13fc-5f94-bc50-a4220fb77fa4', '7c5427fa-34ab-55de-b983-2aede75221d3']",5
7401e65d-8426-5be0-a1b6-8931021ed848,Problems involving arithmetic sequences with geometric properties,filters={} text_search='arithmetic sequences geometric properties' valid_query=True reason='',"['a7d4bf25-dc64-5495-afac-d7c41e71e7dd', 'f122954b-7fed-587c-a7b1-b21e0d2458e1', '7401e65d-8426-5be0-a1b6-8931021ed848', '54459190-edee-545c-ac4f-afded3032ecf', '3a396a04-19a0-54b8-9015-590752c59cac', '412a9678-22a0-5ec8-a7e2-7fded5c0a41a', '86720195-fdba-5b38-a653-3797217aa667', '43cbe160-f5c0-527c-a779-a8d0dc77c50d', '613cd60d-faee-5676-8770-5df06c229a19', 'c713092a-099a-5ebd-acbf-6c1322587bef']",3
7401e65d-8426-5be0-a1b6-8931021ed848,Finding terms of arithmetic sequences forming geometric sequences,filters={} text_search='terms of arithmetic sequences forming geometric sequences' valid_query=True reason='',"['7401e65d-8426-5be0-a1b6-8931021ed848', 'a7d4bf25-dc64-5495-afac-d7c41e71e7dd', 'f122954b-7fed-587c-a7b1-b21e0d2458e1', '412a9678-22a0-5ec8-a7e2-7fded5c0a41a', '43cbe160-f5c0-527c-a779-a8d0dc77c50d', '54459190-edee-545c-ac4f-afded3032ecf', '613cd60d-faee-5676-8770-5df06c229a19', '86720195-fdba-5b38-a653-3797217aa667', 'e50e4ef4-a0f4-525d-b2b0-4603b7f19df8', '5e89931d-4390-5aa1-a670-97d46348cee9']",1
7401e65d-8426-5be0-a1b6-8931021ed848,Finding general terms using sequence properties,filters={} text_search='Finding general terms using sequence properties' valid_query=True reason='',"['bed890fc-2be6-5924-837d-4136edd01029', '613cd60d-faee-5676-8770-5df06c229a19', '81325d2e-e5ae-55dd-8171-a032e21eba26', 'a7d4bf25-dc64-5495-afac-d7c41e71e7dd', '7401e65d-8426-5be0-a1b6-8931021ed848', '4852725b-73d8-55ef-af3a-a02c3868f1ab', 'a2076188-8c3d-5f9b-8e04-06714eb147c7', '14abca7a-1ac1-5b8c-b482-3e56149c10d9', '1d3469b4-c257-5b4c-a619-fcbbf1086893', '2c63bd83-9b0f-5e3d-b2da-200d41fa3a9f']",5
7401e65d-8426-5be0-a1b6-8931021ed848,Solving arithmetic and geometric progression problems,filters={} text_search='arithmetic geometric progression problems' valid_query=True reason='',"['c713092a-099a-5ebd-acbf-6c1322587bef', 'bef00f51-88e3-55fc-841d-a49557b40e63', 'f7165df8-1501-5b32-8d66-f43623a8ab66', '3a396a04-19a0-54b8-9015-590752c59cac', '5ff14a05-c3c4-5a96-97e1-0a25d2ddae3c', 'eeef7966-8355-50b9-93f0-56244c02cfb2', 'bf183159-71c7-53bf-8974-9fda0562014f', 'fc0535cc-8315-5639-ad57-507e0f27e5e4', 'b6e159c9-f50a-5304-abf2-d7082a757431', '6c83a30c-82a6-5060-ad42-546860b1ced8']",-1
7c11a2d3-3a6b-5a27-95b4-a5614a070592,Sum of consecutive odd numbers problems,filters={} text_search='Sum of consecutive odd numbers problems' valid_query=True reason='',"['bca86399-de3a-5ef1-bd0f-e7f547f332b4', '7d1674f4-e503-5b63-80ca-3b1f183022a8', 'b1b99991-1844-5b71-9fd0-45fa02c2865e', '7f5014b6-a103-533a-b2b6-01fc8962511d', 'e0d91051-04a8-5e55-8ee6-cc58736c29ec', 'ff588dab-90bc-5201-aeff-ee7ecf34892b', 'a342cdd6-aa56-5d89-b97e-d9af4cfc5892', '0a0d7ef1-9e0b-5ba7-be60-bcd1ecf81cc2', '69647e92-cd73-5fb0-8e6e-d8bef107c5c4', '940d66f0-85a5-5d3e-a762-8681a9694d8c']",-1
7c11a2d3-3a6b-5a27-95b4-a5614a070592,Problems with consecutive odd integers,filters={} text_search='consecutive odd integers' valid_query=True reason='',"['12664de2-6cd8-5639-b6da-39df96e0876f', '628a4e2e-1874-5c75-bf38-5e942f0403cb', '7d1674f4-e503-5b63-80ca-3b1f183022a8', 'bca86399-de3a-5ef1-bd0f-e7f547f332b4', 'b1b99991-1844-5b71-9fd0-45fa02c2865e', 'a342cdd6-aa56-5d89-b97e-d9af4cfc5892', '40f7d252-ce65-505f-84c9-cc65b43679a8', '69647e92-cd73-5fb0-8e6e-d8bef107c5c4', 'e0d91051-04a8-5e55-8ee6-cc58736c29ec', 'c5f0a5d7-4a85-5e27-b230-e51d579e0015']",-1
7c11a2d3-3a6b-5a27-95b4-a5614a070592,Using algebra to solve integer problems,filters={'problem_type': 'Algebra'} text_search='integer problems' valid_query=True reason='',"['91d57157-035b-5ee5-884d-251de263ada8', '82837e83-e726-56cb-87c9-fb2193953dcd', 'ad62fc3e-277e-5e45-9586-6df9da065b83', 'd6eeeb3f-0b5f-583f-97dc-7f8a63ab8fbb', 'a6dcbf7e-228b-5e8f-8611-6788ff0523cd', 'eef19761-527e-5ccf-8c68-b2ced6391fb4', '8fdbb941-063f-53bb-a0d6-2096a139da23', '3528d1d7-bbbd-524d-9784-3d0309e4601b', '9f5b7d65-bcb4-5fd8-884b-9598640dc2dc', '17a9463c-1f8d-51a5-a7f6-e541a5a29e8e']",-1
7c11a2d3-3a6b-5a27-95b4-a5614a070592,Equations for consecutive odd numbers,filters={} text_search='Equations for consecutive odd numbers' valid_query=True reason='',"['bca86399-de3a-5ef1-bd0f-e7f547f332b4', 'b1b99991-1844-5b71-9fd0-45fa02c2865e', 'e0d91051-04a8-5e55-8ee6-cc58736c29ec', 'a342cdd6-aa56-5d89-b97e-d9af4cfc5892', '7d1674f4-e503-5b63-80ca-3b1f183022a8', 'de5622f8-8b55-51bb-9677-342c9d5536e4', 'c71277e9-0104-50c8-ba78-5c62ed81228f', '7c11a2d3-3a6b-5a27-95b4-a5614a070592', '628a4e2e-1874-5c75-bf38-5e942f0403cb', '40f7d252-ce65-505f-84c9-cc65b43679a8']",8
846b695c-7c7f-5a9c-94a0-398d4fba95e0,problems on integer pairs with gcd 1,filters={} text_search='integer pairs with gcd 1' valid_query=True reason='',"['5b21a7c3-8efe-5cb3-9866-5e450ae11e94', '30d7f2fb-0c21-5999-a181-a72908b692cb', '7be2480a-9297-5b8a-8892-b3bc3c9a3a50', '599e1651-16f5-5f0a-bf01-b3f1acc6a157', 'f00140b2-b729-5390-9f25-3f3ae3206e19', '480d920a-6719-5a94-b6b4-93c69d407630', '8dbc40f9-727e-5334-9aa1-69296eb52be4', '7bb2672b-4964-5979-9308-d33380c3fa8d', '5d8cc502-f1a6-5abc-a2fa-63fa19ace825', 'af9292c3-a30a-5a87-8ad5-3196a1ae42c0']",-1
846b695c-7c7f-5a9c-94a0-398d4fba95e0,gcd in integer pair problems,filters={} text_search='gcd in integer pair problems' valid_query=True reason='',"['7be2480a-9297-5b8a-8892-b3bc3c9a3a50', '30d7f2fb-0c21-5999-a181-a72908b692cb', 'ada0c403-271d-502d-a88e-8fdb8f70acd1', 'bcf976eb-991b-5190-a235-e07c237d756e', '72f3eed6-896e-5f07-a07b-1af664d49f5c', 'f00140b2-b729-5390-9f25-3f3ae3206e19', '480d920a-6719-5a94-b6b4-93c69d407630', '14225587-669b-52b3-994d-ebdeda19058c', '78f6c5a8-ab8c-591f-abf5-455d7188b0b6', '35a4a731-6a7e-5280-bace-dd3ed2338db3']",-1
846b695c-7c7f-5a9c-94a0-398d4fba95e0,number theory problems involving fractions,filters={'problem_type': 'Number Theory'} text_search='fractions' valid_query=True reason='',"['216d823d-ea6a-5b6d-8b73-c7613cf15f30', '3d81717b-1153-5bc2-a0ee-9da2515f4ba0', '8cf171b5-b26c-5e28-9957-1800f8b46855', '1b2b4960-7e3b-5680-98ec-d9bb9c047286', 'd3ea00a1-48b2-5808-85c0-96e73e21a91c', 'a6343e36-cbea-512e-be77-a8f04e7cf247', '427b59e5-a6e0-5731-8885-2dadff9c132a', 'd83bc737-dd65-5133-bf2a-65dc4cfe2985', 'd96ed8ac-6508-53bb-a314-5d1568f31730', '05369bf9-74d4-5cdf-bd92-7d569d34ae59']",-1
846b695c-7c7f-5a9c-94a0-398d4fba95e0,integer solutions for fraction simplification,filters={} text_search='integer solutions fraction simplification' valid_query=True reason='',"['427b59e5-a6e0-5731-8885-2dadff9c132a', '846b695c-7c7f-5a9c-94a0-398d4fba95e0', 'd695af8c-290f-5f7d-aa5e-b72c58c7d2e6', 'e7f5ed6a-d234-5e9e-89b0-8b589c37be25', 'a5b790f7-dcec-540a-96c6-df7dbdbf9512', '8855d67b-9008-5e66-83b0-96a0ce1dab5d', 'c15de5ba-3c00-50d2-8b33-c37315e8b2bd', 'ee77c65f-fc78-573f-86bf-d989efcf0e12', '827b5cc5-9070-57d7-9c3c-002f194e4b8a', '58d40911-8b58-53df-9e18-6791a425b72d']",2
9c11521b-9872-51b9-9b32-00d3c133b7f6,Fruit market cost comparison problems,filters={} text_search='Fruit market cost comparison problems' valid_query=False reason='Query is not related to mathematical problems',"['9c11521b-9872-51b9-9b32-00d3c133b7f6', '8bd0551d-3a0d-55ec-81bf-bc736a02ba39', 'd349f776-03ba-505e-853c-07c71dc7c86e', 'f7bac53b-c2a9-5fda-bde9-2e73169a828c', 'f17a50d7-8216-51db-912c-abb512c799f3', '4229868e-2951-53f5-bd17-84e78d4cb469', '7e65d3ae-df45-5ae7-9bc5-83b85617bdc5', '13d805d4-e5b2-58f0-8039-d2d58a3672f1', '52aea08b-2c54-5299-96a4-25d67d276641', '43de347a-eb54-59de-951f-2d7c66497bae']",1
9c11521b-9872-51b9-9b32-00d3c133b7f6,Simple algebra problems with ratios,filters={'problem_type': 'Algebra'} text_search='simple problems ratios' valid_query=True reason='',"['0152caff-1442-5591-ac12-36f459f1402d', '54459190-edee-545c-ac4f-afded3032ecf', '2c66820b-32ba-5886-98e3-312c22a395df', '412a9678-22a0-5ec8-a7e2-7fded5c0a41a', '0f53e5e5-b51c-504d-8d3b-d440b650e091', '7848ec1e-f8d4-5477-85e9-072c60b2887e', 'b1185ad3-a5d7-51ab-b7e6-f6cb9ada553e', '35763509-243e-5291-a2fb-6ca859c8294a', 'faa2bc85-4e93-5dae-8b05-31060ddd846d', '737797d7-72fd-57f4-8ff7-6319672e2a31']",-1
9c11521b-9872-51b9-9b32-00d3c133b7f6,Banana and apple price ratio questions,"filters={} text_search='Banana and apple price ratio questions' valid_query=False reason=""Query doesn't align with the mathematical problem dataset, likely referring to a financial or economics problem rather than a structured math question.""","['8bd0551d-3a0d-55ec-81bf-bc736a02ba39', '9bf02620-61af-5be1-931d-f06f98e8a6a6', '10412630-a328-51fe-b6c1-6380a79c600c', '186fd2f7-1089-5542-b7a2-00c97de7939c', '5c238846-3d58-5d06-b0b0-6341b073c9e4', '9c11521b-9872-51b9-9b32-00d3c133b7f6', '1c04a9ae-82c8-5c6b-b981-a06e0b252a6d', '7e65d3ae-df45-5ae7-9bc5-83b85617bdc5', 'ef7f3d65-cb7b-5f23-88f9-81196979eecd', '4672f5e1-c1ff-521f-85b4-60a118812365']",6
9c11521b-9872-51b9-9b32-00d3c133b7f6,Solving problems using proportionality,filters={} text_search='Solving problems using proportionality' valid_query=True reason='',"['1ecbfcee-5d60-5ace-9ff5-b4275e1cdabb', 'f37afe5f-32f9-5e89-b441-92168047fe65', 'c1f675d0-1196-57e3-b972-b8b95c47dd01', '17a2a903-a841-5ec7-a756-38a9882bbdb0', '8c2434f7-454b-5dbb-b305-8569afaab201', '0104d9f5-62e1-5021-b259-53959e6351ad', '6c2f75f1-531b-556e-bc7a-7421796fc6a0', '2c66820b-32ba-5886-98e3-312c22a395df', 'ed9cdd02-9272-5234-97c3-07294432d528', 'a7b6306b-d40e-59ee-b54f-1e213983c7c6']",-1
b31bcce1-d203-56fe-9b31-f06d5c1e70e9,find problems on calculating average speed,filters={} text_search='calculating average speed' valid_query=True reason='',"['6e231782-37be-5ade-a6e9-61ae395451ac', 'e6bd41aa-edd9-590c-b2f0-1b7454b0f728', 'cf4cb38e-5256-50a5-9107-73a77afaa0aa', '5bb25a82-96d8-542e-909f-5c9e7b1a4474', '511e1dcc-828c-578e-b86c-420e1e523fd7', 'd4452d01-b06c-5176-b166-d21881157505', '953402b3-2cfa-506d-a23d-7fbd464ce135', 'b31bcce1-d203-56fe-9b31-f06d5c1e70e9', '6cd2610b-71d4-5fc6-a827-faba449716cc', '2a178d22-5853-50fb-b735-9ae89fecee8b']",8
b31bcce1-d203-56fe-9b31-f06d5c1e70e9,using formula for average speed with multiple speeds,filters={} text_search='formula for average speed with multiple speeds' valid_query=True reason='',"['511e1dcc-828c-578e-b86c-420e1e523fd7', 'cf4cb38e-5256-50a5-9107-73a77afaa0aa', 'e6bd41aa-edd9-590c-b2f0-1b7454b0f728', '6e231782-37be-5ade-a6e9-61ae395451ac', 'd4452d01-b06c-5176-b166-d21881157505', '5bb25a82-96d8-542e-909f-5c9e7b1a4474', '1124dbd1-d4a5-51d8-b0d4-17afa6857df4', '953402b3-2cfa-506d-a23d-7fbd464ce135', '2a178d22-5853-50fb-b735-9ae89fecee8b', 'b31bcce1-d203-56fe-9b31-f06d5c1e70e9']",10
b31bcce1-d203-56fe-9b31-f06d5c1e70e9,average speed calculation problems,filters={} text_search='average speed calculation problems' valid_query=True reason='',"['6e231782-37be-5ade-a6e9-61ae395451ac', 'e6bd41aa-edd9-590c-b2f0-1b7454b0f728', 'cf4cb38e-5256-50a5-9107-73a77afaa0aa', '511e1dcc-828c-578e-b86c-420e1e523fd7', '5bb25a82-96d8-542e-909f-5c9e7b1a4474', 'd4452d01-b06c-5176-b166-d21881157505', 'b31bcce1-d203-56fe-9b31-f06d5c1e70e9', '953402b3-2cfa-506d-a23d-7fbd464ce135', '6cd2610b-71d4-5fc6-a827-faba449716cc', '2a178d22-5853-50fb-b735-9ae89fecee8b']",7
b31bcce1-d203-56fe-9b31-f06d5c1e70e9,calculate average speed combining cycling and walking,"filters={} text_search='' valid_query=False reason='Query is not related to mathematical problems in the context of the dataset, but rather a real-world calculation request.'",[],-1
bfdff1dd-696d-5042-b8d9-cc188128b03a,sequence problems with custom functions,filters={} text_search='sequence problems with custom functions' valid_query=True reason='',"['4fcc9779-678f-554a-a8ec-7c5b7f8bbd0f', '7311f010-f545-5af3-8011-ef812fb09db6', 'f0571888-6ed8-5e09-85fb-569e326981bc', '435a5343-6a4c-5b6b-8ea6-f408aeb6ae0c', 'fe11d3dc-409e-5bfd-9b1c-02943e9c22e1', 'd2f0e55f-ddc3-51b5-a12b-b1e828deeeec', 'd1001904-d13d-5cdc-a37e-a1a70657c143', 'a2076188-8c3d-5f9b-8e04-06714eb147c7', 'a3b92d1e-5cb1-5deb-b40a-8c99cbac07ba', 'd08b1e53-44e4-5ab1-9c24-d18670b97a1e']",-1
bfdff1dd-696d-5042-b8d9-cc188128b03a,integer sequence and factorization problems,filters={} text_search='integer sequence factorization' valid_query=True reason='',"['0f26b44d-c6a5-5bdc-8444-1796077e13e5', '6b5d01f1-ad6d-54f9-b205-db1654c95e1d', '576ec9f4-98df-5783-aadc-efef248a4bcf', '576b565a-e7f0-5dcc-8b84-e2d1b3dd8d53', '1a4bfa9b-4bf9-57b3-bcc9-c4ebfa68bb39', '7f100bae-027a-5e63-9927-aff571c5573d', 'e65dff79-16a0-5b8f-b954-78c902affcaa', 'eefdddee-fa86-5a76-a9b2-a05f204a4e09', '5d612ea9-1767-563d-be73-b8d83b9c47b2', 'ca4725e0-e17c-571b-8e02-243f750d4cd9']",-1
bfdff1dd-696d-5042-b8d9-cc188128b03a,recursive sequence problem-solving techniques,filters={} text_search='recursive sequence problem-solving techniques' valid_query=True reason='',"['0ff38ffb-0192-573a-8cf7-2fba18e1ef75', 'cd2765c7-ffc8-512e-8512-5f19cadb21f1', 'a81a9e35-aab0-5029-8b6f-7dd84cc30c7f', '4852725b-73d8-55ef-af3a-a02c3868f1ab', 'a959ebcc-69d9-55bc-86e6-57ca0cb4ebe9', 'f5f18959-68cf-5392-8ef7-d054844f2cc8', '147c639b-abe4-5d13-a704-49f78beb1855', 'a2076188-8c3d-5f9b-8e04-06714eb147c7', 'c78bf3ac-3b47-522f-84e2-d80649f619b2', '1b9f5fc5-8714-585c-afdc-90230c7fdce4']",-1
bfdff1dd-696d-5042-b8d9-cc188128b03a,combinatorial counting approach in integer sequences,filters={'problem_type': 'Combinatorics'} text_search='counting approach in integer sequences' valid_query=True reason='',"['e6c5d587-87bb-5304-88ba-a648e1f8a598', 'b530d1e1-b369-5ca5-b710-4dd547fbaf33', '3a5f049c-bdc5-54d0-a262-144c3ba8ea6f', '07768c37-1a29-5726-9332-1948a93c670c', 'ffe8424b-1c83-515c-aaa0-d66da2619a59', 'd50ed1a7-f950-5611-922a-d7eca6895ab7', '91bf31b2-59a9-50cd-82cd-84c65ebc3a73', '11994b38-14cb-5209-a412-c1cb8dcd3b44', '86720195-fdba-5b38-a653-3797217aa667', 'f8c8e444-7a19-59cd-aff5-02e0ea954b7d']",-1
ce282cda-a30b-53aa-8703-e188b4854ab2,Work rate problems,filters={} text_search='Work rate problems' valid_query=True reason='',"['0deb533d-1cea-5e2d-9526-609486dd5d88', '2730bf34-a424-5453-bf03-3e0faac48fce', 'ce282cda-a30b-53aa-8703-e188b4854ab2', '1aed3151-5240-5cf4-aef0-b3e9816f4e25', '27908da6-88dc-5153-8c60-0ab0b3e59e14', '54be38c6-8029-5870-b1d3-21725074ebc3', '6dd746d8-41ca-5fb8-b847-861a5a0c5311', '77f67aa3-8d93-5119-a196-b061b29a3600', 'a1b10842-b2e7-56df-81d4-e47fedd3eff7', '997462a8-0c79-5129-85c6-d78a3bf433ca']",3
ce282cda-a30b-53aa-8703-e188b4854ab2,Calculating work completion time,filters={} text_search='Calculating work completion time' valid_query=False reason='Query is not related to mathematical problems in the dataset context',"['ce282cda-a30b-53aa-8703-e188b4854ab2', '0deb533d-1cea-5e2d-9526-609486dd5d88', '047a9beb-1ba2-5366-812d-cb97f957d1b1', 'fb8187be-7ad0-56a7-845c-5c1989a0c30a', 'a392540e-6076-52a9-8444-91ca61a34330', '990add8a-b604-5a53-ac2b-aa540fe8bc71', 'a662450b-0fa7-5999-beda-a0ae39fe9149', 'a9dfa195-a06f-5ccc-ac62-e61a28103c0b', 'fc7da86a-5c24-57af-8866-6c6a7c063663', '3b9babf6-a70a-59d6-8653-37a71546191b']",1
ce282cda-a30b-53aa-8703-e188b4854ab2,Time and work questions,filters={} text_search='time work questions' valid_query=True reason='',"['ce282cda-a30b-53aa-8703-e188b4854ab2', '047a9beb-1ba2-5366-812d-cb97f957d1b1', '1c04a9ae-82c8-5c6b-b981-a06e0b252a6d', 'fb8187be-7ad0-56a7-845c-5c1989a0c30a', 'a10e43bf-3666-5fb7-8e39-bf8303ef4588', '0ba5e01e-acd6-502c-9e75-1c8262b59c0f', '541928f4-6d6d-50e9-a8f9-aa9d194d55db', '0deb533d-1cea-5e2d-9526-609486dd5d88', 'a5d24f60-f55f-5ac0-b8e1-60147512ae99', '6543d2eb-20d6-58e8-9466-6fbc3c04979a']",1
ce282cda-a30b-53aa-8703-e188b4854ab2,Problems using proportional reasoning,filters={} text_search='problems using proportional reasoning' valid_query=True reason='',"['17a2a903-a841-5ec7-a756-38a9882bbdb0', 'ef15dc4b-c455-55b2-a11d-59eda88ef755', 'f37afe5f-32f9-5e89-b441-92168047fe65', '35763509-243e-5291-a2fb-6ca859c8294a', '58e22ef3-8e1e-50b7-b736-3835d77261dc', '0104d9f5-62e1-5021-b259-53959e6351ad', 'cb9d1204-0a45-5e45-adcb-a71bce23ba23', 'c68baeaa-ed47-57cf-9c18-6f1b67b9c208', '1ecbfcee-5d60-5ace-9ff5-b4275e1cdabb', '823458ca-49e8-5cbd-99b1-733f04f4fe98']",-1
cecaca2b-02e9-51e2-9d72-30202e13b0b7,applying AM-GM inequality to sum fractions,filters={} text_search='AM-GM inequality sum fractions' valid_query=True reason='',"['eb417c64-e215-5f0e-8441-be74f430e11e', '28bb1bc1-edb5-5d1b-9629-277c7263b8e1', '317d20de-ed64-540d-b835-ab5719ca1097', '93246743-5be9-5139-8ac7-b661c74811d7', 'ef03e81f-8405-55f0-888a-c8dee9ded3fe', '045087bc-da43-55a6-9c62-035e8b970126', 'a80808ae-3358-51f5-9363-40304a93a5db', 'bcc3e673-6991-5939-9f26-4ce3338189ff', 'c22a57bb-892c-5fc2-922d-f29ba6955ff3', 'e159e166-796d-5658-93fd-4be0868c29c3']",-1
cecaca2b-02e9-51e2-9d72-30202e13b0b7,optimization problems with positive real numbers,filters={} text_search='optimization problems positive real numbers' valid_query=True reason='',"['5dbef417-7374-5e48-bb34-e0d0936d0f9d', '1428bdc0-5f2c-51aa-8a20-63f9b5ddfa92', 'a79ae900-9843-555a-9cc6-7ba6c222c54b', 'a80be469-d840-5ffa-9cf3-24a57e54279c', '06b6d6dc-ca49-5500-b260-3715fcdbab2b', '576b23b9-e5ab-5824-96a1-f3dd817f9cce', '9bcbf30e-e4b3-5487-ae32-4989ff9a1701', '0184849e-b6cd-5bd7-9b1d-65a38c5db36b', 'e159e166-796d-5658-93fd-4be0868c29c3', 'ce71e648-cf35-5885-a24f-6dff9bcf31ef']",-1
cecaca2b-02e9-51e2-9d72-30202e13b0b7,inequality problems with conditions x+y+z=1,filters={'problem_type': 'Inequalities'} text_search='conditions x+y+z=1' valid_query=True reason='',"['0a6fcadb-070e-5d7e-8b9d-65921af3b9b6', '0e7d23c7-0c9f-59fd-9210-a9a3fa6355c8', '3b428238-51b1-5e55-aba6-be5939a14b43', '19fd16cd-0b20-5851-b789-91a155376488', 'b3ce2687-d2e2-5109-b30f-29bfdb565481', 'ff26caea-1774-5839-8772-746edadaf06b', '7befc173-1044-5f73-a5db-2b8422e342ea', '9d71e2ff-a358-5c15-aaa9-0bef37d693bd', 'bd534a02-5ee2-5d29-8510-72578c526ae0', 'eca106e4-ac51-56ae-b071-6e1ce97de9b7']",-1
cecaca2b-02e9-51e2-9d72-30202e13b0b7,find minimum using AM-GM inequality,filters={'mathematical_results': 'AM-GM Inequality'} text_search='find minimum' valid_query=True reason='',"['906f8b69-c311-5986-83ab-b6d178da75a9', '13391edf-eea4-590b-99c3-ab8b3b68b2c2', '8f015a7b-f9a8-5661-8998-8aafca43a3d0', '3bdaa424-480d-5114-a36a-a5bb86ec73e4', '409b71b8-e4a0-5ca0-91ad-88203144fb11', '32e87abf-f0dc-5371-9d25-6f6c12070a9e', '01696aeb-7dc2-59e2-9ff1-1a7d0a4f8e8d', 'd7695681-9d0a-553a-a395-3d6bbcd97fc4', '1d26fd7f-d331-5a96-a7e2-582b726c1fd1', '7f850b66-511d-5960-b525-f54fa2c0dc56']",-1
d0c4d578-c4af-5760-9aed-dff9829f29f9,problems about trigonometric identities,filters={} text_search='trigonometric identities' valid_query=True reason='',"['8cef42ab-9827-5b8b-8843-dd4736d4f72b', '9749d118-2a41-5fb5-ab21-883caf87cf14', 'b285666e-1a6f-5184-98a6-859185673241', 'bfd8045c-0520-5a9a-98d4-6d0d093bebe7', 'ab2c8d8a-5ede-5694-be56-7e15bf6c84c1', '691ba0b1-d9c3-5cb6-9c5d-7788f2647561', '8ca7e2e9-f736-51c3-b3a3-7ff922c306bd', 'f5a9b90b-8719-58fd-bb1c-5912c01db179', '15a94d79-2f68-5179-ac48-84ebcce56f84', 'e0bf2d65-26df-53e7-8332-fff9581a6387']",-1
d0c4d578-c4af-5760-9aed-dff9829f29f9,calculate tangent using identities,filters={} text_search='calculate tangent using identities' valid_query=True reason='',"['def387c6-0909-5c69-8212-0764bcab92aa', '97de0738-33f8-5482-b947-eb60cb5efe3c', 'e1ee034a-6f01-597c-bad4-3f451d0afac0', '2804ab2f-a747-5d13-a440-caf271082a0d', '73d6b892-c47d-5eb5-8fb1-235aaa8382e5', '5c0a77f0-0657-5d16-8c9d-bc7aec0962f0', 'c947d67d-54d3-5b90-bfbb-70e78cc3cac8', '57515908-0e15-5be9-a4f7-4cf05d470093', '4027d94a-3499-5e82-af09-d8010e1c518d', '353b5a07-5d82-5a5b-826b-469f0fe6fded']",-1
d0c4d578-c4af-5760-9aed-dff9829f29f9,sine cosine angle problems,filters={} text_search='sine cosine angle problems' valid_query=True reason='',"['8b7b4721-108f-5947-ade5-1b581a29ed4f', 'a8187d9b-2e43-594f-ba21-e638040b608a', '07686ebf-77cf-52ab-bc4c-6c3bdcd1e6bb', 'd528ce24-647b-543c-9610-1c15d15bf9d7', '8e8fb87c-d922-5946-8771-a15ce5895952', '9ad38378-d78e-5bc5-81b5-515fd54f65c8', '7f878932-3585-5c21-a896-f4f52c6b419d', 'ab87c22d-989c-5f18-a2a8-3aef56040a8b', '44450537-c236-58d7-9a40-a1afa0c032d2', '15da2b5f-bbd8-52ed-8503-ec17235adca3']",-1
d0c4d578-c4af-5760-9aed-dff9829f29f9,solving trigonometric equations,filters={} text_search='solving trigonometric equations' valid_query=True reason='',"['757de761-ce7a-5005-a252-db7b0a240582', 'a8187d9b-2e43-594f-ba21-e638040b608a', '6ca646a1-4180-5fe7-bc2d-c7629f765d38', '9ad38378-d78e-5bc5-81b5-515fd54f65c8', '92b78efa-0903-5904-b94b-e861da2cb781', 'ed9cdd02-9272-5234-97c3-07294432d528', 'ad8d0013-d106-5a20-9db1-f9ba1769441a', '44450537-c236-58d7-9a40-a1afa0c032d2', 'a8366778-5a8d-545d-9f05-f02679e1c185', '4bcb9bd4-ca2e-5ecb-9e48-fded20ff1869']",-1
f7c0282a-4039-52dc-8426-3a8b658dd482,Problems involving differences between squares,filters={} text_search='differences between squares' valid_query=True reason='',"['187a35aa-73a7-52ab-88f2-4cb5a2e96d97', '6431c6d4-ca71-5b52-9c8a-59da77d630a6', '5b878340-3909-5901-9770-7b340d6ac1af', 'ca95be8a-0be7-5228-8d22-08eb7dace93b', '3e98434a-ce83-54b8-bab2-8c5fab1b5a5b', '7b3521b6-b4b9-5bf9-a620-6f6030bf0afe', '7c234048-9f82-5bd3-9ab4-53cb73539af1', '178c7166-00ec-50cc-a629-195aed743739', '2a7e2918-e170-57bf-8486-91698bc8d256', 'f7c0282a-4039-52dc-8426-3a8b658dd482']",10
f7c0282a-4039-52dc-8426-3a8b658dd482,Problems about digit manipulation in numbers,filters={} text_search='digit manipulation in numbers' valid_query=True reason='',"['74caf35f-c9a5-5e7c-880c-23b2ac90601d', '8d067749-5b76-56cb-ac4f-647cc4fc9143', '232bf198-412d-5559-ac06-162bcb9370d4', '38fa61f1-7391-5c33-9343-66ceecaecf7f', '83141d85-8ebc-5f44-83b1-3eb4e9d322cc', 'f42908ed-b5e8-5ed3-98d7-75de4385943b', '46598902-655b-525d-b812-06a0830fed55', '61b4262f-bb50-55f1-aaa0-b8d5f34eb4d8', 'fc4976aa-9eae-5cb9-ad6c-e2742d03a836', '998b36f2-3875-500c-9149-ac2428d338b7']",-1
f7c0282a-4039-52dc-8426-3a8b658dd482,Three-digit integer math problems,filters={} text_search='three-digit integer' valid_query=True reason='',"['d9b9f15b-6e01-5230-890f-55a8679664f8', '02cae9ff-6d99-5d76-8d0f-477155c7d66a', '18eb2038-38d1-5760-bc0e-9337667bbb53', '61b4262f-bb50-55f1-aaa0-b8d5f34eb4d8', 'e02596d8-cdc7-524b-944c-a1034ec499ad', 'b2cfeb89-2074-51b9-b782-bac34450ef86', 'c77ed96f-1578-563b-8fee-637d0aa9996f', 'dc5b81ad-bcfc-5ba7-aebe-089c800d38c3', '2ba6c62e-0cf6-5ccb-a9fb-e3acd0f115ff', '26693501-66ab-51bb-9cda-46d879decc60']",-1
f7c0282a-4039-52dc-8426-3a8b658dd482,Solving equations using digit positions,filters={} text_search='solving equations using digit positions' valid_query=True reason='',"['46598902-655b-525d-b812-06a0830fed55', '8d067749-5b76-56cb-ac4f-647cc4fc9143', 'f3cb561b-ce71-5c7e-9a53-0cf0aa0588ed', 'cfba1740-e068-5ebf-8d33-5f02308b34cf', 'd2443cd9-e87c-5609-ba67-2635000da94a', 'fc4976aa-9eae-5cb9-ad6c-e2742d03a836', '9eb572a6-4264-547a-9030-710515338235', 'ed9cdd02-9272-5234-97c3-07294432d528', '5d00f4af-c679-532a-88e9-fc52a4418f11', '1a4c80fd-0ab9-57ec-a6bd-bb7332933b8a']",-1
fa01f664-ca98-5cce-af24-967eae942f3c,solving equations with modulus conditions,filters={} text_search='solving equations with modulus conditions' valid_query=True reason='',"['a7b6306b-d40e-59ee-b54f-1e213983c7c6', '1c6a86e7-fc52-52bb-b465-6038410b43d8', 'ad8d0013-d106-5a20-9db1-f9ba1769441a', 'ead78f93-3675-539f-a034-d84d54f70149', '8932c016-bf88-506b-9361-c6c4b4a3c9d0', '1df3b27b-9df9-520f-a367-ac006ce7a7b0', 'a476292c-9504-5957-84e1-8334c53a211e', 'bd534a02-5ee2-5d29-8510-72578c526ae0', 'e57a5f60-86c0-5392-8378-26eb6d13fa87', '414a0e7f-f176-5361-a311-58917ef52a97']",-1
fa01f664-ca98-5cce-af24-967eae942f3c,quadratic trinomial value problems,filters={} text_search='quadratic trinomial value problems' valid_query=True reason='',"['81767f62-08dc-5d14-9869-6b89cb19cf4e', 'fa01f664-ca98-5cce-af24-967eae942f3c', '377a8436-2944-5fb6-998d-b7e35bd8aeb5', 'a0daca7b-df0c-5e14-ba05-f579a2bf3798', '0a3c76d4-e022-521b-b384-b3155c7893ac', '1b9f5fc5-8714-585c-afdc-90230c7fdce4', 'ade68cec-a0b6-5793-8ddd-22c0b08940bc', '296a1974-d85f-5cd9-a75a-e030cb7f8787', '014a5c51-c734-5d27-9f59-629c1987d4c1', '9104a005-ff18-55d7-9e7f-6a70d13c378e']",2
fa01f664-ca98-5cce-af24-967eae942f3c,finding coefficients in quadratic equations with constraints,filters={} text_search='finding coefficients in quadratic equations with constraints' valid_query=True reason='',"['7631bb8f-e3e1-54d1-934c-1b6ce1a4965b', '82523d75-61db-5d55-9b9f-e763175e60ee', '1d1189cc-7d7c-5a74-a8b1-3b02c95d997b', '8b8c232f-7a96-5d10-bf4f-680827c5dcad', 'bb9e29c6-3f66-59cc-ad03-0efa80d0cb35', '3e415673-9bd8-5f4b-8d14-7be61e10513c', '957836c0-9a3a-5967-aa67-aefb05f74ea9', 'c2149346-8b4b-5e15-9a88-f168d7306b7e', '6a1cd1c6-693c-58cf-afc7-e4f04cdd7281', '002d8884-f082-59f6-b513-5736a2543bd1']",-1
fa01f664-ca98-5cce-af24-967eae942f3c,conditions on quadratic coefficients,filters={} text_search='conditions on quadratic coefficients' valid_query=True reason='',"['cf2ce12a-d9b9-517a-973b-abc76b67f5b9', '3e415673-9bd8-5f4b-8d14-7be61e10513c', '002d8884-f082-59f6-b513-5736a2543bd1', 'bb9e29c6-3f66-59cc-ad03-0efa80d0cb35', '6a1cd1c6-693c-58cf-afc7-e4f04cdd7281', '6d8b8b78-7a74-5faa-91f7-f9590e9143ac', '8b8c232f-7a96-5d10-bf4f-680827c5dcad', 'a5b5e2f4-d4e4-5ab7-a495-1f241d076e14', 'b863f086-ac39-534c-a229-77835ac91796', '31109d02-2ef2-5fd5-9f2e-47f0e26924ae']",-1
