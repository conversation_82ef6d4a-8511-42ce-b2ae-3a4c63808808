from typing import List

import pytest

from mathlete.search.tags.tags_matcher import MetadataFuzzyMatcher
from mathlete.utils.constants import ExamInfo, MathProblem, Metadata


@pytest.fixture
def sample_problems() -> List[MathProblem]:
    return [
        MathProblem(
            uuid="1",
            informal_statement="Prove that 2 + 2 = 4",
            formal_statement="2 + 2 = 4",
            informal_proof="By definition of addition",
            exam_info=ExamInfo(exam_category="International Contests"),
            metadata=Metadata(
                question_type="proof", problem_type="Algebra", tags=["basic", "proof"]
            ),
        ),
        MathProblem(
            uuid="2",
            informal_statement="Solve for x: 3x + 5 = 20",
            formal_statement="Find x such that 3x + 5 = 20",
            informal_proof="Subtract 5 from both sides...",
            exam_info=ExamInfo(exam_category="National Olympiads"),
            metadata=Metadata(
                question_type="math-word-problem",
                problem_type="Algebra",
                tags=["equation", "linear"],
            ),
        ),
        MathProblem(
            uuid="3",
            informal_statement="What is the area of a circle with radius 5?",
            formal_statement="Calculate area of circle with r=5",
            informal_proof="A = πr² = π*5² = 25π",
            exam_info=ExamInfo(exam_category="Junior Olympiads"),
            metadata=Metadata(
                question_type="mcq", problem_type="Geometry", tags=["circle", "area"]
            ),
        ),
    ]


@pytest.fixture
def matcher(sample_problems) -> MetadataFuzzyMatcher:
    return MetadataFuzzyMatcher(problem_dataset=sample_problems)


@pytest.mark.asyncio
async def test_get_match_score_perfect_match(matcher):
    tokens = ["algebra"]
    score = await matcher._get_match_score(tokens, "algebra")
    assert score == 100


@pytest.mark.asyncio
async def test_get_match_score_partial_match(matcher):
    tokens = ["alge"]
    score = await matcher._get_match_score(tokens, "algebra")
    assert score > 85


@pytest.mark.asyncio
async def test_get_match_score_no_match(matcher):
    tokens = ["calculus"]
    score = await matcher._get_match_score(tokens, "algebra")
    assert score < 50


@pytest.mark.asyncio
async def test_match_field_question_type(matcher):
    tokens = ["proof"]
    result = await matcher.match_field(tokens, "question_type", 85)
    assert "question_type" in result
    assert len(result["question_type"]) == 1
    assert result["question_type"][0]["value"] == "proof"
    assert result["question_type"][0]["score"] == 100


@pytest.mark.asyncio
async def test_match_field_problem_type(matcher):
    tokens = ["geometr"]  # Intentional typo
    result = await matcher.match_field(tokens, "problem_type", 85)
    assert "problem_type" in result
    assert any(m["value"] == "Geometry" for m in result["problem_type"])


@pytest.mark.asyncio
async def test_match_field_below_threshold(matcher):
    tokens = ["xyz"]  # No match
    result = await matcher.match_field(tokens, "problem_type", 85)
    assert result == {}


@pytest.mark.asyncio
async def test_match_fields_multiple(matcher):
    tokens = ["algebra", "proof"]
    result = await matcher.match_fields(tokens)
    assert "question_type" in result
    assert "problem_type" in result
    assert any(m["value"] == "proof" for m in result["question_type"])
    assert any(m["value"] == "Algebra" for m in result["problem_type"])


@pytest.mark.asyncio
async def test_filter_problems_by_metadata(matcher):
    tokens = ["algebra"]
    metadata_matches = await matcher.match_fields(tokens)
    print(metadata_matches)
    problems = matcher.filter_problems_by_metadata(metadata_matches)
    print(problems)
    assert len(problems) == 2  # Should match both algebra problems
    assert all(p.metadata.problem_type == "Algebra" for p in problems)


@pytest.mark.asyncio
async def test_filter_problems_by_multiple_metadata(matcher):
    tokens = ["algebra", "proof"]
    metadata_matches = await matcher.match_fields(tokens)
    problems = matcher.filter_problems_by_metadata(metadata_matches)
    assert len(problems) == 1
    assert problems[0].metadata.question_type == "proof"
    assert problems[0].metadata.problem_type == "Algebra"


@pytest.mark.asyncio
async def test_filter_problems_by_tags(matcher):
    tokens = ["circle"]
    metadata_matches = await matcher.match_fields(tokens)
    metadata_matches["tags"] = [{"value": "circle", "score": 100}]
    problems = matcher.filter_problems_by_metadata(metadata_matches)
    assert len(problems) == 1
    assert "circle" in problems[0].metadata.tags
