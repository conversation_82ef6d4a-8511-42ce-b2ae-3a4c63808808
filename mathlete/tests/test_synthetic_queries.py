import argparse
import ast
import concurrent.futures
import math
import threading
from datetime import datetime
from pathlib import Path
from typing import List

import pandas as pd
from loguru import logger

from mathlete.search.search import MathProblemSearch


class SearchTest:
    def __init__(self, base_df_path: Path = None, output_path: Path = None):
        """
        Initializes the SearchTest class with parameters for testing.
        """
        self.searcher = MathProblemSearch()

        script_dir = Path(__file__).resolve().parent

        if base_df_path is None:
            base_df_path = (
                script_dir.parent / "mathlete" / "data" / "final_data.parquet"
            )
        self.base_df = pd.read_parquet(base_df_path)

        if output_path is None:
            output_dir = script_dir / "results" / "search"
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = output_dir / f"search_results_{timestamp}.json"
        self.output_path = output_path

        self.results = []
        self._results_lock = threading.Lock()
        self._searcher_lock = threading.Lock()

    def run_search_pipeline(
        self,
        data_file_path: str,
        top_k: int,
        num_problems: int,
        num_queries: int,
        query_type: str = "both",
        save_output: bool = False,
    ):
        """
        Runs the search pipeline on the dataset for the selected queries and logs results.

        Args:
            data_file_path (str): Path to the CSV file containing synthetic queries.
            top_k (int): The number of top results to return in the search.
            num_problems (int): The number of problems to test.
            num_queries (int): The number of queries to test per problem.
            query_type (str): Type of queries to use - "topic_based", "technique_based", or "both".
            save_output (bool): Writing results to output_path.
        """
        self.top_k = top_k
        self.num_problems = num_problems
        self.num_queries = num_queries
        self.query_type = query_type

        self.df = pd.read_csv(data_file_path)
        problems_to_test = self.df.head(self.num_problems)

        logger.info("Fitting Retrievers")
        self.searcher.fit_retriever(self.base_df)
        logger.info("Retrievers are fitted")

        with concurrent.futures.ThreadPoolExecutor() as executor:
            futures = [
                executor.submit(self._test_problem, problem_data, i + 1)
                for i, problem_data in problems_to_test.iterrows()
            ]
            for future in concurrent.futures.as_completed(futures):
                future.result()

        if save_output:
            self._save_results()

    def _get_queries(self, problem_data: pd.Series) -> List[str]:
        """
        Returns a list of queries for a given problem based on the selected query type.

        Args:
            problem_data (pd.Series): The row of the DataFrame corresponding to a problem.

        Returns:
            List[str]: A list of queries for the problem.
        """
        queries = []
        half_queries = math.ceil(self.num_queries / 2)
        if self.query_type in {"topic_based", "both"}:
            topic_queries = ast.literal_eval(problem_data["topic_based_queries"])
            queries.extend(topic_queries[:half_queries])

        if self.query_type in {"technique_based", "both"}:
            technique_queries = ast.literal_eval(
                problem_data["technique_based_queries"]
            )
            queries.extend(technique_queries[:half_queries])

        return queries

    def _test_problem(self, problem_data: pd.Series, problem_index: int):
        """
        Runs the search for a single problem and logs the results (including top_k ranking).

        Args:
            problem_data (pd.Series): The row of the DataFrame corresponding to the problem.
            problem_index (int): The index of the problem in the testing set.
        """
        queries = self._get_queries(problem_data)
        for i, query in enumerate(queries):
            logger.info(
                f"Running search for query {i + 1} of problem {problem_index}: {query}"
            )
            with self._searcher_lock:
                search_results = self.searcher.search(query, top_k=self.top_k)[
                    "results"
                ]
                query_decomposition = self.searcher.query_decomposition

            problem_position = self._get_problem_position(problem_data, search_results)
            search_results_uuid = [result["uuid"] for result in search_results]

            with self._results_lock:
                self.results.append(
                    {
                        "uuid": problem_data["uuid"],
                        "query": query,
                        "query_decomposition": query_decomposition,
                        "results": search_results_uuid,
                        "problem_position": problem_position,
                    }
                )

    def _get_problem_position(
        self, problem_data: pd.Series, results: List[dict]
    ) -> int:
        """
        Checks if the problem is in the top_k results and returns the position.

        Args:
            problem_data (pd.Series): The row of the DataFrame corresponding to the problem.
            results (List[dict]): The top_k results returned by the search.

        Returns:
            int: The position of the problem in the top_k results, or -1 if not found.
        """
        problem_uuid = problem_data["uuid"]

        for idx, result in enumerate(results):
            if result["uuid"] == problem_uuid:
                return idx + 1

        return -1

    def _save_results(self):
        """
        Saves the collected search results to a CSV file.
        """
        self.output_path.parent.mkdir(parents=True, exist_ok=True)

        results_df = pd.DataFrame(self.results)
        results_df.sort_values(by="uuid", inplace=True)
        results_df.to_csv(self.output_path, index=False)
        logger.info(f"Results saved to {self.output_path}")


def main():
    script_dir = Path(__file__).resolve().parent
    default_data_path = (
        script_dir / "data/synthetic_queries/synthetic_queries_20250209_230926.csv"
    )

    parser = argparse.ArgumentParser(
        description="Test search pipeline on synthetic queries dataset"
    )
    parser.add_argument(
        "--data_file_path",
        type=Path,
        default=default_data_path,
        help="Path to the synthetic queries dataset (CSV file)",
    )
    parser.add_argument(
        "--top_k", type=int, default=10, help="Number of top results to return"
    )
    parser.add_argument(
        "--num_problems", type=int, default=20, help="Number of problems to test"
    )
    parser.add_argument(
        "--num_queries",
        type=int,
        default=4,
        help="Number of queries to test per problem and",
    )
    parser.add_argument(
        "--query_type",
        type=str,
        choices=["topic_based", "technique_based", "both"],
        default="both",
        help="Type of queries to use ('topic_based', 'technique_based', or 'both')",
    )
    parser.add_argument(
        "--output_path",
        type=Path,
        default=None,
        help="Path to save the output CSV file",
    )
    parser.add_argument(
        "--save_output",
        action="store_true",
        help="If set, saves the search results to a CSV file",
    )

    args = parser.parse_args()

    search_test = SearchTest(output_path=args.output_path)
    search_test.run_search_pipeline(
        data_file_path=args.data_file_path,
        top_k=args.top_k,
        num_problems=args.num_problems,
        num_queries=args.num_queries,
        query_type=args.query_type,
        save_output=args.save_output,
    )


if __name__ == "__main__":
    main()
