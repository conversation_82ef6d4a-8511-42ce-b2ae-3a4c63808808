import ast
import os

import pandas as pd
from loguru import logger

from mathlete.search.related_queries.related_queries import RelatedQueriesGenerator


def load_queries_from_csv(file_path: str) -> pd.DataFrame:
    try:
        df = pd.read_csv(file_path)
        logger.info(f"Successfully loaded {len(df)} rows from {file_path}")
        return df
    except Exception as e:
        logger.error(f"Error loading CSV file: {str(e)}")
        raise


def parse_query_list(query_string):
    try:
        if query_string.startswith("[") and query_string.endswith("]"):
            return ast.literal_eval(query_string)
        return [query_string]
    except Exception as e:
        logger.warning(f"Error parsing query list: {str(e)}. Using as single query.")
        return [query_string]


def run_test(csv_path: str, output_dir: str, model: str = None, limit: int = None):
    """Run the test using queries from CSV as input"""
    os.makedirs(output_dir, exist_ok=True)
    df = load_queries_from_csv(csv_path)
    generator = RelatedQueriesGenerator()

    rows_to_process = df.iloc[:limit] if limit else df
    all_row_results = []

    for idx, row in rows_to_process.iterrows():
        uuid = row["uuid"]
        topic_queries = parse_query_list(row["topic_based_queries"])
        technique_queries = parse_query_list(row["technique_based_queries"])

        logger.info(f"Processing row {idx+1}/{len(rows_to_process)} with UUID: {uuid}")

        if topic_queries:
            query = topic_queries[0]
            try:
                logger.info(f"  Processing first topic query: {query}")
                generated_queries = generator.suggest_queries(query, model)

                for item in generated_queries.related_queries:
                    query_result = {
                        "original_query": query,
                        "query_type": "topic_based",
                        "related_query": item.query,
                        "relation_type": item.relation_type,
                        "description": item.description,
                    }
                    all_row_results.append(query_result)

            except Exception as e:
                logger.error(f"Error processing topic query: {query}: {str(e)}")
                all_row_results.append(
                    {
                        "original_query": query,
                        "query_type": "topic_based",
                        "related_query": None,
                        "relation_type": None,
                        "description": f"ERROR: {str(e)}",
                    }
                )

        if technique_queries:
            query = technique_queries[0]
            try:
                logger.info(f"  Processing first technique query: {query}")
                generated_queries = generator.suggest_queries(query, model)

                for item in generated_queries.related_queries:
                    query_result = {
                        "original_query": query,
                        "query_type": "technique_based",
                        "related_query": item.query,
                        "relation_type": item.relation_type,
                        "description": item.description,
                    }
                    all_row_results.append(query_result)

            except Exception as e:
                logger.error(f"Error processing technique query: {query}: {str(e)}")
                all_row_results.append(
                    {
                        "original_query": query,
                        "query_type": "technique_based",
                        "related_query": None,
                        "relation_type": None,
                        "description": f"ERROR: {str(e)}",
                    }
                )

        logger.info(f"Completed processing row {idx+1}")

    output_file = os.path.join(output_dir, "related_queries_results.csv")

    try:
        results_df = pd.DataFrame(all_row_results)
        results_df.to_csv(output_file, index=False)
        logger.info(f"Results successfully written to {output_file}")
    except Exception as e:
        logger.error(f"Failed to write results to CSV: {str(e)}")

    logger.info(f"All results saved to {output_dir}")

    return all_row_results


if __name__ == "__main__":
    import argparse

    default_output_dir = os.path.join(os.path.dirname(__file__), "results")

    parser = argparse.ArgumentParser(
        description="Test RelatedQueriesGenerator with queries from CSV"
    )
    parser.add_argument("--csv_path", help="Path to CSV file with queries")
    parser.add_argument(
        "--output_dir", help="Directory to save results", default=default_output_dir
    )
    parser.add_argument("--model", help="LLM model to use", default="gpt-4o-2024-08-06")
    parser.add_argument(
        "--limit", type=int, help="Limit the number of rows to process", default=5
    )

    args = parser.parse_args()

    run_test(args.csv_path, args.output_dir, args.model, args.limit)
