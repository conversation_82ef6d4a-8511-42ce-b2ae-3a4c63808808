import argparse
import json
from concurrent.futures import <PERSON><PERSON>oolExecutor
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Union

import pandas as pd
from loguru import logger

from mathlete.feedback.informal.informal_proof_feedback import InformalProofAnalyzer
from mathlete.feedback.informal.informal_proof_feedback_models import (
    MathematicalAnalysisResult,
    MathematicalError,
)
from mathlete.utils.constants import (
    MathProblem,
    MathProblemCollection,
    UserProofAttempt,
)

SCRIPT_DIR = Path(__file__).parent
RESULTS_DIR = SCRIPT_DIR / "results/informal_proof_pipeline"
RESULTS_DIR.mkdir(parents=True, exist_ok=True)


@dataclass
class InformalProofResultMetadata:
    """Container for the complete analysis results with problem metadata"""

    problem_uuid: str
    _analysis_result: MathematicalAnalysisResult

    @property
    def summary(self) -> str:
        return self._analysis_result.summary

    @property
    def mathematical_errors(self) -> List[MathematicalError]:
        return self._analysis_result.mathematical_errors

    @property
    def has_errors(self) -> bool:
        return len(self.mathematical_errors) > 0

    @property
    def has_mathematical_errors(self) -> bool:
        return len(self.mathematical_errors) > 0

    def to_dict(self) -> Dict[str, Any]:
        result = {
            "problem_uuid": self.problem_uuid,
            "summary": self.summary,
            "has_mathematical_errors": self.has_mathematical_errors,
            "errors": [error.__dict__ for error in self.mathematical_errors],
        }

        return result


class InformalProofPipelineTester:
    """
    A pipeline for evaluating and analyzing informal mathematical proofs.

    This class is designed for testing purposes, it validates the provided data, processes each math
    problem and performs informal proof analysis.

    The class allows batch processing of multiple problems for efficiency.

    Attributes:
        REQUIRED_FIELDS (List[str]): Fields that must be present in the input data.
        data (Any): Placeholder for input data.

    Methods:
        validate_data(data): Validates input data format and required fields.
        prepare_data(data): Converts input data into MathProblem instances.
        analyze_proof_logic(problem): Runs proof analysis.
        process_problem(problem): Processes a single math problem.
        run_pipeline(data): Runs the pipeline on multiple problems.
    """

    REQUIRED_FIELDS = ["problem", "informal_solution"]

    def __init__(self):
        self.data = None

    @classmethod
    def validate_data(cls, data: Union[pd.DataFrame, List[Dict[str, Any]]]) -> None:
        """
        Validates that the input data contains all required fields.

        Args:
            data: DataFrame or list of dictionaries containing math problems

        Raises:
            ValueError: If required fields are missing
        """
        if isinstance(data, pd.DataFrame):
            missing_cols = [
                col for col in cls.REQUIRED_FIELDS if col not in data.columns
            ]
            if missing_cols:
                raise ValueError(f"Missing required columns: {missing_cols}")
        elif isinstance(data, list):
            if not data:
                raise ValueError("Empty data provided")
            missing_keys = [
                key for key in cls.REQUIRED_FIELDS if key not in data[0].keys()
            ]
            if missing_keys:
                raise ValueError(f"Missing required keys in dictionary: {missing_keys}")
        else:
            raise ValueError(
                "Data must be either a DataFrame or a list of dictionaries"
            )

    def prepare_data(
        self, data: Union[pd.DataFrame, List[Dict[str, Any]]]
    ) -> List[MathProblem]:
        """
        Prepares and validates the input data for processing.

        Args:
            data: DataFrame or list of dictionaries containing math problems

        Returns:
            List of MathProblem instances

        Raises:
            ValueError: If data format is invalid or required fields are missing
        """
        self.validate_data(data)

        if isinstance(data, pd.DataFrame):
            return list(MathProblemCollection.from_dataframe(data))
        else:
            data_dicts = data
            return [MathProblem.from_dict(d) for d in data_dicts]

    def analyze_proof_logic(
        self, attempt: UserProofAttempt
    ) -> MathematicalAnalysisResult:
        """
        Analyzes the informal proof.

        Args:
            attempt: UserProofAttempt instance

        Returns:
            MathematicalAnalysisResult
        """
        proof_analyzer = InformalProofAnalyzer()
        try:
            logger.info("Analyzing Proof...")
            proof_analysis_result = proof_analyzer.analyze_informal_proof(attempt)
            logger.info(f"Proof Analyzer Summary: {proof_analysis_result.summary}")

            if proof_analysis_result.mathematical_errors:
                logger.error(
                    f"Proof Analyzer First Math Error: {proof_analysis_result.mathematical_errors[0]}"
                )

        except RuntimeError as e:
            error_message = str(e)
            if "context_length_exceeded" in error_message:
                logger.error(f"Context length exceeded: {error_message}")
                proof_analysis_result = MathematicalAnalysisResult(
                    mathematical_errors=[],
                    summary="Context Length Exceeded",
                )
            else:
                logger.error(f"Unexpected error during proof analysis: {error_message}")
                proof_analysis_result = MathematicalAnalysisResult(
                    mathematical_errors=[],
                    summary=f"Proof analysis encountered an unexpected error: {error_message}",
                )
        return proof_analysis_result

    def process_problem(self, problem: MathProblem):
        """
        Process a single math problem.

        Args:
            problem: MathProblem instance

        Returns:
            MathematicalAnalysisResult
        """
        problem_uuid = getattr(problem, "uuid", "unknown")
        user_id = "test_user123"
        user_proof = problem.informal_proof
        user_attempt = UserProofAttempt.create_new(problem, user_id, user_proof)

        proof_analysis_result = self.analyze_proof_logic(user_attempt)

        result = InformalProofResultMetadata(
            problem_uuid=problem_uuid, _analysis_result=proof_analysis_result
        )
        return result

    def run_pipeline(
        self,
        data: Union[pd.DataFrame, List[Dict[str, Any]]],
        max_workers: int = 50,
    ) -> List[Any]:
        """
        Run the pipeline on the specified range of problems.

        Args:
            data: DataFrame or list of dictionaries containing math problems

        Returns:
            List of InformalProofAnalyzer results

        Raises:
            ValueError: If data is invalid or required fields are missing
        """
        logger.info(f"Starting pipeline with {len(data)} problems")
        problems = self.prepare_data(data)

        if len(problems) == 1:
            return [self.process_problem(problems[0])]

        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            results = list(
                executor.map(
                    self.process_problem,
                    [p for p in problems],
                )
            )

        error_count = sum(1 for r in results if r.has_errors)

        logger.info(
            f"Pipeline completed - "
            f"Processed {len(results)} problems with {error_count} errors"
        )

        return results


def load_input_data(
    file_path: str, limit: int
) -> Union[pd.DataFrame, List[Dict[str, Any]]]:
    """
    Load input data from file based on extension.
    """
    if file_path.endswith(".csv"):
        return pd.read_csv(file_path).head(limit)
    elif file_path.endswith(".parquet"):
        return pd.read_parquet(file_path).head(limit)
    elif file_path.endswith(".json"):
        with open(file_path, "r") as f:
            return json.load(f)
    elif file_path.endswith(".jsonl"):
        with open(file_path, "r") as f:
            return [json.loads(line) for line in f]
    else:
        raise ValueError(f"Unsupported file format: {file_path}")


def get_default_output_path():
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    return RESULTS_DIR / f"informal_proof_results_{timestamp}.json"


def save_results(
    results: List[InformalProofResultMetadata],
    output_path: Path,
    format_type: str = "json",
):
    """
    Save results to a file in the specified format.

    Args:
        results: List of ProofResultMetadata objects
        output_path: Path to save results
        format_type: Output format (json, csv)
    """
    logger.info(
        f"Saving {len(results)} results to {output_path} in {format_type} format"
    )

    result_dicts = [r.to_dict() for r in results]

    if format_type == "json":
        with output_path.open("w") as f:
            json.dump(result_dicts, f, indent=2)
    elif format_type == "csv":
        pd.DataFrame(result_dicts).to_csv(output_path, index=False)
    else:
        raise ValueError(f"Format type {format_type} not accepted. Only json or csv.")

    logger.info("Results saved")


def main():
    parser = argparse.ArgumentParser(description="Math Problem Proof Analysis Pipeline")

    input_group = parser.add_argument_group("Input Options")
    input_group.add_argument(
        "--input",
        type=str,
        default=str(Path(__file__).parent.parent / "data" / "dedup_data.parquet"),
        required=False,
        help="Input file path (supports .csv, .parquet, .json, .jsonl)",
    )

    process_group = parser.add_argument_group("Processing Options")
    process_group.add_argument(
        "--limit",
        type=int,
        default=10,
        help="Limit the pipeline to the first N rows of data (default: 10)",
    )

    output_group = parser.add_argument_group("Output Options")
    output_group.add_argument(
        "--output",
        type=str,
        default=str(get_default_output_path()),
        help="Path to save results (default: auto-generated timestamped file)",
    )
    output_group.add_argument(
        "--format",
        choices=["json", "csv"],
        default="json",
        help="Output format (default: json)",
    )

    args = parser.parse_args()
    output_path = Path(args.output)
    output_path.parent.mkdir(parents=True, exist_ok=True)

    try:
        logger.info(f"Starting proof pipeline with {args.limit} problems")
        data = load_input_data(args.input, args.limit)

        pipeline = InformalProofPipelineTester()
        results = pipeline.run_pipeline(data=data)

        save_results(results, output_path, args.format)
        logger.info(f"Results saved to {output_path}")

    except Exception as e:
        logger.error(f"Error in pipeline: {str(e)}", exc_info=True)
        raise


if __name__ == "__main__":
    main()
