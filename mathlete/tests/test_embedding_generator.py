import numpy as np
import pandas as pd
import pytest

from mathlete.utils.constants import MathProblemCollection
from mathlete.vectors.embedding_generator import EmbeddingGenerator

data = [
    {
        "uuid": "405ac224-e87e-5ffc-ae8b-a0418c954fc4",
        "problem": "How many positive integers have exactly three proper divisors?",
        "informal_solution": "Suppose n is such an integer... the answer is 109.",
        "languages": ["English"],
        "mathematical_techniques": ["Direct proof", "Combination counting"],
        "mathematical_results": [],
        "difficulty_level": "5",
        "exam": "Math Exam 1",
        "problem_type": "Number Theory",
        "question_type": "proof",
    },
    {
        "uuid": "d3f8731b-51c8-587c-8e29-1eb67c0db32b",
        "problem": "For any odd prime p... Does such a sequence exist?",
        "informal_solution": "Fix some odd prime p... the answer is detailed above.",
        "languages": ["English"],
        "mathematical_techniques": ["Direct proof", "Modular arithmetic"],
        "mathematical_results": ["Multiplicative order"],
        "difficulty_level": "8",
        "exam": "Advanced Math",
        "problem_type": "Algebra",
        "question_type": "proof",
    },
]


@pytest.fixture
def sample_df():
    return pd.DataFrame(data)


@pytest.fixture
def embedding_generator():
    return EmbeddingGenerator()


def test_embedding_generation(embedding_generator, sample_df):
    """Test if embeddings are generated correctly."""
    problems = MathProblemCollection.from_dataframe(sample_df)
    embedded_problems = embedding_generator.generate_embeddings(problems)

    assert len(embedded_problems) == len(problems)

    first_problem = embedded_problems[0]
    assert isinstance(first_problem.embedding, np.ndarray)
    assert first_problem.embedding.shape[0] > 0


def test_embedding_metadata(embedding_generator, sample_df):
    """Test if the metadata is processed correctly."""
    problems = MathProblemCollection.from_dataframe(sample_df)
    embedded_problems = embedding_generator.generate_embeddings(problems)
    first_metadata = embedded_problems[0].metadata

    assert first_metadata.difficulty_level == 5
    assert first_metadata.problem_type == "Number Theory"
    assert first_metadata.question_type == "proof"
