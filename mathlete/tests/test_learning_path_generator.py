from pathlib import Path
from unittest.mock import patch

import igraph as ig
import pandas as pd
import pytest

from mathlete.graph.graph_service import GraphService
from mathlete.graph.similarity_graph_builder import SimilarityGraphBuilder
from mathlete.recommendation.learning_paths.learning_path_generator import (
    LearningPathGenerator,
)
from mathlete.recommendation.recommender import Problem<PERSON><PERSON>ommender
from mathlete.utils.constants import MathProblemCollection
from mathlete.utils.problem_manager import ProblemManager
from mathlete.vectors.embedding_generator import EmbeddingGenerator
from mathlete.vectors.similarity_matrix_builder import SimilarityMatrixBuilder


@pytest.fixture
def sample_problems():
    data_path = Path(__file__).parent.parent / "data" / "final_data.parquet"
    df = pd.read_parquet(data_path).head(10)
    problems = MathProblemCollection.from_dataframe(df)
    gen = EmbeddingGenerator()
    return gen.generate_embeddings(problems)


@pytest.fixture
def problem_manager(sample_problems):
    """Create a real ProblemManager with sample problems."""
    manager = ProblemManager(problems=sample_problems)
    return manager


@pytest.fixture
def similarity_matrix_builder(problem_manager):
    """Create a SimilarityMatrixBuilder from a real ProblemManager."""
    return SimilarityMatrixBuilder(problem_manager)


@pytest.fixture
def similarity_graph_builder(similarity_matrix_builder, problem_manager):
    """Create a real SimilarityGraphBuilder with a SimilarityMatrixBuilder."""
    return SimilarityGraphBuilder(
        problem_manager=problem_manager,
        similarity_matrix_builder=similarity_matrix_builder,
    )


def test_learning_path_generator_init(sample_problems):
    problem_manager = ProblemManager(sample_problems)

    similarity_matrix_builder = SimilarityMatrixBuilder(problem_manager)
    similarity_graph_builder = SimilarityGraphBuilder(
        problem_manager=problem_manager,
        similarity_matrix_builder=similarity_matrix_builder,
    )

    graph_service = GraphService()
    path_finder = LearningPathGenerator(
        problems=sample_problems,
        graph_service=graph_service,
    )

    assert (
        path_finder.problem_manager.get_problem_ids()
        == problem_manager.get_problem_ids()
    )
    assert isinstance(
        path_finder.distance_graph, ig.Graph
    ), "Expected distance_graph to be igraph.Graph"

    graph_1 = path_finder.distance_graph
    graph_2 = similarity_graph_builder.get_similarity_graph()

    assert graph_1.vcount() == graph_2.vcount(), "Graph vertex count mismatch"
    assert graph_1.ecount() == graph_2.ecount(), "Graph edge count mismatch"
    assert graph_1.get_edgelist() == graph_2.get_edgelist(), "Graph edge list mismatch"


def test_find_next_problem(problem_manager):
    """Test the find_next_problem method."""
    all_problem_ids = list(problem_manager.get_problem_ids())

    assert len(all_problem_ids) >= 5, "Not enough problems for test"

    current_problems = all_problem_ids[:2]
    eligible_problems = all_problem_ids[2:5]
    excluding = set(current_problems)

    recommender = ProblemRecommender(problem_manager=problem_manager)
    # Test normal case
    result = recommender._find_next_problem(
        current_problems, eligible_problems, excluding
    )
    assert result is not None
    assert result in eligible_problems
    assert result not in excluding

    # Test empty candidates
    result = recommender._find_next_problem(current_problems, [], excluding)
    assert result is None

    # Test all eligible problems excluded
    result = recommender._find_next_problem(
        current_problems, current_problems, set(current_problems)
    )
    assert result is None


def test_find_learning_path(problem_manager):
    """Test find_learning_path method."""
    all_problem_ids = list(problem_manager.get_problem_ids())

    assert len(all_problem_ids) >= 2, "Not enough problems for test"

    start_problems = all_problem_ids[:2]

    recommender = ProblemRecommender(problem_manager=problem_manager)

    # Test normal case
    result = recommender.find_learning_path(
        start_problems, target_difficulty=8, max_problems=5
    )
    assert isinstance(result, list)
    assert len(result) <= 5

    for problem in result:
        assert problem not in start_problems

    # Test with target difficulty lower than min difficulty
    with pytest.raises(ValueError):
        # Patch the get_problem_difficulty method to always return 7
        with patch.object(
            recommender.problem_manager, "get_problem_difficulty", return_value=7
        ):
            recommender.find_learning_path(start_problems, target_difficulty=3)


def test_find_bridging_path(sample_problems):
    """Test find_bridging_path method."""
    problem_manager = ProblemManager(sample_problems)
    all_problem_ids = list(problem_manager.get_problem_ids())

    assert len(all_problem_ids) >= 10, "Not enough problems for test"

    graph_service = GraphService()
    path_finder = LearningPathGenerator(
        problems=sample_problems,
        graph_service=graph_service,
    )

    sources = all_problem_ids[:2]

    valid_exams = set()
    for problem in problem_manager.problems:
        if problem.exam_info and problem.exam_info.exam:
            valid_exams.add(problem.exam_info.exam)

    target_exam = next(iter(valid_exams)) if valid_exams else "IMO"

    middle_problems = all_problem_ids[3:6]

    with patch.object(
        path_finder, "_find_bridging_path", return_value=middle_problems
    ) as mock_find:
        result = path_finder.find_bridging_path(sources, "exam", target_exam)
        assert result == middle_problems

        # Verify that targets with the specified exam were selected
        called_sources, called_targets = mock_find.call_args[0]
        assert called_sources == sources
        for target in called_targets:
            problem = path_finder.problem_manager.get_problem(target)
            assert problem.exam_info and problem.exam_info.exam == target_exam

    # Test with empty problems list
    with patch.object(path_finder.problem_manager, "_problems_by_id", {}):
        with patch.object(path_finder.problem_manager, "_problem_ids", []):
            with pytest.raises(
                ValueError, match="No problems available in the dataset"
            ):
                path_finder.find_bridging_path(sources, "exam", target_exam)

    # Test with invalid metadata field
    with pytest.raises(ValueError, match="Invalid metadata field"):
        path_finder.find_bridging_path(sources, "invalid_field", "value")

    # Test with no matching target problems
    result = path_finder.find_bridging_path(sources, "exam", "unknown_exam")
    assert result == []
