import argparse
import json
from concurrent.futures import <PERSON><PERSON>oolExecutor
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Union

import pandas as pd
from loguru import logger

from mathlete.autoformalization.proof_formalization import LeanProofFormalizer
from mathlete.autoformalization.proof_formalization_models import (
    LeanFormalizationResult,
)
from mathlete.feedback.formal.formal_proof_feedback_models import FormalizationError
from mathlete.feedback.informal.informal_proof_feedback_models import MathematicalError
from mathlete.feedback.lean4_feedback import lean4_feedback
from mathlete.feedback.proof_feedback import Proof<PERSON><PERSON>ys<PERSON>R<PERSON><PERSON>, ProofAnalyzer
from mathlete.utils.constants import MathProblem, UserProofAttempt

SCRIPT_DIR = Path(__file__).parent
RESULTS_DIR = SCRIPT_DIR / "results/proof_pipeline"
RESULTS_DIR.mkdir(parents=True, exist_ok=True)


@dataclass
class ProofResultMetadata:
    """Container for the complete analysis results with problem metadata"""

    problem_uuid: str
    formalized: str
    analyzed: str
    _analysis_result: ProofAnalysisResult

    @property
    def summary(self) -> str:
        return self._analysis_result.summary

    @property
    def mathematical_errors(self) -> List[MathematicalError]:
        return self._analysis_result.mathematical_errors

    @property
    def formalization_errors(self) -> List[FormalizationError]:
        return self._analysis_result.formalization_errors

    @property
    def all_errors(self) -> List[Union[MathematicalError, FormalizationError]]:
        """Returns a combined list of all errors"""
        return self.mathematical_errors + self.formalization_errors

    @property
    def has_errors(self) -> bool:
        return len(self.mathematical_errors) > 0 or len(self.formalization_errors) > 0

    @property
    def has_mathematical_errors(self) -> bool:
        return len(self.mathematical_errors) > 0

    @property
    def has_formalization_errors(self) -> bool:
        return len(self.formalization_errors) > 0

    def to_dict(self) -> Dict[str, Any]:
        result = {
            "problem_uuid": self.problem_uuid,
            "summary": self.summary,
            "has_mathematical_errors": self.has_mathematical_errors,
            "has_formalization_errors": self.has_formalization_errors,
            "errors": [error.__dict__ for error in self.all_errors],
            "formalized": self.formalized,
            "analyzed": self.analyzed,
        }

        return result


class ProofPipelineTester:
    """
    A pipeline for evaluating and analyzing already formalized mathematical proofs.

    This class is designed for testing purposes, it validates the provided data, processes each math
    problem without running full formalization (unless specified), and performs proof
    analysis.

    The class allows batch processing of multiple problems for efficiency.

    Attributes:
        REQUIRED_FIELDS (List[str]): Fields that must be present in the input data.
        data (Any): Placeholder for input data.

    Methods:
        validate_data(data): Validates input data format and required fields.
        prepare_data(data): Converts input data into MathProblem instances.
        formalize_proof(problem, formalize): Returns existing formalization or generates a new one.
        build_full_lean4_code(formalized_proof): Constructs the full Lean4 proof.
        analyze_proof_logic(problem, formalized_proof, full_lean4_code, force_analyze): Runs proof analysis.
        process_problem(problem, formalize, force_analyze): Processes a single math problem.
        run_pipeline(data, formalize, force_analyze): Runs the pipeline on multiple problems.
    """

    REQUIRED_FIELDS = ["problem", "lean_code", "informal_solution"]

    def __init__(self):
        self.data = None

    @classmethod
    def validate_data(cls, data: Union[pd.DataFrame, List[Dict[str, Any]]]) -> None:
        """
        Validates that the input data contains all required fields.

        Args:
            data: DataFrame or list of dictionaries containing math problems

        Raises:
            ValueError: If required fields are missing
        """
        if isinstance(data, pd.DataFrame):
            missing_cols = [
                col for col in cls.REQUIRED_FIELDS if col not in data.columns
            ]
            if missing_cols:
                raise ValueError(f"Missing required columns: {missing_cols}")
        elif isinstance(data, list):
            if not data:
                raise ValueError("Empty data provided")
            missing_keys = [
                key for key in cls.REQUIRED_FIELDS if key not in data[0].keys()
            ]
            if missing_keys:
                raise ValueError(f"Missing required keys in dictionary: {missing_keys}")
        else:
            raise ValueError(
                "Data must be either a DataFrame or a list of dictionaries"
            )

    def prepare_data(
        self, data: Union[pd.DataFrame, List[Dict[str, Any]]]
    ) -> List[MathProblem]:
        """
        Prepares and validates the input data for processing.

        Args:
            data: DataFrame or list of dictionaries containing math problems

        Returns:
            List of MathProblem instances

        Raises:
            ValueError: If data format is invalid or required fields are missing
        """
        self.validate_data(data)

        if isinstance(data, pd.DataFrame):
            data_dicts = data.to_dict("records")
        else:
            data_dicts = data

        return [MathProblem.from_dict(d) for d in data_dicts]

    def formalize_proof(
        self, attempt: UserProofAttempt, formalize: bool = False
    ) -> LeanFormalizationResult:
        """
        Handles the formalization process of the proof.

        Args:
            attempt: UserProofAttempt instance
            formalize: Whether to generate new formalization or use existing

        Returns:
            LeanFormalizationResult: The formalized proof

        Raises:
            ValueError: If formalize=False and lean4_solution is not provided
        """
        problem = attempt.problem
        if formalize:
            logger.info("Formalizing problem")
            formalizer = LeanProofFormalizer()
            return formalizer.formalize_proof(attempt)
        else:
            logger.info("Using existing formalization")
            if not problem.lean4_solution:
                raise ValueError("lean4_solution is required when formalize=False")

            return LeanFormalizationResult.from_dict(
                {
                    "lean4_code": problem.lean4_solution,
                    "required_imports": [],
                    "gaps_identified": [],
                }
            )

    @staticmethod
    def build_full_lean4_code(formalized_proof: LeanFormalizationResult) -> str:
        """
        Builds the complete Lean4 code including imports.

        Args:
            formalized_proof: LeanFormalizationResult instance

        Returns:
            str: Complete Lean4 code with imports
        """
        imports = "\n".join(
            f"import {imp}" for imp in formalized_proof.required_imports
        )
        return (
            f"{imports}\n\n{formalized_proof.lean4_code}"
            if imports
            else formalized_proof.lean4_code
        )

    def analyze_proof_logic(
        self,
        attempt: UserProofAttempt,
        formalized_proof: LeanFormalizationResult,
        full_lean4_code: str,
        force_analyze: bool = True,
    ):
        """
        Analyzes the formalized proof.

        Args:
            attempt: UserProofAttempt instance
            formalized_proof: LeanFormalizationResult instance
            full_lean4_code: Complete Lean4 code string
            force_analyze: Whether to force analysis even without errors

        Returns:
            ProofAnalyzer result
        """
        has_errors, lean_result = lean4_feedback([full_lean4_code])
        logger.info(f"Errors in Lean4: {has_errors}")

        has_error = True if force_analyze else has_errors[0]

        proof_analyzer = ProofAnalyzer()
        try:
            logger.info("Analyzing Proof...")
            proof_analysis_result = proof_analyzer.analyze_proof(
                attempt,
                formalized_proof,
                has_error,
                lean_result,
            )
            logger.info(f"Proof Analyzer Summary: {proof_analysis_result.summary}")

            if proof_analysis_result.mathematical_errors:
                logger.error(
                    f"Proof Analyzer First Math Error: {proof_analysis_result.mathematical_errors[0]}"
                )

            if proof_analysis_result.formalization_errors:
                logger.error(
                    f"Proof Analyzer First Math Error: {proof_analysis_result.formalization_errors[0]}"
                )

        except RuntimeError as e:
            error_message = str(e)
            if "context_length_exceeded" in error_message:
                logger.error(f"Context length exceeded: {error_message}")
                proof_analysis_result = ProofAnalysisResult(
                    is_valid=False,
                    mathematical_errors=[],
                    formalization_errors=[],
                    summary="Context Length Exceeded",
                )
            else:
                logger.error(f"Unexpected error during proof analysis: {error_message}")
                proof_analysis_result = ProofAnalysisResult(
                    is_valid=False,
                    mathematical_errors=[],
                    formalization_errors=[],
                    summary=f"Proof analysis encountered an unexpected error: {error_message}",
                )
        return proof_analysis_result

    def process_problem(
        self, problem: MathProblem, formalize: bool = False, force_analyze: bool = True
    ):
        """
        Process a single math problem.

        Args:
            problem: MathProblem instance
            formalize: Whether to generate new formalization or use existing
            force_analyze: Whether to force analysis even without errors

        Returns:
            ProofAnalyzer result
        """
        problem_uuid = getattr(problem, "uuid", "unknown")
        user_id = "test_user123"
        user_proof = problem.informal_proof
        user_attempt = UserProofAttempt.create_new(problem, user_id, user_proof)
        formalized_proof = self.formalize_proof(user_attempt, formalize)
        full_lean4_code = self.build_full_lean4_code(formalized_proof)

        proof_analysis_result = self.analyze_proof_logic(
            user_attempt, formalized_proof, full_lean4_code, force_analyze
        )

        result = ProofResultMetadata(
            problem_uuid=problem_uuid,
            _analysis_result=proof_analysis_result,
            formalized=formalize,
            analyzed=force_analyze,
        )
        return result

    def _process_problem_wrapper(self, args):
        # executor.map only accepts functions that take a single argument
        problem, formalize, force_analyze = args
        return self.process_problem(problem, formalize, force_analyze)

    def run_pipeline(
        self,
        data: Union[pd.DataFrame, List[Dict[str, Any]]],
        formalize: bool = False,
        force_analyze: bool = True,
        max_workers: int = 50,
    ) -> List[Any]:
        """
        Run the pipeline on the specified range of problems.

        Args:
            data: DataFrame or list of dictionaries containing math problems
            formalize: Whether to generate new formalization or use existing
            force_analyze: Whether to force analysis even without errors

        Returns:
            List of ProofAnalyzer results

        Raises:
            ValueError: If data is invalid or required fields are missing
        """
        logger.info(f"Starting pipeline with {len(data)} problems")
        problems = self.prepare_data(data)

        if len(problems) == 1:
            return [self.process_problem(problems[0], formalize, force_analyze)]

        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            results = list(
                executor.map(
                    self._process_problem_wrapper,
                    [(p, formalize, force_analyze) for p in problems],
                )
            )

        error_count = sum(1 for r in results if r.has_errors)

        logger.info(
            f"Pipeline completed - "
            f"Processed {len(results)} problems with {error_count} errors"
        )

        return results


def load_input_data(
    file_path: str, limit: int
) -> Union[pd.DataFrame, List[Dict[str, Any]]]:
    """
    Load input data from file based on extension.
    """
    if file_path.endswith(".csv"):
        return pd.read_csv(file_path).head(limit)
    elif file_path.endswith(".parquet"):
        df = pd.read_parquet(file_path)
        final_df = df[(df["is_valid_no_sorry"])].head(limit)
        return final_df
    elif file_path.endswith(".json"):
        with open(file_path, "r") as f:
            return json.load(f)
    elif file_path.endswith(".jsonl"):
        with open(file_path, "r") as f:
            return [json.loads(line) for line in f]
    else:
        raise ValueError(f"Unsupported file format: {file_path}")


def get_default_output_path():
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    return RESULTS_DIR / f"proof_results_{timestamp}.json"


def save_results(
    results: List[ProofResultMetadata], output_path: Path, format_type: str = "json"
):
    """
    Save results to a file in the specified format.

    Args:
        results: List of ProofResultMetadata objects
        output_path: Path to save results
        format_type: Output format (json, csv)
    """
    logger.info(
        f"Saving {len(results)} results to {output_path} in {format_type} format"
    )

    result_dicts = [r.to_dict() for r in results]

    if format_type == "json":
        with output_path.open("w") as f:
            json.dump(result_dicts, f, indent=2)
    elif format_type == "csv":
        pd.DataFrame(result_dicts).to_csv(output_path, index=False)
    else:
        raise ValueError(f"Format type {format_type} not accepted. Only json or csv.")

    logger.info("Results saved")


def main():
    parser = argparse.ArgumentParser(
        description="Math Problem Proof Formalization and Analysis Pipeline"
    )

    input_group = parser.add_argument_group("Input Options")
    input_group.add_argument(
        "--input",
        type=str,
        default=str(Path(__file__).parent.parent / "data" / "final_data.parquet"),
        required=False,
        help="Input file path (supports .csv, .parquet, .json, .jsonl)",
    )

    process_group = parser.add_argument_group("Processing Options")
    process_group.add_argument(
        "--formalize",
        action="store_true",
        help="Generate new formalizations instead of using existing ones",
    )
    process_group.add_argument(
        "--force-analyze",
        action="store_true",
        help="Force analysis even without errors",
    )
    process_group.add_argument(
        "--limit",
        type=int,
        default=10,
        help="Limit the pipeline to the first N rows of data (default: 10)",
    )

    output_group = parser.add_argument_group("Output Options")
    output_group.add_argument(
        "--output",
        type=str,
        default=str(get_default_output_path()),
        help="Path to save results (default: auto-generated timestamped file)",
    )
    output_group.add_argument(
        "--format",
        choices=["json", "csv"],
        default="json",
        help="Output format (default: json)",
    )

    args = parser.parse_args()
    output_path = Path(args.output)
    output_path.parent.mkdir(parents=True, exist_ok=True)

    try:
        logger.info(f"Starting proof pipeline with {args.limit} problems")
        data = load_input_data(args.input, args.limit)

        pipeline = ProofPipelineTester()
        results = pipeline.run_pipeline(
            data=data, formalize=args.formalize, force_analyze=args.force_analyze
        )

        save_results(results, output_path, args.format)
        logger.info(f"Results saved to {output_path}")

    except Exception as e:
        logger.error(f"Error in pipeline: {str(e)}", exc_info=True)
        raise


# For quick testing :)
example_problems = [
    {
        "problem": "If $\\tan \\alpha = 1$, then the value of $\\frac{2\\sin^{2}\\alpha + 1}{\\sin 2\\alpha}$ is ______.",
        "informal_solution": "Given $\\tan \\alpha = 1$, we aim to find the value of $\\frac{2\\sin^{2}\\alpha + 1}{\\sin 2\\alpha}$.\n\nFirst, recall that $\\sin 2\\alpha = 2\\sin\\alpha \\cos\\alpha$ and $\\tan \\alpha = \\frac{\\sin\\alpha}{\\cos\\alpha}$. Given $\\tan \\alpha = 1$, this implies $\\sin\\alpha = \\cos\\alpha$.\n\nLet's break down the expression step by step:\n\n\\[\n\\frac{2\\sin^{2}\\alpha + 1}{\\sin 2\\alpha} = \\frac{2\\sin^{2}\\alpha + 1}{2\\sin\\alpha \\cos\\alpha}\n\\]\n\nGiven $\\sin\\alpha = \\cos\\alpha$, we can substitute $\\cos\\alpha$ with $\\sin\\alpha$:\n\n\\[\n= \\frac{2\\sin^{2}\\alpha + 1}{2\\sin^{2}\\alpha}\n\\]\n\nNow, since $\\tan \\alpha = 1$, we can express $\\cos^{2}\\alpha$ in terms of $\\sin^{2}\\alpha$ using the identity $\\sin^{2}\\alpha + \\cos^{2}\\alpha = 1$:\n\n\\[\n= \\frac{3\\sin^{2}\\alpha + \\cos^{2}\\alpha}{2\\sin\\alpha \\cos\\alpha}\n\\]\n\nGiven $\\tan \\alpha = 1$, we can rewrite $\\cos^{2}\\alpha$ as $\\sin^{2}\\alpha$:\n\n\\[\n= \\frac{3\\tan^{2}\\alpha + 1}{2\\tan\\alpha}\n\\]\n\nSince $\\tan \\alpha = 1$, substituting $\\tan \\alpha$ with $1$ gives:\n\n\\[\n= \\frac{3(1)^{2} + 1}{2(1)} = \\frac{3 + 1}{2} = \\frac{4}{2} = 2\n\\]\n\nTherefore, the value of $\\frac{2\\sin^{2}\\alpha + 1}{\\sin 2\\alpha}$ is $\\boxed{2}$.",
        "lean_code": "import Mathlib\n\ntheorem algebra_533152 (α : ℝ) (h : Real.tan α = 1) :\n    (2 * (Real.sin α)^2 + 1) / (Real.sin (2 * α)) = 2 := by sorry",
        "lean4_solution": "import Mathlib\n\n/- If $\\tan \\alpha = 1$, then the value of $\\frac{2\\sin^{2}\\alpha + 1}{\\sin 2\\alpha}$ is ______. -/\ntheorem algebra_533152 (α : ℝ) (h : Real.tan α = 1) :\n    (2 * (Real.sin α)^2 + 1) / (Real.sin (2 * α)) = 2 := by \n  rw [Real.tan_eq_sin_div_cos] at h\n  have h1 : Real.sin α ≠ 0 := by\n    intro h2\n    rw [h2] at h\n    norm_num at h\n  have h2 : Real.cos α ≠ 0 := by\n    intro h3\n    rw [h3] at h\n    norm_num at h\n  field_simp [Real.sin_two_mul] at *\n  rw [← sub_eq_zero] at h\n  nlinarith [Real.sin_sq_add_cos_sq α, Real.sin_le_one α, Real.cos_le_one α]\n",
    }
]

if __name__ == "__main__":
    main()
