from loguru import logger

from mathlete.hints.infer_hints_models import HintGenerationResult
from mathlete.hints.infer_hints_prompts import InfertHintsPromptGenerator
from mathlete.llms import llms
from mathlete.utils.constants import UserProofAttempt


class MathHintGenerator:
    def __init__(self):
        pass

    def generate_hints(
        self, attempt: UserProofAttempt, model: str
    ) -> HintGenerationResult:
        prompt_generator = InfertHintsPromptGenerator()
        prompt = prompt_generator.make_prompt_problem_hints(attempt)
        model = model or "gpt-4o-2024-08-06"
        try:
            return llms.execute_prompt(
                prompt=prompt, output_class=HintGenerationResult, model=model
            )

        except Exception as e:
            logger.error(f"Error generating hints: {str(e)}")
            raise RuntimeError(f"Hint generation failed: {str(e)}")
