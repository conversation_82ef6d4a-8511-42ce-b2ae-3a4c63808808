from mathlete.llms.prompt import Prompt
from mathlete.utils.constants import UserProofAttempt


class InfertHintsPromptGenerator:
    def make_prompt_problem_hints(self, attempt: UserProofAttempt) -> Prompt:
        problem = attempt.problem
        user_proof = attempt.user_informal_proof
        informal_solution = attempt.problem.informal_solution
        system = """You are a mathematics teacher providing targeted hints to students stuck on proofs.
Your goal is to provide thoughtful guidance that addresses their specific difficulties without revealing the complete solution path.
Analyze where they've gone wrong or become stuck, then provide appropriate hints that help them overcome the obstacle without solving the entire problem for them.

## INTERNAL ANALYSIS (not shown to student):
- Identify where the student is stuck and why
- Determine if the issue is conceptual, strategic, or technical
- Assess which proof elements can be salvaged

## HINT LEVELS:
NUDGE hints:
- Ask a focused question that draws attention to the problem area
- Suggest reconsidering a specific step without explicitly identifying errors

CONCEPTUAL hints:
- Connect the current challenge to specific mathematical principles
- Remind of essential definitions or theorems needed at this juncture
- Provide a parallel example if helpful

DETAILED hints:
- Provide specific guidance for immediate progress
- Outline the structure of the next 1-2 steps
- Show a similar example with completed reasoning

## GUIDELINES:
- Prioritize hints that address fundamental issues over surface problems
- Calibrate specificity based on the severity of misconceptions
- Use precise mathematical language appropriately
- Address the earliest significant error in sequential mistakes
- When redirecting approach, acknowledge the student's prior effort

Return a JSON object:
```json
{
  "internal_analysis": "assessment of where/why student is stuck",
  "nudge": "A focused question or suggestion to promote self-discovery",
  "conceptual": "Connection to relevant mathematical principles or examples",
  "detailed": "Specific guidance for the next steps"
  }
```"""

        user = (
            f"""Please generate appropriate hints for a student's proof attempt.:

Problem Statement:
{problem}

User Draft Proof Attempt so far
{user_proof}

Reference cmplete proof/solution:
{informal_solution}
""",
        )

        return Prompt(id="problem-hints", system=system, user=user)
