import argparse
from concurrent.futures import ThreadPoolExecutor
from pathlib import Path

import pandas as pd
from loguru import logger

from mathlete.llms import llms
from mathlete.solution_corruptor.solution_corruptor_models import (
    CorruptedSolution,
    ErrorLevel,
)
from mathlete.solution_corruptor.solution_corruptor_prompt import (
    SolutionCorruptionPromptGenerator,
)
from mathlete.utils.constants import MathProblem, MathProblemCollection


class GenerateCorruptedSolutions:
    def __init__(self):
        pass

    def generate_modified_solution(
        self, problem: MathProblem, error_level: ErrorLevel = ErrorLevel.SERIOUS
    ) -> CorruptedSolution:
        logger.info(
            f"Generating {error_level} corrupted solution for problem {problem.uuid}"
        )
        prompt_generator = SolutionCorruptionPromptGenerator()
        prompt = prompt_generator.make_prompt_solution_corruption(problem, error_level)
        try:
            return llms.execute_prompt(prompt=prompt, output_class=CorruptedSolution)
        except Exception as e:
            logger.error(f"Error generating corrupted solution: {str(e)}")
            raise RuntimeError(f"Modified solution corruption failed: {str(e)}")


def process_problems(
    df: pd.DataFrame, error_level: ErrorLevel, max_workers: int
) -> pd.DataFrame:
    problems = MathProblemCollection.from_dataframe(df)
    solution_generator = GenerateCorruptedSolutions()

    def generate_solution_for_problem(problem):
        try:
            result = solution_generator.generate_modified_solution(problem, error_level)
            return pd.Series(
                {
                    "uuid": problem.uuid,
                    "problem": problem.informal_statement,
                    "informal_solution": result.modified_solution,
                    "original_step": result.original_step,
                    "modified_step": result.modified_step,
                    "error_type": result.error_type,
                    "explanation": result.explanation,
                    "error_level": error_level.value,
                }
            )
        except Exception as e:
            logger.error(f"Error processing problem {problem.uuid}: {e}")
            return pd.Series(
                {
                    "uuid": problem.uuid,
                    "problem": problem.informal_statement,
                    "informal_solution": None,
                    "original_step": None,
                    "modified_step": None,
                    "error_type": None,
                    "explanation": None,
                    "error_level": error_level.value,
                }
            )

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        results = list(executor.map(generate_solution_for_problem, problems))

    results_df = pd.DataFrame(results)
    return results_df[
        [
            "uuid",
            "problem",
            "informal_solution",
            "original_step",
            "modified_step",
            "error_type",
            "explanation",
            "error_level",
        ]
    ]


def generate_synthetic_solutions(
    input_file: Path,
    output_file: Path,
    error_level: ErrorLevel = ErrorLevel.SERIOUS,
    limit: int = 10,
    max_workers: int = 20,
) -> pd.DataFrame:
    logger.info(f"Reading problems from {input_file}")
    df = pd.read_parquet(input_file)
    df = df[df["exam"] == "IMO"].head(limit)
    logger.info(f"Filtered to {len(df)} problems to keep only IMO problems")

    total_problems = len(df)
    logger.info(
        f"Found {total_problems} problems to process with {error_level.value} errors"
    )

    processed_df = process_problems(df, error_level, max_workers)
    processed_df.to_parquet(output_file)
    logger.info(f"Saved results to {output_file}")

    return processed_df


def main():
    parser = argparse.ArgumentParser(
        description="Generate corrupted solutions for mathematical problems."
    )

    script_dir = Path(__file__).parent
    default_input_file = script_dir.parent / "data" / "dedup_data.parquet"
    default_output_file = script_dir.parent / "data" / "corrupted_solutions.parquet"

    parser.add_argument(
        "input_file",
        type=Path,
        nargs="?",
        default=default_input_file,
        help="Input file path (Parquet format)",
    )
    parser.add_argument(
        "output_file",
        type=Path,
        nargs="?",
        default=default_output_file,
        help="Output file path (Parquet format)",
    )
    parser.add_argument(
        "--limit",
        type=int,
        default=10,
        help="Limit the pipeline to the first N rows of data (default: 10)",
    )
    parser.add_argument(
        "--max_workers",
        type=int,
        default=20,
        help="Maximum number of worker threads (default is 20)",
    )
    parser.add_argument(
        "--error_level",
        type=str,
        choices=[e.name for e in ErrorLevel],
        default=ErrorLevel.SERIOUS.name,
        help="Error level for modified solutions (SLIGHT, MODERATE, SERIOUS)",
    )

    args = parser.parse_args()
    error_level = ErrorLevel[args.error_level]

    _ = generate_synthetic_solutions(
        args.input_file,
        args.output_file,
        error_level=error_level,
        limit=args.limit,
        max_workers=args.max_workers,
    )
    logger.info("Corrupted solution generation completed")


if __name__ == "__main__":
    main()
