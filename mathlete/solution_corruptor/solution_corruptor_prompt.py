from mathlete.llms.prompt import Prompt
from mathlete.solution_corruptor.solution_corruptor_models import <PERSON>rrorLevel
from mathlete.utils.constants import MathProblem


class SolutionCorruptionPromptGenerator:
    def make_prompt_solution_corruption(
        self, problem: MathProblem, error_level: ErrorLevel = ErrorLevel.SERIOUS
    ) -> Prompt:
        system = """
You are a mathematical solution modification assistant. Your task is to generate a plausible but flawed solution to a given mathematical problem by introducing exactly one intentional error that genuinely breaks the validity of the solution.

Guidelines:

1. Analyze the correct solution and identify its key logical steps.
2. Select exactly one step to modify; all other steps remain unchanged.
3. Introduce one meaningful error from the following categories:
   - missing_step: Omit a critical or logically necessary step or part of a step.
   - logical_error: Misapply, misinterpret, or incorrectly apply a mathematical principle or reasoning.
   - unjustified_step: Insert a step without proper justification or proof.
   - incorrect_assumption: Use a false or unsupported assumption in the reasoning.
   - calculation_error: Make a genuine computational or algebraic error that changes the result.
     Note: Calculation errors must alter the outcome and not just restate correct calculations.

4. The modified step must break the correctness or validity of the solution — it must create a real flaw that impacts the proof or final conclusion.

5. The solution text **must NOT** contain:
   - Any explicit statement, label, or annotation that points out or discusses the error.
   - Any commentary or explanation about the correctness or incorrectness of steps.
   - Any restatement or confirmation that the step is valid or invalid.
   - Phrases or language that serve as meta-commentary, such as "which contradicts", "which is wrong because", "therefore this is valid", or "no longer provides a contradiction".

6. The flawed solution must read as a natural mathematical argument with the embedded flaw **hidden inside the math or logic itself**, not highlighted or explained.

7. It is acceptable to remove an entire step or part of a step to create a meaningful flaw.

8. Favor introducing logical errors, missing steps, or unjustified assumptions over trivial or cosmetic calculation errors.

9. Use the JSON "explanation" field exclusively to:
   - Describe exactly what was changed,
   - Explain why the modification is incorrect,
   - Clarify how it invalidates the overall solution.

Return a JSON object with the structure:

{
  "modified_solution": "Full text of the flawed solution with one embedded error",
  "original_step": "Exact text of the step from the original correct solution that was modified",
  "modified_step": "Flawed version of the original step with the embedded error",
  "error_type": "Category of error introduced (e.g., calculation_error, missing_step, logical_error, unjustified_step, incorrect_assumption)",
  "explanation": "Explain what was changed, why it is incorrect, and how this invalidates the solution"
}
"""

        user = f"""Create a flawed solution attempt for this mathematical problem:

# Problem Statement:
{problem.informal_statement}

# Correct Solution:
{problem.informal_proof}
"""
        return Prompt(id="synthetic-problem", system=system, user=user)
