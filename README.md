# Axion
Mathlete is an AI-powered tool that transforms how students engage with mathematical proofs.
It leverages Numina data and models to build an IMO companion app where
- mathlet<PERSON> can search and find mathematical problems,
- math<PERSON><PERSON> writes proofs, automatically formalized and verified in the background,
- feedback is provided when a proof fails, highlighting exactly where the issue lies.

## Installation
You can install `mathlete` as a Python package with

```bash
pip install -e .
pip install -r requirements.txt
```

You should enable pre-commit hook with

```bash
pre-commit install
```

You can then run pre-commit before commiting with

```bash
pre-commit run --all-files
```

## Code Structure
All external LLM calls follows the structure defined in the [llms](https://github.com/project-numina/mathlete/tree/main/mathlete/llms) directory. This separate prompt construction from the querying. It is currently possible to query OpenAI, TogetherAI or vLLM in [llms.py](https://github.com/project-numina/mathlete/blob/main/mathlete/llms/llms.py). You need to set up your OPENAI API key (and similarly your TOGETHER API key):

```bash
export OPENAI_API_KEY=your_api_key
export TOGETHER_API_KEY=your_api_key
```

We implemented a local cache for all prompts, whose default directory can be specified:

```bash
export PROMPTS_CACHE_PATH=your_favorite_path
```

We should consider extending this to a redis cache for the webapp.

## Dataset Processing
The core dataset used to power mathlete is [numina-math-lean4](https://huggingface.co/datasets/AI-MO/numina-math-lean4). We proceed to the foloowing to build our base data for Mathlete:
1. The original dataset is enriched with the metadata coming from its different sources of data (olympiads, aops, cnk12,...) in the [`data_processing/dataset_merger.py`](https://github.com/project-numina/mathlete/blob/main/mathlete/data_processing/dataset_merger.py).

2. We then perform six rounds of metadata inference, the first one adds languages, mathematical techniques, mathematical results and non-mathematical text in the solution, the second one infers the difficulty level following AOPS Competition Ranking guidelines, then the fourth one adds hints to get started on the problem, the fifth detects teh presence of multiple solutions in the `informal_solution` field, and last but not least we propose problem names.

3. By default we only keep problems that are in English and with a valid Lean4 solution.

4. We can then optionally proceed to a deduplication analysis to identify (near) duplicate problems with [`data_processing/deduplicate.py`](https://github.com/project-numina/mathlete/blob/main/mathlete/data_processing/deduplicate.py).

All of those operations are orchestrated in [`run_pipeline.py`](https://github.com/project-numina/mathlete/blob/main/mathlete/data_processing/run_pipeline.py).

The final dataset can be found in ['data/app_data.parquet'](https://github.com/project-numina/mathlete/blob/main/mathlete/data/app_data.parquet) with the following information.

The main fields are:
- `uuid`: unique identifier,
- `problem`: problem statement,
- `informal_solution`: informal problem proof,
- `lean_code`: lean4 problem statement,
- `lean4_solution`: lean4 problem proof,
- `natural_language`: problem statement with solution,
- `source`: source of the problem (`aops`, `olympiads`, `MATH`,... ).

The metadata fields are:
- `question_type`: type of problem (proof, math-word-problem,...),
- `problem_type`: mathematical domain of the problem,
- `problem_number`: the problem number in the exam,
- `exam`: name of the exam,
- `exam_category`: list of the corresponding categories of exam (`Team Selection Test`, `Undergraduate Contests`, `Junior Olympiads`, `International Contests`, `National And Regional Contests `, `National Olympiads`) ,
- `year`: exam year,
- `level`: difficulty level. CAREFUL, currently there are levels (like `Level 4`) from the `MATH` problem together with levels (like `T3` for Tier 3) from `olympiads-ref`.
- `mathematical_techniques`: the techniques used in the proof (like induction).
- `mathematical_results`: the results used in the proof (like Wilson's theorem).
- `non_mathematical_text`: the boilerplate non-mathematical text in the proof (like signatures).
- `difficulty_level`: the inferred problem difficulty, on a scale from 1 to 10.
- `languages`: the detected languages.
- `hints_nudge`, `hints_conceptual`, `hints_detailed`: the different level of hints to start the problem.
- `multiple_solutins`: the multiple solutions if any.
- `problem_name`: the proposed name of the problem.

```mermaid
graph TD;
    %% Merging Process
    A[("numina-math-lean4 Dataset")] --> B{{BaseDataProcessor.merge_datasets}}
    subgraph "External Datasets"
        C1[("aops")]
        C2[("olympiads")]
        C3[("MATH")]
        C4[("orca")]
        C5[("...")]
    end
    C1 --> B
    C2 --> B
    C3 --> B
    C4 --> B
    C5 --> B
    B --> D[("Merged Dataset")]

    %% Parallel metadata inference operations from Merged Dataset
    D --> E1{{infer_metadata.analyze_problems}}:::step1
    D --> E2{{infer_difficulty.grade_problems}}:::step2
    D --> E3{{infer_hints.generate_hints}}:::step3
    D --> E4{{infer_solutions.detect_multiple}}:::step4
    D --> E5{{infer_names.propose_problem_names}}:::step5

    %% Individual processed datasets
    E1 --> F1[("Metadata Enriched Dataset")]
    E2 --> F2[("Difficulty Graded Dataset")]
    E3 --> F3[("Hints Dataset")]
    E4 --> F4[("Multiple Solutions Annotated")]
    E5 --> F5[("Named Problems")]

    %% Final merging
    F1 --> K{{Final Dataset Merger}}
    F2 --> K
    F3 --> K
    F4 --> K
    F5 --> K
    K --> L[("Final Dataset")]

    %% Styling steps
    classDef step1 fill:#e0f7fa,stroke:#006064;
    classDef step2 fill:#fff3e0,stroke:#ef6c00;
    classDef step3 fill:#f3e5f5,stroke:#6a1b9a;
    classDef step4 fill:#fce4ec,stroke:#ad1457;
    classDef step5 fill:#e8f5e9,stroke:#2e7d32;
```

We define a core class [`MathProblem`](https://github.com/project-numina/mathlete/blob/0acc7b0ff3cf813d5cd9202bd3f2d47042a8f7b6/mathlete/utils/constants.py#L24) to hold all data related to a problem.

From our base app dataset, we define the `MathProblemCollection` class as a list of `MathProblem` that can be instantiated as

```python
df = pd.read_parquet('/path/to/app_data.parquet')
problems = MathProblemCollection.from_dataframe(df)
```

## Search Feature
To allow efficient search on the problems dataset, we proceed as follows in [`search.py`](https://github.com/project-numina/mathlete/blob/main/mathlete/search/search.py):

```mermaid
flowchart TD
    subgraph Training["Training Phase"]
        A[(Full Dataset)] --> B[Hybrid Retriever]
        B --> |Fit| C[["Trained Retriever\n(Lexical + Semantic)"]]
    end

    subgraph Search["Search Phase"]
        D[(User Query)] --> E[LLM Decomposition]
        E --> F[Keyword Filters]
        E --> G[Free Text Query]

        H[(Dataset)] --> |Apply Filters| I[(Filtered Dataset)]
        F --> |Filter| I

        I --> J[Hybrid Search]
        G --> J
        C --> |Use| J

        J --> K[(Best Matching Problems)]
    end

    style Training fill:#e6f3ff,stroke:#4a90e2
    style Search fill:#fff3e6,stroke:#e2844a
```

1. At train time, a hybrid retriever (combining lexical and semantic retrievers) is fitted on the full dataset (see `/search/retrievers`).
2. During search, an LLM decomposes the user query in keyword filters (see `/search/search.py`).
2. The filters are applied to the dataset.
3. The remaining free text search is then transformed with the hybrid retriever from step 1, and the best matching problems from the filtered dataset is returned.

Specifically, we use a classic `BM25Retriever` lexical retriever and `SemanticRetriever`, a SentenceTransformer based semantic retriever which uses by default the `all-MiniLM-L6-v2` model. Those retrievers are merged in a hybrid retriever based on reciprocal rank fusion. In an Instructor-like approach, we chose to embed together `problem` and `informal_solution` by adding a prefix instruction string.

Note that the retrievers locally cache their embeddings (in `.cache`).

### Search Evaluation
Before deploying the app, we can try to bootstrap search evaluation through synthetic queries. This is achieved by querying gpt-4 to formulate various queries for a given problem of the dataset, as exposed in `evaluation/synthetic.py`. In particular we generate two kinds of queries:
1. **Topic-based queries**: These are queries related to general topics or areas of math (e.g., differentiation, inequalities, sequences).
2. **Technique-based queries**: These queries focus on specific techniques, methods, or approaches used to solve the problem (e.g., induction, integration,...).

The generated synthetic queries are then saved in `/data/synthetic_queries_timestamp.csv`.

### Tag Fuzzy Matching
Based on the generated synthetic queries as described above, we extract tags after running a tf-idf analysis on the topic-based and technique-based queries (see [tags_generator](https://github.com/project-numina/mathlete/blob/main/mathlete/data_processing/extract_tags/tags_generator.py)).
We then build in [tags_matcher.py](https://github.com/project-numina/mathlete/blob/main/mathlete/search/tags/tags_matcher.py) a system for fuzzy tag matching against the problems tags. This is meant to propose filter suggestions to the user enter his/her search queries.

```python
from mathlete.search.tags.tags_matcher import MetadataFuzzyMatcher

fuzzy_matcher = MetadataFuzzyMatcher(problems) #problems is a List[Problem] or MathProblemCollection.
query = "functional inequality"
result = await matcher.match_query(query)

for field, matches in result["metadata_matches"].items():
    print(f"{field}:")
    for match in matches:
        print(f"  - {match['value']} (score: {match['score']})")

    print("\nMatching Problems:")
    for problem in result["problem_matches"]:
        print(problem)
```

### Query Suggestion
At the end of a session, we might want to suggest new related queries based the original queries. This is done [here](https://github.com/project-numina/mathlete/blob/main/mathlete/search/related_queries/related_queries.py). Later we could enrich this with user information from the session.

```python
from mathlete.search.related_queries import RelatedQueriesGenerator

generator = RelatedQueriesGenerator()
original_query = "pigeonhole principle"
response = generator.suggest_queries(query=original_query)
related_query = response.query
```

## Practice Feature
If we choose the autoformalization route, this will be the overall process:

0. Set up a working Lean environment (follow the instructions steps [here](https://github.com/project-numina/autoformalizer/)):

```bash
export AUTOFORMALIZER_WORKSPACE=your_workspace
```
1. A user writes an informal proof for a problem and submits it.
2. We try to formalize the proof in Lean4. This is currenlty done with gpt-4 (see [this](https://github.com/project-numina/mathlete/blob/main/mathlete/autoformalization/proof_formalization.py), but hopefully we'll have in-house dedicated model for that at some point.
3. We get Lean4 feedback (`/feedback/lean4_feedback.py`) by executing the Lean4 code provided by the last step.
4. Finally, we ask gpt-4 to provide a general proof feedback (`/feedback/proof_feedback.py`). There are two levels of feedback which are combined:
   - informal proof feedback that looks for mathematical errors and compares the user tentative proof and the golden solution.
   - formal proof feedback that interprets the feedback from executing Lean4 generated code.


```mermaid
flowchart LR
    subgraph Input["User Input"]
        A[(Informal Proof)]
    end

    subgraph Processing["Processing Steps"]
        B[GPT-4 Formalization]
        C[(Lean4 Code)]
        D[Lean4 Execution]
        E[(Lean4 Feedback)]
        F[GPT-4 Analysis]
        G[(Final Proof Feedback)]
    end

    A --> B
    B --> C
    E --> F

    C -- lake run --> D
    D -- lean4_feedback.py --> E
    F -- proof_feedback.py --> G

    style Input fill:#e6f3ff,stroke:#4a90e2
    style Processing fill:#fff3e6,stroke:#e2844a
```

To test this functionality, we set up a problem pipeline in `/tests/test_proof_pipeline.py` (where we can cheat and use the existing Lean4 code directly to better understand the proof feedback). Here is an example of feedback

```json
{
    "problem_uuid": "bd3ca75b-a124-5b77-85bf-996895a051d9",
    "summary": "The proof has both mathematical and formalization issues. Mathematical issues: The proof contains an initial incorrect application of the cosine double angle formula and lacks the rigorous verification of the derived solution set. Formalization issues: The Lean4 proof formalization contains incomplete parts marked by 'sorry', indicating unfinished segments of logical reasoning. Several `cos` simplifications and the required substantiations are inadequately formalized, analogous to the informal proof's trigonometric manipulations. Ensuring complete logical transitions and using correct syntax would solidify the formal proof.",
    "has_mathematical_errors": true,
    "has_formalization_errors": true,
    "errors": [
      {
        "informal_proof_location": "Since \\(\\cos 2 x=1+\\cos ^{2} x\\) ...",
        "specific_error_category": "incorrect_application_of_theorem",
        "explanation": "The statement \\(\\cos 2 x=1+\\cos ^{2} x\\) is incorrect. The correct double angle formula for cosine is \\(\\cos 2x = 2\\cos^2 x - 1\\).",
        "suggested_hint": "Revisit the trigonometric identities, particularly the double angle formula for cosine, and re-evaluate how \\(\\cos 2x\\) impacts the rest of your calculations."
      },
      {
        "informal_proof_location": "Solution involves applying trigonometric identities to simplify the equation and identify possible solutions for x.",
        "specific_error_category": "syntax_error",
        "explanation": "The proof contains tactical operations that lack justification and are tagged with 'sorry'. These placeholders indicate that certain steps in the formal proof are incomplete or unverified."
      }]
}
```

⚠️ However, it's unlikely that we will autoformalize user's proof attempts and so we will restrict ourselves to informal feedback. ⚠️

## Hints Suggestion
Beyond cold start hints that are generated once and for all and added as problem metadata, we can also suggest contextualized hints: this is the purpose of [this LLM call](https://github.com/project-numina/mathlete/blob/main/mathlete/hints/infer_hints.py).

## Problem Recommendation and Learning Paths
- The `ProblemRecommender` (in [recommender.py](https://github.com/project-numina/mathlete/blob/main/mathlete/recommendation/recommender.py)) is a content-based recommender system.
- The `LearningPathGenerator` (in [learning_path_generator.py](https://github.com/project-numina/mathlete/blob/main/mathlete/recommendation/learning_paths/learning_path_generator.py) generate personalized learning paths based on problem similarity, difficulty progression, and user history. It uses graph-based algorithms, clustering, and heuristics to recommend problems and create adaptive learning experiences.

### 1. Graph-Based Similarity
The system constructs a **similarity (directed) graph** (in [similarity_graph_builder](https://github.com/project-numina/mathlete/blob/main/mathlete/graph/similarity_graph_builder.py)) where nodes represent problems and edges represent similarity relationships. Edge weights are computed based on:
- **Problem embeddings**: High-dimensional vectors representing problem content.
- **Difficulty progression**: Ensures smooth transitions between problems of varying difficulty levels.

### 2. Problem Recommendation
The recommedner generates personalized learning paths using Maximum Marginal Relevance (MMR) that balances:
- Relevance: Similarity to problems the user has already solved.
- Diversity: Avoids recommending problems that are too similar to recently solved ones.

Here is an usage example:

```python
from mathlete.recommendation.recommender import ProblemRecommender

recommender = ProblemRecommender(problem_manager=problem_manager)
problem_ids = list(problem_manager.get_problem_ids())
current_problems = problem_ids[:10]

recommended_problems = recommender.recommend_problems(start_problems=current_problems, recency_window=5, max_problems=5)
```

### 3. Cold Start
For new users with no history, the system uses a [cold start heuristic](https://github.com/project-numina/mathlete/blob/main/mathlete/recommendation/cold_start_manager.py) to recommend diverse problems. It balances difficulty levels to ensure a smooth learning curve by doing clustering for each difficulty strata (easy, medium, hard).

### 4. Bridging Path Generation
The system can generate [bridging paths](https://github.com/project-numina/mathlete/blob/800b591b518863941c211b717eaa28675ce261c5/mathlete/recommendation/learning_paths/path_finder.py#L289) to help users transition between topics or difficulty levels. For instance to propose a learning path from the user current problems to IMO problems.

## Graph Visualization
To provide an interative way to explore problems in the webapp, we propose to leverage a graph visualization based on [hierarchical clustering](https://github.com/project-numina/mathlete/blob/main/mathlete/clustering/math_clusterer.py) and [UMAP projections](https://github.com/project-numina/mathlete/blob/800b591b518863941c211b717eaa28675ce261c5/mathlete/embeddings/problem_embeddings.py#L120C1-L120C26) of the problems embeddings. The graph is build in [here](https://github.com/project-numina/mathlete/blob/800b591b518863941c211b717eaa28675ce261c5/mathlete/graph/similarity_graph_builder.py#L119).
