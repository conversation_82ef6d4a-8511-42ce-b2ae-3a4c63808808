http {
    include       mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;
    sendfile        on;
    keepalive_timeout  65;

    # Cache control variable for later use
    map $sent_http_content_type $cache_control {
      default                   no-cache;
      application/font-woff     public;
      application/javascript    public;
      application/x-font-ttf    public;
      image/png                 public;
      image/jpeg                public;
      image/svg+xml             public;
      text/css                  public;
    }

    proxy_cache_path /var/cache/nginx/assets levels=1:2 keys_zone=assets_cache:10m max_size=100m inactive=30d use_temp_path=off;

    server {
        # server_name ~(app|staging)\.projectnumina\.ai$;
        listen 80;

        client_max_body_size 50M;

        location /assets {
            proxy_pass http://frontend:5173;

            proxy_set_header Cache-Control "public, max-age=31536000";

            gzip on;
            gzip_types application/font-woff2 application/font-woff application/octet-stream;

            proxy_cache assets_cache;
            proxy_cache_valid 200 30d;
            proxy_cache_valid 404 1m;

            add_header X-Proxy-Cache $upstream_cache_status;
        }

        location /api {
            proxy_pass http://backend:8000;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_cache_bypass $http_upgrade;
        }

        location / {
            proxy_pass http://frontend:5173;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_cache_bypass $http_upgrade;
        }
    }

    # server {
    #     server_name ~(.*\.)?projectnumina\.ai$;
    #     listen 80 default_server;

    #     client_max_body_size 1M;

    #     location / {
    #         proxy_pass https://projectnumina.wordpress.com;
    #         add_header Cache-Control $cache_control;
    #         proxy_set_header Authorization "";
    #         proxy_next_upstream error timeout non_idempotent;
    #     }
    # }
}

events {
    worker_connections  1024;
}
