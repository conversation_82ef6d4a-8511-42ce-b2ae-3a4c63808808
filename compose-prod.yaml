services:
  backend:
    image: ${BACKEND_DOCKER_IMAGE}
    ports:
      - "8000:8000"
    environment:
      - DATABASE_USER=${DATABASE_USER}
      - DATABASE_PASSWORD=${DATABASE_PASSWORD}
      - DATABASE_NAME=${DATABASE_NAME}
      - DATABASE_HOST=postgres
      - DATABASE_PORT=${DATABASE_PORT}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - TOGETHER_API_KEY=${TOGETHER_API_KEY}
      - DATABASE_URL=${DATABASE_URL}
    depends_on:
      - postgres

  frontend:
    image: ${FRONTEND_DOCKER_IMAGE}
    ports:
      - "5173:5173"
    environment:
      - FRONTEND_DOCKER_IMAGE
      - TIPTAP_PRO_TOKEN=${TIPTAP_PRO_TOKEN}

  nginx:
    image: nginx:latest
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - backend
      - frontend

  postgres:
    image: postgres:latest
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=${DATABASE_USER}
      - POSTGRES_PASSWORD=${DATABASE_PASSWORD}
      - POSTGRES_DB=${DATABASE_NAME}
    volumes:
      - ./data:/var/lib/postgresql/data

  migrator:
    image: node:lts-slim
    volumes:
      - ./app:/app
    working_dir: /app
    environment:
      - DATABASE_URL=${DATABASE_URL}
    depends_on:
      - postgres
