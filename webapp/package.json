{"name": "@mathlete/webapp", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "shadcn:add": "bunx shadcn@latest add", "lint": "biome lint", "lint:fix": "biome check --assists-enabled=true --unsafe --write .", "types": "tsc -b", "build": "tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@floating-ui/react": "^0.27.8", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-navigation-menu": "^1.2.12", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.3", "@tanstack/react-query": "^5.72.0", "@tanstack/react-query-devtools": "^5.72.0", "@tiptap-pro/extension-mathematics": "^2.18.0-beta.6", "@tiptap/extension-color": "^2.14.0", "@tiptap/extension-heading": "^2.12.0", "@tiptap/extension-highlight": "^2.14.0", "@tiptap/extension-placeholder": "^2.11.7", "@tiptap/extension-text-align": "^2.12.0", "@tiptap/extension-text-style": "^2.14.0", "@tiptap/react": "^2.11.7", "@tiptap/starter-kit": "^2.11.7", "axios": "^1.8.4", "camelcase-keys": "^9.1.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "framer-motion": "^12.6.3", "js-confetti": "^0.12.0", "katex": "^0.16.21", "lodash": "^4.17.21", "lucide-react": "^0.513.0", "next-themes": "^0.4.6", "react": "^19.0.0", "react-apexcharts": "^1.7.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "react-hotkeys-hook": "^5.0.1", "react-markdown": "^10.1.0", "react-sticky-el": "^2.1.1", "rehype-katex": "^7.0.1", "remark-math": "^6.0.0", "shiki": "^3.4.2", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.3", "tw-animate-css": "^1.2.5", "wouter": "^3.6.0", "zod": "^3.25.7"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@types/lodash": "^4.17.16", "@types/node": "^22.14.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/react-dropzone": "^5.1.0", "@vitejs/plugin-react": "^4.3.4", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}