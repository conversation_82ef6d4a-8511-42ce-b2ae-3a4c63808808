import {
	type ReactNode,
	createContext,
	useContext,
	useEffect,
	useRef,
	useState,
} from "react"

interface TimerContextType {
	time: number
	isRunning: boolean
	startTimer: () => void
	pauseTimer: () => void
	resetTimer: () => void
	formatTime: (time: number) => string
}

export const formatTime = (time: number) => {
	const minutes = Math.floor(time / 60)
	const seconds = time % 60
	return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`
}

const defaultContext: TimerContextType = {
	time: 0,
	isRunning: false,
	startTimer: () => {},
	pauseTimer: () => {},
	resetTimer: () => {},
	formatTime,
}

const TimerContext = createContext<TimerContextType>(defaultContext)

export const useTimer = () => useContext(TimerContext)

interface TimerProviderProps {
	children: ReactNode
}

export const TimerProvider = ({ children }: TimerProviderProps) => {
	const [time, setTime] = useState(0)
	const [isRunning, setIsRunning] = useState(false)
	const intervalId = useRef<NodeJS.Timeout | null>(null)

	const startTimer = () => {
		if (!isRunning) {
			setIsRunning(true)
		}
	}

	const pauseTimer = () => {
		if (isRunning) {
			setIsRunning(false)
		}
	}

	const resetTimer = () => {
		setIsRunning(false)
		setTime(0)
	}

	// biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
	useEffect(() => {
		startTimer()
	}, [])

	useEffect(() => {
		if (isRunning) {
			intervalId.current = setInterval(() => {
				setTime((prevTime) => prevTime + 1)
			}, 1000)
		} else if (intervalId.current !== null) {
			clearInterval(intervalId.current)
		}

		return () => {
			if (intervalId.current !== null) {
				clearInterval(intervalId.current)
			}
		}
	}, [isRunning])

	const value = {
		time,
		isRunning,
		startTimer,
		pauseTimer,
		resetTimer,
		formatTime,
	}

	return <TimerContext.Provider value={value}>{children}</TimerContext.Provider>
}
