import type { Camelize } from "@/types/camelize"
import type { Event } from "@/types/events"
import type { quick_picks } from "@/types/prisma/index"
import type { Problem } from "@/types/problems"

export type QuickPick = Camelize<quick_picks> & {
	event?: Event
}

export type ProblemSearchResults = {
	metadataMatches: {
		problemType: string
		// difficulty: difficulty
		// exam_category: exam_category
	}
	skip: number
	limit: number
	total: number
	validQuery: boolean
	reason: string
	problemMatches: Problem[]
}
