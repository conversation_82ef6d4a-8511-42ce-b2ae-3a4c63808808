import axios, { AxiosError, isAxiosError } from "axios"
import camelcaseKeys from "camelcase-keys"

export const getAxiosStatusCodeValidator =
	(statusCode: number) =>
	<T = unknown>(error: unknown): error is AxiosError<T> =>
		isAxiosError(error) && error.response?.status === statusCode

export const isAxiosNotFoundError = getAxiosStatusCodeValidator(404)
export const isAxiosUnauthorizedError = getAxiosStatusCodeValidator(401)
export const isAxiosBadRequestError = getAxiosStatusCodeValidator(400)
export const isAxiosForbiddenError = getAxiosStatusCodeValidator(403)

const createHTTPClient = (baseURL: string) => {
	const client = axios.create({ baseURL })

	client.interceptors.response.use(
		(response) => {
			const camelcasedData = camelcaseKeys(response.data, { deep: true })
			return { ...response, data: camelcasedData }
		},
		async (error) => {
			if (error instanceof AxiosError) {
				return Promise.reject(error.response?.data)
			}
			return Promise.reject(error)
		},
	)

	return client
}

export const apiClient = createHTTPClient(import.meta.env.VITE_API_URL)
