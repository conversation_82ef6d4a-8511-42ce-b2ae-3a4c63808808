@import url("https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:ital,wght@0,100..700;1,100..700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=IBM+Plex+Mono:ital,wght@0,100..700;1,100..700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=JetBrains+Mono:ital,wght@0,100..800;1,100..800&display=swap");

@import "tailwindcss";
@import "tailwindcss/preflight.css" layer(base);
@import "tw-animate-css";
@plugin "@tailwindcss/typography";

:root {
	--default-font-family: "IBM Plex Sans", system-ui, -apple-system,
		BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;
	--font-mono: "JetBrains Mono", ui-monospace, SFMono-Regular, Menlo, Monaco,
		Consolas, "Liberation Mono", "Courier New", monospace;
	--font-ibm-mono: "IBM Plex Mono", ui-monospace, SFMono-Regular, Menlo, Monaco,
		Consolas, "Liberation Mono", "Courier New", monospace;

	--background: #fefaf6;
	--foreground: #1e2d32;
	--card: #ffffff;
	--card-foreground: #1f1f1f;
	--popover: #ffffff;
	--popover-foreground: #4a3b33;

	--primary: #bb470b;
	--primary-foreground: #ffffff;
	--secondary: #016d6d;
	--secondary-foreground: #ffffff;
	--muted: #f1e9da;
	--muted-foreground: #46596e;
	--accent: #f1dace;
	--accent-foreground: #4a3b33;
	--destructive: #991b1b;
	--destructive-foreground: #ffffff;
	--border: #d8cfbe;
	--input: #fdf8f2;
	--ring: #b45309;

	--chart-1: #308aae;
	--chart-2: #e4632b;
	--chart-3: #58b87a;
	--chart-4: #b998ec;
	--chart-5: #f3c85b;

	/* Colors overrides */
	--green-200: #c2e3d4;
	--green-300: #80cfb3;
	--green-700: #175a46;
	--green-800: #1a3d38;
	--green-900: #1f2b2c;

	--red-200: #f4d6cf;
	--red-300: #ebb6a7;
	--red-700: #7b2d27;
	--red-800: #55342f;

	--sidebar: #1e2d32;
	--sidebar-foreground: #233444;
	--sidebar-primary: #d46a33;
	--sidebar-primary-foreground: #ffffff;
	--sidebar-accent: #5d7d99;
	--sidebar-accent-foreground: #ffffff;
	--sidebar-border: #2a3a3d;
	--sidebar-ring: #d26234;

	--radius: 0.65rem;
	--shadow-2xs: 0px 2px 3px 0px hsl(28 18% 25% / 0.0);
	--shadow-xs: 0px 2px 3px 0px hsl(28 18% 25% / 0.0);
	--shadow-sm: 0px 2px 3px 0px hsl(28 18% 25% / 0.0), 0px 1px 2px -1px
		hsl(28 18% 25% / 0.0);
	--shadow: 0px 2px 3px 0px hsl(28 18% 25% / 0.0), 0px 1px 2px -1px
		hsl(28 18% 25% / 0.0);
	--shadow-md: 0px 2px 3px 0px hsl(28 18% 25% / 0.0), 0px 2px 4px -1px
		hsl(28 18% 25% / 0.0);
	--shadow-lg: 0px 2px 3px 0px hsl(28 18% 25% / 0.0), 0px 4px 6px -1px
		hsl(28 18% 25% / 0.0);
	--shadow-xl: 0px 2px 3px 0px hsl(28 18% 25% / 0.0), 0px 8px 10px -1px
		hsl(28 18% 25% / 0.0);
	--shadow-2xl: 0px 2px 3px 0px hsl(28 18% 25% / 0.0);
}

@theme inline {
	--default-font-family: var(--default-font-family);
	--font-mono: var(--font-mono);
	--font-ibm-mono: var(--font-ibm-mono);

	--color-background: var(--background);
	--color-foreground: var(--foreground);
	--color-card: var(--card);
	--color-card-foreground: var(--card-foreground);
	--color-popover: var(--popover);
	--color-popover-foreground: var(--popover-foreground);

	--color-primary: var(--primary);
	--color-primary-foreground: var(--primary-foreground);
	--color-secondary: var(--secondary);
	--color-secondary-foreground: var(--secondary-foreground);
	--color-muted: var(--muted);
	--color-muted-foreground: var(--muted-foreground);
	--color-accent: var(--accent);
	--color-accent-foreground: var(--accent-foreground);
	--color-destructive: var(--destructive);
	--color-destructive-foreground: var(--destructive-foreground);
	--color-border: var(--border);
	--color-input: var(--input);
	--color-ring: var(--ring);

	--color-chart-1: var(--chart-1);
	--color-chart-2: var(--chart-2);
	--color-chart-3: var(--chart-3);
	--color-chart-4: var(--chart-4);
	--color-chart-5: var(--chart-5);
	--color-sidebar: var(--sidebar);

	/* Colors overrides */
	--color-green-200: var(--green-200);
	--color-green-300: var(--green-300);
	--color-green-700: var(--green-700);
	--color-green-800: var(--green-800);
	--color-green-900: var(--green-900);

	--color-red-200: var(--red-200);
	--color-red-300: var(--red-300);
	--color-red-700: var(--red-700);
	--color-red-800: var(--red-800);

	--color-sidebar-foreground: var(--sidebar-foreground);
	--color-sidebar-primary: var(--sidebar-primary);
	--color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
	--color-sidebar-accent: var(--sidebar-accent);
	--color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
	--color-sidebar-border: var(--sidebar-border);
	--color-sidebar-ring: var(--sidebar-ring);

	--radius-sm: calc(var(--radius) - 4px);
	--radius-md: calc(var(--radius) - 2px);
	--radius-lg: var(--radius);
	--radius-xl: calc(var(--radius) + 4px);

	--shadow-2xs: var(--shadow-2xs);
	--shadow-xs: var(--shadow-xs);
	--shadow-sm: var(--shadow-sm);
	--shadow: var(--shadow);
	--shadow-md: var(--shadow-md);
	--shadow-lg: var(--shadow-lg);
	--shadow-xl: var(--shadow-xl);
	--shadow-2xl: var(--shadow-2xl);
}

@layer base {
	* {
		@apply border-border outline-ring/50;
	}

	body {
		@apply bg-background text-foreground;
	}

	button:not(:disabled),
	[role="button"]:not(:disabled) {
		cursor: pointer;
	}
}
