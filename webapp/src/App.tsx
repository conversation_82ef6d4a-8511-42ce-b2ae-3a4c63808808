import { Toaster } from "@/components/ui/sonner"
import { QueryClientProvider } from "@tanstack/react-query"
import { ReactQueryDevtools } from "@tanstack/react-query-devtools"

import { queryClient } from "@/lib/queryClient"
import { Router } from "@/pages/Router"

export const App = () => {
	return (
		<QueryClientProvider client={queryClient}>
			<main className="w-screen max-w-screen prose prose-sm prose-p:m-0 prose-img:m-0 prose-headings:m-0 prose-a:font-normal prose-a:no-underline">
				<Router />
			</main>
			<Toaster />
			<ReactQueryDevtools />
		</QueryClientProvider>
	)
}
