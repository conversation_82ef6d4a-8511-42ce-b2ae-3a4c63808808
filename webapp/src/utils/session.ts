import { queryClient } from "@/lib/queryClient"
import type { Problem } from "@/types/problems"
import { useMutation, useQuery } from "@tanstack/react-query"
import _ from "lodash"
import { useCallback, useMemo } from "react"

export type ProblemStatus = "in-progress" | "solved" | "failed"

export type SessionProblem = {
	problem: Problem
	status: ProblemStatus
	timeSpent: number
	hintsUsed: number
}

export type SessionStats = {
	problemsSolved: number
	totalHintsUsed: number
	totalTimeSpent: number
}

export type Session = {
	goal: string
	problems: SessionProblem[]
}

const DEFAULT_SESSION = {
	goal: "",
	problems: [],
}

export const useSession = () => {
	const { data: maybeSession, isSuccess } = useQuery({
		queryKey: ["session"],
		queryFn: () => {
			const storedSession = sessionStorage.getItem("axion-session")
			if (storedSession) return JSON.parse(storedSession) as Session
			return DEFAULT_SESSION
		},
	})
	const session = maybeSession ?? DEFAULT_SESSION

	const { mutate: updateSession } = useMutation({
		mutationFn: async (session: Session) => {
			sessionStorage.setItem("axion-session", JSON.stringify(session))
			return session
		},
		onSuccess: async () =>
			queryClient.invalidateQueries({ queryKey: ["session"] }),
	})

	const stats = useMemo<SessionStats>(() => {
		return session.problems.reduce(
			(acc, p) => {
				acc.problemsSolved += p.status === "solved" ? 1 : 0
				acc.totalHintsUsed += p.hintsUsed
				acc.totalTimeSpent += p.timeSpent
				return acc
			},
			{
				problemsSolved: 0,
				totalHintsUsed: 0,
				totalTimeSpent: 0,
			},
		)
	}, [session.problems])

	const addProblemToSession = (query: string, problem: Problem) => {
		updateSession({
			...session,
			goal: query,
			problems: [
				...session.problems,
				{
					problem,
					status: "in-progress",
					timeSpent: 0,
					hintsUsed: 0,
				},
			],
		})
	}

	const findCurrentProblem = () => {
		const currentProblem = session.problems.find(
			(p) => p.status === "in-progress",
		)
		const otherProblems = session.problems.filter(
			(p) => p.status !== "in-progress",
		)
		return { currentProblem, otherProblems }
	}

	const updateSessionOnHintRevealed = () => {
		const { currentProblem, otherProblems } = findCurrentProblem()

		if (!currentProblem) return
		updateSession({
			...session,
			problems: [
				...otherProblems,
				{
					...currentProblem,
					hintsUsed: currentProblem.hintsUsed + 1,
				},
			],
		})
	}

	const updateSessionOnProblemVerified = (
		status: ProblemStatus,
		timeSpent: number,
	) => {
		const { currentProblem, otherProblems } = findCurrentProblem()

		if (!currentProblem) return
		updateSession({
			...session,
			problems: [
				...otherProblems,
				{
					...currentProblem,
					status,
					timeSpent,
				},
			],
		})
	}

	const computeFocusMeter = useCallback(
		() => computeFocusMeterForSession(session.problems),
		[session.problems],
	)

	return {
		stats,
		session,
		isReady: isSuccess,
		addProblemToSession,
		updateSessionOnHintRevealed,
		updateSessionOnProblemVerified,
		computeFocusMeter,
	}
}

/*
  Compute the score for a problem
	+1 if the problem was solved
	+0.5 if no hints were used
	−0.5 if a hint was used
	TODO: the above
	+0.5 if the user spent more than the average time (indicates deep thinking)
	+1 if the user viewed the formal proof (for Formalism only)
 */
const computeProblemScore = (problem: SessionProblem) => {
	let score = 0

	if (problem.status === "solved") score += 1
	if (problem.hintsUsed === 0) {
		score += 0.5
	} else {
		score -= 0.5
	}

	return Math.max(0, score)
}

export const computeFocusMeterForSession = (problems: SessionProblem[]) => {
	const problemsByDomain = _.groupBy(problems, (p) => p.problem.domain)
	const scoreByDomain = _.mapValues(problemsByDomain, (problems) => {
		return _.sumBy(problems, computeProblemScore)
	})
	const totalScore = _.sum(_.values(scoreByDomain))

	return _.mapValues(scoreByDomain, (score) => {
		return (score / totalScore) * 100
	})
}
