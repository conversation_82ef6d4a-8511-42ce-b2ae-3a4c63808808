type OS = "mac" | "windows" | "linux" | "android" | "ios" | "unknown"

export const detectOS = (): OS => {
	const userAgent = navigator.userAgent.toLowerCase()

	if (/windows nt/.test(userAgent)) return "windows"
	if (/macintosh|mac os x/.test(userAgent)) return "mac"
	if (/android/.test(userAgent)) return "android"
	if (/iphone|ipad|ipod/.test(userAgent)) return "ios"
	if (/linux/.test(userAgent)) return "linux"

	return "unknown"
}
