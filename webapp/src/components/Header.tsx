import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/tailwind"
import { Link } from "wouter"

type Props = {
	variant?: "light" | "dark"
	withNoLoginCta?: boolean
	withAxion?: boolean
}

export const Header = ({
	variant = "light",
	withNoLoginCta,
	// @ts-expect-error TODO: put it back when necessary
	withAxion = false,
}: Props) => {
	return (
		<Link to="/">
			<div className="w-full p-6 flex flex-row gap-3 items-center">
				<div className=" flex flex-col">
					<div
						className={cn("flex flex-row items-center gap-2 -ml-0.5", {
							"text-white": variant === "light",
							"text-muted-foreground": variant === "dark",
						})}
					>
						<div className="text-3xl font-bold">AXION</div>
						<Badge className="h-6 bg-red-200 text-red-950 text-xs">Beta</Badge>
					</div>
					{withNoLoginCta && (
						<div className="text-[13px] leading-none font-ibm-mono italic text-red-200">
							No login required to try!
						</div>
					)}
				</div>
				{/*{withAxion && <AskAxion />}*/}
			</div>
		</Link>
	)
}
