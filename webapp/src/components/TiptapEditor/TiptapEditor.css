.tiptap {
	:first-child {
		margin-top: 0;
	}

	/* List styles */
	ul,
	ol {
		padding: 0 1rem;
		margin: 1.25rem 1rem 1.25rem 0.4rem;
	}

	ul li {
		list-style-type: disc;
	}

	ol li {
		list-style-type: decimal;
	}

	li,
	li p {
		margin-top: 0.25em;
		margin-bottom: 0.25em;
	}

	li::marker {
		color: var(--color-gray-800);
	}

	/* Heading styles */
	h1,
	h2,
	h3,
	h4,
	h5,
	h6 {
		line-height: 1.1;
		margin-top: 2.5rem;
		text-wrap: pretty;
	}

	h1,
	h2 {
		margin-top: 3.5rem;
		margin-bottom: 1.5rem;
	}

	h1 {
		font-size: 1.4rem;
	}

	h2 {
		font-size: 1.2rem;
	}

	h3 {
		font-size: 1.1rem;
	}

	h4,
	h5,
	h6 {
		font-size: 1rem;
	}

	/* Code and preformatted text styles */
	code {
		background-color: var(--color-gray-700);
		border-radius: 0.4rem;
		color: #fff;
		font-size: 0.85rem;
		padding: 0.25em 0.3em;
	}

	pre {
		background: #000;
		border-radius: 0.5rem;
		color: #fff;
		font-family: "JetBrainsMono", monospace;
		margin: 1.5rem 0;
		padding: 0.75rem 1rem;
	}

	pre code {
		background: none;
		color: inherit;
		font-size: 0.8rem;
		padding: 0;
	}

	blockquote {
		border-left: 3px solid var(--color-gray-300);
		margin: 1.5rem 0;
		padding-left: 1rem;
	}

	hr {
		border: none;
		border-top: 1px solid var(--color-gray-200);
		margin: 2rem 0;
	}

	/* Placeholder */
	p.is-editor-empty:first-child::before {
		color: var(--muted-foreground);
		content: attr(data-placeholder);
		float: left;
		height: 0;
		pointer-events: none;
	}

	/* LateX editor */
	.Tiptap-mathematics-editor {
		background: var(--color-gray-700);
		color: #fff;
		font-family: monospace;
		padding: 0.2rem 0.5rem;
	}

	.Tiptap-mathematics-render--editable {
		cursor: pointer;
		transition: background 0.2s;
	}

	.Tiptap-mathematics-render--editable:hover {
		background: var(--color-gray-300);
	}

	.Tiptap-mathematics-editor,
	.Tiptap-mathematics-render {
		border-radius: 0.25rem;
		display: inline-block;
	}
}
