import { MathStatement } from "@/components/MathStatement"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogDescription,
	Di<PERSON>Footer,
	<PERSON>alogHeader,
	DialogTitle,
} from "@/components/ui/dialog"
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
} from "@/components/ui/form"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { apiClient } from "@/lib/apiClient"
import { cn } from "@/lib/tailwind"
import { zodResolver } from "@hookform/resolvers/zod"
import { useMutation } from "@tanstack/react-query"
import { ChevronDown } from "lucide-react"
import { useEffect, useState } from "react"
import { useForm } from "react-hook-form"
import { toast } from "sonner"
import { z } from "zod"

type Props = {
	title: string
	description: string
	statementLabel: string
	statement: string
	problemUuid?: string
	isOpen: boolean
	onClose: () => void
}

const reportSchema = z.object({
	statement: z.string(),
	feedback: z.string().optional(),
	problemUuid: z.string().optional(),
})

type ReportInput = z.infer<typeof reportSchema>

export const BugReportDialog = ({
	title,
	description,
	statementLabel,
	statement,
	problemUuid,
	isOpen,
	onClose,
}: Props) => {
	const [isStatementExpanded, setIsStatementExpanded] = useState(false)

	const form = useForm<ReportInput>({
		resolver: zodResolver(reportSchema),
		defaultValues: {
			statement,
			feedback: "",
			problemUuid,
		},
	})

	useEffect(() => {
		form.setValue("statement", statement)
	}, [statement, form])

	const { mutateAsync: sendReport, isPending } = useMutation({
		mutationFn: async (data: ReportInput) => {
			await apiClient.post("/bug-report", {
				problem_uuid: data.problemUuid,
				statement: data.statement,
				feedback: data.feedback,
			})
		},
		onSuccess: () => {
			toast("Bug report sent!")
			onClose()
		},
		onError: (error) => {
			toast("Bug report failed!")
			console.error("ERROR:", error)
		},
	})

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent>
				<DialogHeader>
					<DialogTitle>{title}</DialogTitle>
					<DialogDescription>{description}</DialogDescription>
				</DialogHeader>
				<Form {...form}>
					<form
						onSubmit={form.handleSubmit((data) => sendReport(data))}
						className="space-y-8"
					>
						<div className="flex flex-col gap-4 py-4">
							<div className="flex flex-col gap-2">
								<Label>{statementLabel}</Label>
								<div
									className={cn(
										"relative bg-muted px-2 rounded-sm border-border border-1 text-sm overflow-hidden",
										{ "h-24": !isStatementExpanded },
									)}
								>
									<MathStatement statement={statement} />
									<button
										type="button"
										onClick={() => setIsStatementExpanded((prev) => !prev)}
										className="absolute bottom-0 cursor-pointer flex flex-row gap-1 items-end py-2 bg-gradient-to-t from-muted via-muted to-transparent w-full h-14"
									>
										<div className="text-sm font-medium">
											{isStatementExpanded
												? "Hide full version"
												: "Show full version"}
										</div>
										<ChevronDown
											className={cn(
												"w-5 h-5 transition-transform duration-200",
												{
													"transform rotate-180": isStatementExpanded,
												},
											)}
										/>
									</button>
								</div>
							</div>
							<FormField
								control={form.control}
								name="feedback"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Optional feedback to reviewer</FormLabel>
										<FormControl>
											<Textarea
												placeholder="Explain why you believe the model's evaluation is incorrect or incomplete."
												{...field}
											/>
										</FormControl>
									</FormItem>
								)}
							/>
						</div>
						<DialogFooter>
							<div className="w-full flex flex-row gap-2 justify-end">
								<Button type="reset" variant="outline" onClick={onClose}>
									Cancel
								</Button>
								<Button
									type="submit"
									disabled={!form.formState.isValid}
									isLoading={isPending}
								>
									Save changes
								</Button>
							</div>
						</DialogFooter>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	)
}
