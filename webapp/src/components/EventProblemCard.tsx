import { ProblemCard } from "@/components/ProblemCard"
import { cn } from "@/lib/tailwind"
import type { Problem } from "@/types/problems"
import dayjs from "dayjs"

type Props = {
	dayIndex: number
	problemIndex: number
	revealedAt: Date
	problem: Problem
}

export const EventProblemCard = ({
	dayIndex,
	problemIndex,
	revealedAt,
	problem,
}: Props) => {
	// TODO: we can add the "coming soon" feature later
	const isRevealed = dayjs(revealedAt).isBefore(dayjs())
	return (
		<div className="w-[300px] h-100 bg-white rounded-sm flex flex-col gap-3 overflow-hidden border-1 border-border">
			<div
				className={cn("flex flex-col gap-1 p-4 bg-chart-1", {
					"bg-chart-1/70": !isRevealed,
				})}
			>
				<div className="text-3xl text-white font-medium">
					Problem {problemIndex + 1}
				</div>
				<div className="text-sm text-white">
					Day {dayIndex + 1}: {dayjs(revealedAt).format("dddd, MMMM D, YYYY")}
				</div>
			</div>
			<div className="h-full overflow-hidden">
				<ProblemCard problem={problem} isRevealed={isRevealed} />
			</div>
		</div>
	)
}
