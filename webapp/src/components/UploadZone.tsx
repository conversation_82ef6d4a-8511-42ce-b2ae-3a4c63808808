"use client"
import { useProblemEditor } from "@/components/ProblemEditor/index"
import {
	DropZoneArea,
	Dropzone,
	DropzoneMessage,
	DropzoneTrigger,
	useDropzone,
} from "@/components/ui/dropzone"
import { apiClient } from "@/lib/apiClient"
import { cn } from "@/lib/tailwind"
import { isAxiosError } from "axios"
import { CircleCheckBig, CircleX, CloudUploadIcon, Loader2 } from "lucide-react"
import { useRef, useState } from "react"
import { toast } from "sonner"

type UploadStatus = "idle" | "uploading" | "success" | "error"

type ButtonProps = {
	status: UploadStatus
	totalFiles: number
}

const UploadButton = ({ status, totalFiles }: ButtonProps) => {
	switch (status) {
		case "idle":
			return (
				<div className="flex flex-row items-center ">
					<div className="p-2">
						<CloudUploadIcon className="size-8" />
					</div>
					<div className="p-2 pl-0">
						<p className="text-sm font-semibold">Drag files</p>
						<p className="text-xs font-normal text-muted-foreground">
							Click to upload files
						</p>
					</div>
				</div>
			)
		case "uploading":
			return (
				<div className="flex flex-row items-center ">
					<div className="p-2">
						<Loader2 className="size-8 animate-spin" />
					</div>
					<div className="p-2 pl-0">
						<p className="text-sm font-semibold">Uploading...</p>
						<p className="text-xs font-normal text-muted-foreground">
							{totalFiles} file{totalFiles > 1 ? "s" : ""}
						</p>
					</div>
				</div>
			)
		case "success":
			return (
				<div className="flex flex-row items-center ">
					<div className="p-2">
						<CircleCheckBig className="size-8" />
					</div>
					<div className="p-2 pl-0">
						<p className="text-sm font-semibold">Upload success</p>
						<p className="text-xs font-normal text-muted-foreground">
							{totalFiles} file{totalFiles > 1 ? "s" : ""}
						</p>
					</div>
				</div>
			)
		case "error":
			return (
				<div className="flex flex-row items-center ">
					<div className="p-2">
						<CircleX className="size-8" />
					</div>
					<div className="p-2 pl-0">
						<p className="text-sm font-semibold">Error</p>
						<p className="text-xs font-normal text-muted-foreground">
							Please try again
						</p>
					</div>
				</div>
			)
	}
}

export const UploadZone = () => {
	const { editor } = useProblemEditor()

	const files = useRef<File[]>([])
	const [uploadStatus, setUploadStatus] = useState<UploadStatus>("idle")

	const dropzone = useDropzone({
		onDropFile: async (file: File) => {
			files.current.push(file)
			return {
				status: "success",
				result: true,
			}
		},
		onAllUploaded: async () => {
			try {
				const formData = new FormData()
				for (const file of files.current) {
					formData.append("images", file)
				}
				setUploadStatus("uploading")
				const response = await apiClient.post<{ results: string[] }>(
					"/ocr-upload",
					formData,
				)
				editor.commands.insertContent(
					`\n\n${response.data.results.join("\n\n")}`,
				)
				toast.success(
					`Successfully uploaded ${files.current.length} image${
						files.current.length > 1 ? "s" : ""
					}!`,
				)
				setUploadStatus("success")
			} catch (error) {
				toast.error("Failed to upload images nnn", {
					description: isAxiosError(error) ? error.response?.data : undefined,
				})
				setUploadStatus("error")
			}
		},
		onRootError: (error) => {
			if (error) {
				toast.error("Failed to upload images", {
					description: error,
				})
			}
		},
		validation: {
			accept: { "image/png": [".png"], "image/jpeg": [".jpg", ".jpeg"] },
			maxSize: 10 * 1024 * 1024,
			maxFiles: 10,
		},
	})

	return (
		<div className="w-full flex flex-col gap-4">
			<Dropzone {...dropzone}>
				<DropZoneArea
					className={cn("w-full flex flex-col gap-2 p-5 items-start border-0", {
						"bg-primary/20": dropzone.isDragActive,
					})}
				>
					<p className="text-lg text-gray-900">
						<b>Upload your proof</b> - We'll convert handwritten or printed work
						into editable text
					</p>
					<p className="text-sm text-gray-600 mb-2">
						we’ll extract it and add it to your proof.
					</p>
					<DropzoneTrigger className="border-1 border-input not-prose gap-2 p-2 bg-transparent text-sm hover:bg-primary/20">
						<UploadButton
							status={uploadStatus}
							totalFiles={files.current.length}
						/>
					</DropzoneTrigger>
				</DropZoneArea>
			</Dropzone>
			<DropzoneMessage />
		</div>
	)
}
