import { cn } from "@/lib/tailwind"
// import { detectOS } from "@/utils/os"
import type { LucideIcon } from "lucide-react"
// import { useMemo } from "react"
// import { useHotkeys } from "react-hotkeys-hook"

type Props = {
	variant: "primary" | "success" | "error"
	label: string
	description?: string
	hotkey: string
	icon: LucideIcon
	onClick: () => void
}

const VARIANTS = {
	primary: {
		bg: "bg-primary",
		icon: "text-primary-foreground",
		text: "text-primary-foreground",
		hotkey: "text-primary-foreground",
	},
	success: {
		bg: "bg-green-300",
		icon: "text-green-700",
		text: "text-green-900",
		hotkey: "text-green-700",
	},
	error: {
		bg: "bg-red-300",
		icon: "text-red-700",
		text: "text-red-700",
		hotkey: "text-red-700",
	},
}

export const ActionButtonWithKeymap = ({
	variant,
	label,
	description,
	// hotkey,
	icon: Icon,
	onClick,
}: Props) => {
	// useHotkeys(hotkey, onClick)
	//
	// const formattedHotkey = useMemo(() => {
	// 	const os = detectOS()
	// 	if (os === "mac") return hotkey.replace("meta+", "⌘")
	// 	return hotkey.replace("meta", "Ctrl")
	// }, [hotkey])

	const variants = VARIANTS[variant]

	return (
		<button
			type="button"
			className={cn(
				"group flex flex-row gap-6 items-center justify-between px-4 py-2 rounded-sm hover:bg-black",
				variants.bg,
			)}
			onClick={onClick}
		>
			<div className="flex flex-row items-center gap-2">
				<Icon className={cn("h-4 w-4 group-hover:text-white", variants.icon)} />
				<span
					className={cn(
						"text-sm font-bold group-hover:text-white",
						variants.text,
					)}
				>
					{label}
				</span>
				{description && (
					<span className={cn("text-sm group-hover:text-white", variants.text)}>
						{description}
					</span>
				)}
			</div>
			{/*<span className={cn("text-sm", variants.hotkey)}>{formattedHotkey}</span>*/}
		</button>
	)
}
