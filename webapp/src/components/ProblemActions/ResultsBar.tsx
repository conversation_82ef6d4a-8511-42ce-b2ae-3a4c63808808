import { But<PERSON> } from "@/components/ui/button"
import { PanelRight } from "lucide-react"
import { useMemo } from "react"

type Props = {
	variant: "success" | "error"
	onOpenResultDrawer: () => void
}

const variants = {
	error: {
		title: "You're almost there!",
		description: "Your proof needs stronger justification",
		style: {
			container: "bg-red-200 text-red-700",
			button: "hover:bg-red-300",
		},
	},
	success: {
		title: "Good Job!",
		description: "The proof is formally verified and correct!",
		style: {
			container: "bg-green-200 text-green-700",
			button: "hover:bg-green-300",
		},
	},
}

export const ResultsBar = ({ variant, onOpenResultDrawer }: Props) => {
	const variantProps = useMemo(() => variants[variant], [variant])

	return (
		<div
			className={`w-full flex flex-col gap-3 px-4 py-3 ${variantProps.style.container}`}
		>
			<div className="w-full flex flex-row items-center justify-between gap-8">
				<div className="w-full flex flex-row items-center justify-between">
					<div className="flex flex-row items-baseline gap-3">
						<p className="text-lg font-bold">{variantProps.title}</p>
						<p className="text-sm">{variantProps.description}</p>
					</div>
					<Button
						variant="ghost"
						className={variantProps.style.button}
						onClick={onOpenResultDrawer}
					>
						<p className="underline">Check Details</p>
						<PanelRight />
					</Button>
				</div>
			</div>
		</div>
	)
}
