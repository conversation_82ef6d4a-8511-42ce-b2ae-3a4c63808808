import { RedGlare } from "@/components/RedGlare"
import { But<PERSON> } from "@/components/ui/button"
import { CircleStop, LoaderCircle, PanelRight } from "lucide-react"
import { useEffect, useState } from "react"

type Props = {
	isReasoning: boolean
	onAbort: () => void
	onOpenResultDrawer: () => void
}

export const VerifyingBar = ({
	isReasoning,
	onAbort,
	onOpenResultDrawer,
}: Props) => {
	const [timeThinking, setTimeThinking] = useState(0)

	useEffect(() => {
		if (isReasoning) {
			const interval = setInterval(() => {
				setTimeThinking((prev) => prev + 1)
			}, 1000)
			return () => clearInterval(interval)
		}
	}, [isReasoning])

	return (
		<div className="w-full flex flex-col">
			<div className="w-full flex flex-row items-center justify-between gap-8 px-4 py-3 border-t-1 border-border">
				<div className="flex flex-row items-center gap-3 text-sm">
					<RedGlare />
					<p>Axion is working on your proof</p>
				</div>
				<div className="flex flex-row gap-3 items-center">
					<div className="flex flex-row gap-2 text-primary items-center">
						<LoaderCircle className="animate-spin w-5 h-5" />
						Thinking {timeThinking}s...
					</div>
					<Button variant="outline" onClick={onOpenResultDrawer}>
						<PanelRight />
						Check details
					</Button>
					<Button variant="outline" onClick={onAbort}>
						<CircleStop />
						Stop
					</Button>
				</div>
			</div>
		</div>
	)
}
