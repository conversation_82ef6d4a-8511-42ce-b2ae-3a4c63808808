import { InvalidAttemptDialog } from "@/components/ProblemActions/InvalidAttemptDialog"
import { ProblemToolbar } from "@/components/ProblemActions/ProblemToolbar"
import {
	type AnalysisResult,
	type MathematicalError,
	ResultDrawer,
	type VerificationStatus,
} from "@/components/ProblemActions/ResultDrawer"
import { ResultsBar } from "@/components/ProblemActions/ResultsBar"
import { VerifyingBar } from "@/components/ProblemActions/VerifyingBar"
import { useProblemEditor } from "@/components/ProblemEditor/index"
import { useTimer } from "@/contexts/TimerContext"
import { apiClient } from "@/lib/apiClient"
import { useSession } from "@/utils/session"
import camelcaseKeys from "camelcase-keys"
import { useRef, useState } from "react"
import { useLocation } from "wouter"

type Props = {
	problemUuid: string
	problemStatement: string
	isProofShown: boolean
	proofText?: string
}

const formatResult = (result: string): AnalysisResult => {
	// Match everything before the ```json block and capture the JSON inside
	const match = result.match(/([\s\S]*?)```json\s*([\s\S]*?)\s*```/)

	if (match) {
		const explanation = match[1].trim()
		let mathematicalErrors: MathematicalError[]

		try {
			const parsedMathematicalErrors = camelcaseKeys(
				JSON.parse(match[2].replace(/\n/g, "").replace(/\\/g, "$")),
				{ deep: true },
			)
			console.log("pp", parsedMathematicalErrors)
			mathematicalErrors = parsedMathematicalErrors.mathematicalErrors
		} catch (err) {
			mathematicalErrors = []
		}

		return {
			explanation,
			mathematicalErrors,
			isSuccess: mathematicalErrors.length === 0,
		}
	}

	return {
		explanation: result.trim(),
		mathematicalErrors: [],
		isSuccess: false,
	}
}

type StatusBarProps = {
	status: VerificationStatus
	isProofShown: boolean
	onOpenResultDrawer: () => void
	onVerify: () => void
	onSkip: () => void
	onAbort: () => void
}

const StatusBar = ({
	status,
	onVerify,
	onSkip,
	onAbort,
	isProofShown,
	onOpenResultDrawer,
}: StatusBarProps) => {
	switch (status) {
		case "idle":
			return (
				<ProblemToolbar
					onCheckProof={onVerify}
					onAbortCheckProof={onSkip}
					isProofShown={isProofShown}
				/>
			)
		case "verifying":
			return (
				<VerifyingBar
					isReasoning
					onAbort={onAbort}
					onOpenResultDrawer={onOpenResultDrawer}
				/>
			)
		case "success":
			return (
				<ResultsBar variant="success" onOpenResultDrawer={onOpenResultDrawer} />
			)
		case "error":
			return (
				<ResultsBar variant="error" onOpenResultDrawer={onOpenResultDrawer} />
			)
	}
}

export const ProblemActions = ({
	problemUuid,
	problemStatement,
	isProofShown,
}: Props) => {
	const [, navigate] = useLocation()
	const { updateSessionOnProblemVerified } = useSession()
	const { editor } = useProblemEditor()

	const { time, pauseTimer } = useTimer()

	const [verificationStatus, setVerificationStatus] =
		useState<VerificationStatus>("idle")

	const [reasoning, setReasoning] = useState<string>("")
	const [analysisResult, setAnalysisResult] = useState<AnalysisResult>({
		explanation: "",
		mathematicalErrors: [],
		isSuccess: false,
	})

	const [isInvalidAttemptOpen, setIsInvalidAttemptOpen] = useState(false)

	const [isResultDrawerOpen, setIsResultDrawerOpen] = useState(false)

	const abortController = useRef(new AbortController())
	const handleAbort = () => {
		abortController.current.abort()
		abortController.current = new AbortController()
	}

	const handleSkip = () => {
		navigate("/session/next")
	}

	const handleVerify = async () => {
		pauseTimer()

		const proofText = editor.getText()
		const attemptPayload = {
			attempt: {
				problem_statement: problemStatement,
				proof_text: proofText,
			},
		}

		setVerificationStatus("verifying")

		const checkResponse = await apiClient.post<{ isValid: boolean }>(
			"/check-proof-attempt",
			attemptPayload,
		)

		if (!checkResponse.data.isValid) {
			setIsInvalidAttemptOpen(true)
			setVerificationStatus("idle")
			return
		}

		try {
			let thinkingContent = ""
			let resultContent = ""

			await apiClient.post("/analyze-proof", attemptPayload, {
				responseType: "text",
				signal: abortController.current.signal,
				headers: { Accept: "text/event-stream" },
				onDownloadProgress: (progressEvent) => {
					const chunk = progressEvent.event.target.responseText
					// Process the chunk here
					const lines = chunk.split("\n")

					for (const line of lines) {
						if (line.startsWith("data: ")) {
							try {
								const data = JSON.parse(line.slice(6))

								switch (data.type) {
									case "thinking":
										if (data.content) {
											thinkingContent += data.content
												.replaceAll("<think>", "")
												.replaceAll("</think>", "")
											setReasoning(thinkingContent)
										}
										break
									case "thinking_complete":
										console.log("Thinking complete:", thinkingContent)
										break
									case "result":
										resultContent += data.content
										break
									case "error":
										throw new Error(data.message || "Analysis failed")
								}
							} catch (parseError) {
								console.warn("Failed to parse stream data:", parseError)
							}
						}
					}
				},
			})

			if (resultContent.trim()) {
				const result = formatResult(resultContent)

				for (const error of result.mathematicalErrors) {
					const charIndexInProof = proofText.indexOf(error.locationInProof)
					if (charIndexInProof !== -1) {
						editor.commands.insertContentAt(
							{
								from: charIndexInProof,
								to: charIndexInProof + error.locationInProof.length + 1,
							},
							`<mark style="background-color: #FEE2E2;"><span style="color: #EF4444;">${error.locationInProof}</span></mark>`,
						)
					}
				}

				setAnalysisResult(result)
				setVerificationStatus(result.isSuccess ? "success" : "error")
				updateSessionOnProblemVerified(
					result.isSuccess ? "solved" : "failed",
					time,
				)
			} else {
				throw new Error("No result received from analysis")
			}
		} catch (error) {
			console.error("Verification error:", error)

			if (error instanceof Error && error.name === "AbortError") {
				setVerificationStatus("idle")
				return
			}

			setVerificationStatus("error")
			setAnalysisResult({
				explanation:
					error instanceof Error
						? `Analysis failed: ${error.message}`
						: "An unexpected error occurred during verification.",
				mathematicalErrors: [],
				isSuccess: false,
			})
		}
	}

	return (
		<>
			{!isResultDrawerOpen && (
				<StatusBar
					status={verificationStatus}
					isProofShown={isProofShown}
					onVerify={handleVerify}
					onSkip={handleSkip}
					onAbort={handleAbort}
					onOpenResultDrawer={() => setIsResultDrawerOpen(true)}
				/>
			)}
			<ResultDrawer
				reasoning={reasoning}
				status={verificationStatus}
				isOpen={isResultDrawerOpen}
				onOpenChange={setIsResultDrawerOpen}
				analysisResult={analysisResult}
				problemUuid={problemUuid}
			/>
			<InvalidAttemptDialog
				isOpen={isInvalidAttemptOpen}
				onClose={() => setIsInvalidAttemptOpen(false)}
			/>
		</>
	)
}
