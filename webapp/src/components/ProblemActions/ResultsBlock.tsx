import { BugReportDialog } from "@/components/BugReportDialog"
import { ActionButtonWithKeymap } from "@/components/ProblemActions/ActionButtonWithKeymap"
import { useProblemEditor } from "@/components/ProblemEditor/index"
import { ChevronsRight, FlagTriangleLeftIcon } from "lucide-react"
import { type ReactNode, useState } from "react"
import { useLocation } from "wouter"

type Props = {
	children: ReactNode
	variant: "success" | "error"
	problemUuid: string
}

export const ResultsBlock = ({ variant, problemUuid, children }: Props) => {
	const [, navigate] = useLocation()

	const [isReportDialogOpen, setIsReportDialogOpen] = useState(false)
	const [currentStatement, setCurrentStatement] = useState<string>("")

	const { editor } = useProblemEditor()

	const handleOpenReportDialog = () => {
		// TODO: editor.getText() doesn't return text formatting. Use editor.getJSON() if you want a rich text format
		setCurrentStatement(editor.getText())
		setIsReportDialogOpen(true)
	}

	return (
		<div className="w-full h-full flex flex-col gap-4 mt-3 justify-between text-sm">
			<div>{children}</div>
			<div className="flex flex-col gap-2">
				<ActionButtonWithKeymap
					variant={variant}
					icon={ChevronsRight}
					label="Next challenge"
					description="Pick your next challenge"
					hotkey="meta+U"
					onClick={() => navigate("/session/next")}
				/>
				<ActionButtonWithKeymap
					variant={variant}
					icon={FlagTriangleLeftIcon}
					label="Something seems off?"
					description="Quick report, quick fix."
					hotkey="meta+J"
					onClick={handleOpenReportDialog}
				/>
				<BugReportDialog
					title="Request Review"
					description="If you think this evaluation is incorrect or incomplete, request a humain review."
					statementLabel="Your submitted proof (Summary)"
					isOpen={isReportDialogOpen}
					statement={currentStatement}
					problemUuid={problemUuid}
					onClose={() => setIsReportDialogOpen(false)}
				/>
			</div>
		</div>
	)
}
