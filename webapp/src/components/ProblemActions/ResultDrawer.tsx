import { ErrorCard } from "@/components/ProblemActions/ErrorCard"
import { ProblemStat } from "@/components/ProblemActions/ProblemStat"
import { ReasoningBlock } from "@/components/ProblemActions/ReasoningBlock"
import { ResultsBlock } from "@/components/ProblemActions/ResultsBlock"
import {
	She<PERSON>,
	<PERSON><PERSON><PERSON>lose,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet"
import { useTimer } from "@/contexts/TimerContext"
import { useSession } from "@/utils/session"
import { Loader2, X } from "lucide-react"
import { useEffect, useMemo, useRef } from "react"

export type VerificationStatus = "idle" | "verifying" | "success" | "error"

export type AnalysisResult = {
	explanation: string
	mathematicalErrors: MathematicalError[]
	isSuccess: boolean
}

type ContentProps = {
	status: VerificationStatus
	reasoning: string
	analysisResult: AnalysisResult
	problemUuid: string
}

type Props = {
	isOpen: boolean
	onOpenChange: (open: boolean) => void
} & ContentProps

const ResultDrawerContent = ({
	status,
	reasoning,
	analysisResult,
	problemUuid,
}: ContentProps) => {
	const { time, formatTime } = useTimer()
	const { session } = useSession()
	const scrollContainerRef = useRef<HTMLDivElement>(null)

	const hintsUsed = useMemo(() => {
		return (
			session.problems.find((p) => p.problem.uuid === problemUuid)?.hintsUsed ??
			0
		)
	}, [session.problems, problemUuid])

	// Auto-scroll to bottom when reasoning text updates during verification
	useEffect(() => {
		if (status === "verifying" && scrollContainerRef.current && reasoning) {
			const container = scrollContainerRef.current
			container.scrollTop = container.scrollHeight
		}
	}, [reasoning, status])

	switch (status) {
		case "verifying":
			return (
				<SheetContent className="bg-muted sm:max-w-xl w-xl">
					<SheetHeader>
						<SheetClose className="absolute right-4 text-muted-foreground">
							<X />
						</SheetClose>
						<SheetTitle className="flex flex-row gap-2 items-center">
							Checking Proof
							<Loader2 className="text-primary h-4 w-4 animate-spin" />
						</SheetTitle>
					</SheetHeader>
					<div
						ref={scrollContainerRef}
						className="w-full h-full pt-0 p-5 overflow-y-scroll"
					>
						<ReasoningBlock reasoning={reasoning} defaultOpen />
					</div>
				</SheetContent>
			)
		case "success":
			return (
				<SheetContent className="bg-green-200 sm:max-w-xl w-xl">
					<SheetHeader>
						<SheetClose className="absolute right-4 text-muted-foreground">
							<X />
						</SheetClose>
						<SheetTitle>The proof is formally verified and correct!</SheetTitle>
					</SheetHeader>
					<div
						ref={scrollContainerRef}
						className="w-full h-full pt-0 p-5 flex flex-col gap-3 overflow-y-scroll"
					>
						<ReasoningBlock reasoning={reasoning} />
						<ResultsBlock variant="success" problemUuid={problemUuid}>
							<div className="flex flex-row gap-12">
								<ProblemStat label="your time:" value={formatTime(time)} />
								<ProblemStat label="hints used:" value={hintsUsed} />
							</div>
						</ResultsBlock>
					</div>
				</SheetContent>
			)
		case "error":
			return (
				<SheetContent className="bg-red-200 sm:max-w-xl w-xl">
					<SheetHeader>
						<SheetClose className="absolute right-4 text-muted-foreground">
							<X />
						</SheetClose>
						<SheetTitle>Your proof needs stronger justification</SheetTitle>
					</SheetHeader>
					<div
						ref={scrollContainerRef}
						className="w-full h-full pt-0 p-5 flex flex-col gap-3 overflow-y-scroll"
					>
						<ReasoningBlock reasoning={reasoning} />
						<ResultsBlock variant="error" problemUuid={problemUuid}>
							<div className="flex flex-col gap-3">
								{analysisResult.mathematicalErrors.map(
									({ errorType, explanation, suggestedHint }, index) => (
										<ErrorCard
											// biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
											key={`${errorType}-${index}`}
											errorType={errorType}
											explanation={explanation}
											suggestedHint={suggestedHint}
										/>
									),
								)}
							</div>
						</ResultsBlock>
					</div>
				</SheetContent>
			)
	}
}

export const ResultDrawer = ({
	isOpen,
	onOpenChange,
	status,
	reasoning,
	analysisResult,
	problemUuid,
}: Props) => {
	return (
		<Sheet open={isOpen} onOpenChange={onOpenChange}>
			<ResultDrawerContent
				status={status}
				reasoning={reasoning}
				analysisResult={analysisResult}
				problemUuid={problemUuid}
			/>
		</Sheet>
	)
}
