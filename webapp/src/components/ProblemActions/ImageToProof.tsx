import { UploadZone } from "@/components/UploadZone"

export const ImageToProof = () => {
	return (
		<div className="not-prose flex flex-row bg-muted border-1 border-dashed border-border">
			{/*<div className="w-3/5 border-r-1 border-dashed border-border">*/}
			<UploadZone />
			{/*</div>*/}
			{/*<div className="w-2/5 flex flex-col items-center justify-center gap-2 p-5">*/}
			{/*	<img*/}
			{/*		alt="image-to-proof-qr-code"*/}
			{/*		src="/fake-qr.png"*/}
			{/*		className="w-25 h-25"*/}
			{/*	/>*/}
			{/*	<div className="text-center">*/}
			{/*		<p className="text-sm font-medium text-gray-600">*/}
			{/*			Or scan this QR-Code*/}
			{/*		</p>*/}
			{/*		<p className="text-xs font-medium text-gray-400">*/}
			{/*			To upload pictures and draw*/}
			{/*		</p>*/}
			{/*	</div>*/}
			{/*</div>*/}
		</div>
	)
}
