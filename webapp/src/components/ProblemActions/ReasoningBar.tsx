import { MathStatement } from "@/components/MathStatement"
import { But<PERSON> } from "@/components/ui/button"
import { Sheet, SheetContent } from "@/components/ui/sheet"
import { cn } from "@/lib/tailwind"
import { Check, ChevronUp, PanelBottomClose, PanelRight } from "lucide-react"
import { useState } from "react"

type Props = {
	isReasoning: boolean
	reasoning: string
}

export const ReasoningBar = ({ isReasoning, reasoning }: Props) => {
	const [showReasoning, setShowReasoning] = useState(false)
	const [showReasoningDrawer, setShowReasoningDrawer] = useState(false)

	if (showReasoningDrawer) {
		return (
			<Sheet open onOpenChange={() => setShowReasoningDrawer(false)}>
				<SheetContent className="sm:max-w-xl w-xl">
					<div className="w-full h-full flex flex-col gap-5 bg-muted px-4 py-2">
						<div className="flex flex-row items-center justify-between">
							<div className="w-full flex flex-row gap-3">
								<span className="font-medium text-sm">Reasoning</span>
								<span className="text-muted-foreground text-sm">
									Thinking.... 110s
								</span>
							</div>
							<div className="flex flex-row gap-3 items-center">
								<Button
									size="icon"
									variant="outline"
									onClick={() => setShowReasoningDrawer((prev) => !prev)}
								>
									<PanelBottomClose />
								</Button>
							</div>
						</div>
						<div className="mb-2 w-full h-full rounded-md p-4 bg-background text-sm text-muted-foreground overflow-y-scroll whitespace-break-spaces">
							<MathStatement statement={reasoning} />
						</div>
					</div>
				</SheetContent>
			</Sheet>
		)
	}

	if (!isReasoning)
		return (
			<div className="w-full flex flex-col bg-muted px-3 rounded-md border-1 border-border">
				<div className="flex flex-row items-center justify-between">
					<div className="flex flex-row items-center gap-3 text-sm">
						<Check className="stroke-chart-3" />
						<span className="text-sm text-muted-foreground">
							Finalized and feedbacks shared
						</span>
					</div>
					<Button
						size="icon"
						variant="ghost"
						onClick={() => setShowReasoning((prev) => !prev)}
					>
						<ChevronUp
							className={cn("transition-transform duration-500", {
								"transform rotate-180": showReasoning,
							})}
						/>
					</Button>
				</div>
				{showReasoning && (
					<div className="mb-2 w-full rounded-md p-4 bg-background text-sm text-muted-foreground whitespace-break-spaces overflow-y-scroll max-h-50">
						<MathStatement statement={reasoning} />
					</div>
				)}
			</div>
		)

	return (
		<div className="w-full flex flex-col gap-5  bg-muted px-4 py-2 rounded-md border-1 border-border">
			<div className="flex flex-row items-center justify-between">
				<div className="w-full flex flex-row gap-3">
					<span className="font-medium text-sm">Reasoning</span>
					<span className="text-muted-foreground text-sm">
						Thinking.... 110s
					</span>
				</div>
				<div className="flex flex-row gap-3 items-center">
					<Button
						size="icon"
						variant="outline"
						onClick={() => setShowReasoningDrawer((prev) => !prev)}
					>
						<PanelRight />
					</Button>
					<Button
						size="icon"
						variant="outline"
						onClick={() => setShowReasoning((prev) => !prev)}
					>
						<ChevronUp
							className={cn("transition-transform duration-500", {
								"transform rotate-180": showReasoning,
							})}
						/>
					</Button>
				</div>
			</div>
			{showReasoning && (
				<div className="w-full rounded-md p-4 bg-background text-sm text-muted-foreground whitespace-break-spaces overflow-y-scroll max-h-50">
					<MathStatement statement={reasoning} />
				</div>
			)}
		</div>
	)
}
