import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog"

type Props = {
    isOpen: boolean
    onClose: () => void
}

export const InvalidAttemptDialog = ({ isOpen, onClose }: Props) => (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
        <AlertDialogContent
            onCloseAutoFocus={(event) => {
                event.preventDefault()
                document.body.style.pointerEvents = ""
            }}
        >
            <AlertDialogHeader>
                <AlertDialogTitle>Provide a proof attempt</AlertDialogTitle>
                <AlertDialogDescription>
                    Axion could not detect a real proof attempt. Please write an actual proof before verifying.
                </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
                <AlertDialogAction onClick={onClose}>Ok</AlertDialogAction>
            </AlertDialogFooter>
        </AlertDialogContent>
    </AlertDialog>
)

