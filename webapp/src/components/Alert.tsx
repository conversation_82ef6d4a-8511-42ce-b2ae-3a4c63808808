import type { LucideIcon } from "lucide-react"
import { AlertDescription, AlertTitle, <PERSON><PERSON> as BaseAlert } from "./ui/alert"

type Props = {
	variant?: "default" | "destructive"
	title?: string
	description: string
	icon: LucideIcon
}

export const Alert = ({
	title,
	description,
	icon: Icon,
	variant = "default",
}: Props) => {
	return (
		<BaseAlert variant={variant}>
			<Icon className="h-4 w-4" />
			{title && <AlertTitle>{title}</AlertTitle>}
			<AlertDescription>{description}</AlertDescription>
		</BaseAlert>
	)
}
