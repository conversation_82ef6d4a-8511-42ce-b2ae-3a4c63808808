import { DifficultyBadge } from "@/components/DifficultyBadge"
import { MathStatement } from "@/components/MathStatement"
import type { Problem } from "@/types/problems"
import { Clock } from "lucide-react"

type Props = {
	problem: Problem
	isRevealed?: boolean
}

export const ProblemCard = ({ problem, isRevealed = true }: Props) => {
	return (
		<div className="relative w-full min-h-80 h-80 bg-white rounded-sm p-4 flex flex-col gap-3">
			<div className="flex flex-row items-center justify-between">
				<div className="text-xs text-yellow-500">{problem.domain}</div>
				{problem.difficultyLevel && (
					<div className="ml-auto">
						<DifficultyBadge level={problem.difficultyLevel} />
					</div>
				)}
			</div>
			<div className="text-xl">{problem.title}</div>
			{isRevealed ? (
				<>
					{/*<div className="flex flex-row items-center justify-between">*/}
					{/*	{problem.duration && (*/}
					{/*		<div className="flex flex-row gap-1 text-muted-foreground">*/}
					{/*			<Clock className="w-4 h-4" />*/}
					{/*			<div className="text-xs">{problem.duration} min</div>*/}
					{/*		</div>*/}
					{/*	)}*/}
					{/*	{problem.difficultyLevel && (*/}
					{/*		<div className="ml-auto">*/}
					{/*			<DifficultyBadge level={problem.difficultyLevel} />*/}
					{/*		</div>*/}
					{/*	)}*/}
					{/*</div>*/}
					<div className="overflow-hidden">
						<MathStatement statement={problem.informalStatement} />
					</div>
				</>
			) : (
				<div className="flex flex-row gap-1 items-center  text-muted-foreground">
					<Clock className="w-4 h-4" />
					<div>Coming soon</div>
				</div>
			)}
			<div className="absolute rounded-md right-0 bottom-0 bg-gradient-to-t from-white via-white to-transparent w-full h-8" />
		</div>
	)
}
