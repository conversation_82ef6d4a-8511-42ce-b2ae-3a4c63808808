import { ActionButton } from "@/components/ActionButton"
import { Hints } from "@/components/Assistant/Hints"
import { SettingCard } from "@/components/Assistant/SettingCard"
import { Timer } from "@/components/Timer"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Separator } from "@/components/ui/separator"
import {
	She<PERSON>,
	Sheet<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>onte<PERSON>,
	<PERSON><PERSON><PERSON>ooter,
	Sheet<PERSON>rigger,
} from "@/components/ui/sheet"
import { useSession } from "@/utils/session"
import { motion } from "framer-motion"
import { ChevronsRight, Lightbulb, Settings, Sparkles } from "lucide-react"
import { useEffect, useState } from "react"
import { AssistantTrigger } from "./AssistantTrigger"

type Props = {
	problemUuid: string
	hints: string[]
	isOpen: boolean
	onOpenChange: (open: boolean) => void
	isProofShown: boolean
}

export const AssistantDrawer = ({
	problemUuid,
	hints,
	isOpen,
	onOpenChange,
	isProofShown,
}: Props) => {
	const [isSettingsOpen, setIsSettingsOpen] = useState(false)
	const [isHintsOpen, setIsHintsOpen] = useState(false)

	const { session, isReady, updateSessionOnHintRevealed } = useSession()

	const [revealedHints, setRevealedHints] = useState<string[]>([])

	// biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
	useEffect(() => {
		if (!isReady) return
		const hintsUsed =
			session.problems.find((p) => p.problem.uuid === problemUuid)?.hintsUsed ??
			0
		const hintsToReveal = hints.slice(0, hintsUsed)
		setRevealedHints(hintsToReveal)
	}, [session.problems, isReady])

	const handleRevealHint = () => {
		if (revealedHints.length < hints.length) {
			// Hack to force a re-render of the Hints component
			setIsHintsOpen(false)
			setTimeout(() => setIsHintsOpen(true), 100)

			setRevealedHints((prev) => [hints[prev.length], ...prev])
			updateSessionOnHintRevealed()
		}
	}

	return (
		<Sheet open={isOpen} onOpenChange={onOpenChange}>
			<SheetTrigger asChild>
				<AssistantTrigger onClick={() => onOpenChange(true)} />
			</SheetTrigger>
			<SheetContent
				className="p-4 bg-input"
				style={{
					width: isSettingsOpen ? 300 : 150,
					transition: "width 0.4s ease-in-out",
				}}
			>
				{isSettingsOpen ? (
					<motion.div
						initial={{ opacity: 0 }}
						animate={{ opacity: 1 }}
						transition={{ delay: 0.1 }}
						className="flex flex-col items-center gap-4"
					>
						<button
							type="button"
							className="flex flex-row self-end items-center gap-1 text-muted-foreground text-sm whitespace-nowrap"
							onClick={() => setIsSettingsOpen(false)}
						>
							Hide Settings
							<ChevronsRight className="h-5 w-5" />
						</button>
						<Separator />
						<div className="w-full flex flex-col gap-3">
							<h5 className="font-bold">Assistant</h5>
							<SettingCard
								title="Select your AI model"
								color="yellow"
								icon={Sparkles}
							>
								<RadioGroup defaultValue="comfortable">
									<div className="flex items-center space-x-2">
										<RadioGroupItem
											value="openai_gpt-4.5"
											id="openai_gpt-4.5"
										/>
										<Label className="cursor-pointer" htmlFor="openai_gpt-4.5">
											GPT-4.5
											<span className="text-muted-foreground">- OpenAI</span>
										</Label>
									</div>
									<div className="flex items-center space-x-2">
										<RadioGroupItem value="deepseek_r1" id="deepseek_r1" />
										<Label className="cursor-pointer" htmlFor="deepseek_r1">
											R1
											<span className="text-muted-foreground">- DeepSeek</span>
										</Label>
									</div>
								</RadioGroup>
							</SettingCard>
						</div>
					</motion.div>
				) : (
					<div className="flex flex-col items-center gap-4">
						<SheetClose className="flex flex-row items-center gap-1 text-muted-foreground text-sm whitespace-nowrap">
							Hide Assistant <ChevronsRight className="h-5 w-5" />
						</SheetClose>
						<Timer direction="vertical" />
						<Separator />
						<div className="flex flex-col gap-3 w-full">
							<p className="text-sm text-muted-foreground font-medium">
								Shortcuts
							</p>
							<ActionButton
								label="Get hint"
								icon={Lightbulb}
								color="primary"
								onClick={handleRevealHint}
								disabled={isProofShown || revealedHints.length === hints.length}
							/>
							{/*<ActionButton*/}
							{/*	label="Show proof"*/}
							{/*	icon={Eye}*/}
							{/*	color="secondary"*/}
							{/*	onClick={onShowProof}*/}
							{/*	disabled={isProofShown}*/}
							{/*/>*/}
							<Hints
								isOpen={isHintsOpen}
								onOpenChange={setIsHintsOpen}
								revealedHints={revealedHints}
								maxHints={hints.length}
							/>
						</div>
					</div>
				)}
				{!isSettingsOpen && (
					<SheetFooter className="px-0 py-2 border-t border-border">
						<Button
							variant="ghost"
							className="w-full"
							onClick={() => setIsSettingsOpen(true)}
						>
							<Settings />
							Settings
						</Button>
					</SheetFooter>
				)}
			</SheetContent>
		</Sheet>
	)
}
