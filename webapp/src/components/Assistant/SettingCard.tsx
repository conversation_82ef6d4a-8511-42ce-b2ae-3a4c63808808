import type { LucideIcon } from "lucide-react"
import type { ReactNode } from "react"

type CardColor = "yellow"

type Props = {
	title: string
	color: CardColor
	icon: LucideIcon
	children: ReactNode
}

const borderVariants = {
	yellow: "border-yellow-500",
}

const colorVariants = {
	yellow: "bg-yellow-500",
}

export const SettingCard = ({ title, color, icon: Icon, children }: Props) => {
	return (
		<div
			className={`flex flex-row gap-4 items-start p-4 rounded-sm border-1 ${borderVariants[color]}`}
		>
			<div className={`rounded-sm p-1.5 text-white  ${colorVariants[color]}`}>
				<Icon className="h-5 w-5 fill-white" />
			</div>
			<div className="flex flex-col gap-2 items-start">
				<span className="text-sm font-bold">{title}</span>
				<div className="flex flex-col gap-2">{children}</div>
			</div>
		</div>
	)
}
