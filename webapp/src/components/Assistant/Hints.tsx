import { MathStatement } from "@/components/MathStatement"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@/components/ui/popover"
import { ChevronLeft, ChevronRight, ChevronsUpDown } from "lucide-react"
import { useEffect, useState } from "react"
import { Card } from "../ui/card"

type Props = {
	isOpen: boolean
	onOpenChange: (open: boolean) => void
	revealedHints: string[]
	maxHints: number
}

export const Hints = ({
	isOpen,
	onOpenChange,
	revealedHints,
	maxHints,
}: Props) => {
	const [currentHintIndex, setCurrentHintIndex] = useState(
		Math.max(revealedHints.length - 1, 0),
	)

	const hint = revealedHints[currentHintIndex]
	const showCyclingButtons = revealedHints.length > 1

	const handleCycleHint = (direction: "left" | "right") => {
		const delta = direction === "left" ? -1 : 1

		setCurrentHintIndex(
			(prev) => (prev + delta + revealedHints.length) % revealedHints.length,
		)
	}

	useEffect(() => {
		setCurrentHintIndex(Math.max(revealedHints.length - 1, 0))
	}, [revealedHints.length])

	return (
		<Popover open={isOpen} onOpenChange={onOpenChange}>
			<PopoverTrigger asChild disabled={revealedHints.length === 0}>
				<Button variant="ghost" className="text-sm p-2 font-medium">
					Hints
					<Badge className="w-4 h-4 p-0 text-xs rounded-full bg-muted text-foreground">
						{revealedHints.length}
					</Badge>
					<ChevronsUpDown className="h-8 w-8" />
				</Button>
			</PopoverTrigger>
			<PopoverContent className="bg-transparent shadow-none border-none pr-10">
				<Card className="text-sm p-4 shadow-xl rounded-xl bg-white cursor-pointer">
					<MathStatement statement={hint} />
					<div className="flex flex-row gap-1 items-center justify-end text-primary text-xs">
						{showCyclingButtons && (
							<Button
								size="icon"
								variant="ghost"
								className="size-5"
								onClick={() => handleCycleHint("left")}
							>
								<ChevronLeft />
							</Button>
						)}
						<div>
							{currentHintIndex + 1}/{maxHints}
						</div>
						{showCyclingButtons && (
							<Button
								size="icon"
								variant="ghost"
								className="size-5"
								onClick={() => handleCycleHint("right")}
							>
								<ChevronRight />
							</Button>
						)}
					</div>
				</Card>
			</PopoverContent>
		</Popover>
	)
}
