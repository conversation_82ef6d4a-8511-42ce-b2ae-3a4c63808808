import { Button } from "@/components/ui/button"
import { AnimatePresence, motion } from "framer-motion"
import { CircleX } from "lucide-react"
import { useEffect, useState } from "react"

type Props = {
	onClick?: () => void
}

export const AssistantTrigger = ({ onClick }: Props) => {
	const [isExpanded, setIsExpanded] = useState(false)

	useEffect(() => {
		const expandTimeout = setTimeout(() => setIsExpanded(true), 3 * 60 * 1000)
		return () => clearTimeout(expandTimeout)
	}, [])

	return (
		<motion.div
			layout
			className="flex max-w-[176px] w-fit bg-primary text-white overflow-hidden rounded-xl"
			initial={{ transformOrigin: "top left" }}
			animate={{
				height: isExpanded ? 108 : 32,
				transformOrigin: "top left",
			}}
			exit={{ transformOrigin: "top left" }}
			transition={{
				duration: 0.5,
				ease: [0.04, 0.62, 0.23, 0.98],
			}}
		>
			<div>
				<AnimatePresence mode="wait">
					{!isExpanded ? (
						<motion.div
							key="small"
							className="flex items-center justify-center w-full h-full hover:bg-orange-800 px-4 cursor-pointer"
							initial={{ opacity: 0 }}
							animate={{ opacity: 1 }}
							exit={{ opacity: 0 }}
							transition={{ duration: 0.2 }}
							onClick={onClick}
						>
							<span className="text-sm whitespace-nowrap">
								Get help with Assistant
							</span>
						</motion.div>
					) : (
						<motion.div
							key="expanded"
							className="flex flex-col items-center justify-center p-4 pb-1 text-center"
							initial={{ opacity: 0 }}
							animate={{ opacity: 1 }}
							exit={{ opacity: 0 }}
							transition={{ duration: 0.2 }}
						>
							<p className="text-xs text-left font-mono">
								<b>Need help?</b> Get a hint to guide your next step.
							</p>
							<div className="w-full flex flex-row items-center justify-between">
								<button
									type="button"
									onClick={onClick}
									className="underline hover:text-orange-100 cursor-pointer"
								>
									Ask for a Hint
								</button>
								<Button
									className="hover:bg-transparent hover:text-orange-100 -mr-3"
									variant="ghost"
									size="icon"
									onClick={(e) => {
										e.preventDefault()
										setIsExpanded((prev) => !prev)
									}}
								>
									<CircleX />
								</Button>
							</div>
						</motion.div>
					)}
				</AnimatePresence>
			</div>
		</motion.div>
	)
}
