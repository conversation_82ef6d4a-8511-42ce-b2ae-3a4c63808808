import { cn } from "@/lib/tailwind"
import { CircleX } from "lucide-react"

type Props = {
	file: File
	className?: string
	onRemove?: () => void
}

export const PictureThumbnail = ({ file, className, onRemove }: Props) => {
	return (
		<div className="relative">
			{onRemove && (
				<div className="w-6 h-6 bg-gray-600/50 rounded-full text-white absolute top-1 right-1">
					<button type="button" onClick={onRemove}>
						<CircleX />
					</button>
				</div>
			)}
			<img
				alt="preview"
				src={URL.createObjectURL(file)}
				className={cn("w-full h-full object-cover rounded-lg", className)}
			/>
		</div>
	)
}
