import { PictureThumbnail } from "@/components/ProofScanner/PictureThumbnail"
import { UploadButton } from "@/components/UploadButton"
import { Button } from "@/components/ui/button"
import { SquarePlus } from "lucide-react"

type Props = {
	files: File[]
	onAddPictures: (files: File[]) => void
	onError: () => void
	onRemove: (index: number) => void
	onSubmit: () => void
	onCancel: () => void
}

export const GalleryScanner = ({
	files,
	onAddPictures,
	onError,
	onRemove,
	onSubmit,
	onCancel,
}: Props) => {
	return (
		<div className="flex flex-col gap-4 h-full justify-between">
			<div className="grid grid-cols-2 gap-3">
				{files.map((file, index) => (
					<PictureThumbnail
						file={file}
						key={file.name}
						className="h-[190px]"
						onRemove={files.length > 1 ? () => onRemove(index) : undefined}
					/>
				))}
				<UploadButton
					variant="ghost"
					onError={onError}
					onUpload={onAddPictures}
					className="flex flex-col items-center justify-center w-full border-2 border-gray-300 rounded-lg h-[190px]"
				>
					<SquarePlus className="text-gray-500 size-8" />
				</UploadButton>
			</div>
			<div className="flex flex-row w-full gap-4 justify-center">
				<Button variant="outline" onClick={onCancel}>
					Back
				</Button>
				<Button onClick={onSubmit}>Upload Selection</Button>
			</div>
		</div>
	)
}
