import { PictureThumbnail } from "@/components/ProofScanner/PictureThumbnail"
import { UploadButton } from "@/components/UploadButton"
import { Button } from "@/components/ui/button"

type Props = {
	file: File
	onRetake: (files: File[]) => void
	onError: () => void
	onSubmit: () => void
	onCancel: () => void
}

export const CameraScanner = ({
	file,
	onRetake,
	onError,
	onSubmit,
	onCancel,
}: Props) => {
	return (
		<div className="flex flex-col gap-4 h-full">
			<div className="flex flex-col justify-center rounded-sm bg-gradient-to-b h-full w-full ">
				<PictureThumbnail file={file} />
			</div>
			<div className="flex flex-row w-full gap-4 justify-center">
				<Button variant="outline" onClick={onCancel}>
					Back
				</Button>
				<UploadButton
					variant="outline"
					onUpload={onRetake}
					onError={onError}
					fromCamera
				>
					Retake
				</UploadButton>
				<Button onClick={onSubmit}>Submit</Button>
			</div>
		</div>
	)
}
