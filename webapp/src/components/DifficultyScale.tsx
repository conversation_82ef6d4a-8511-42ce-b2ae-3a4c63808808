type Props = {
	showLabels?: boolean
}

export const DifficultyScale = ({ showLabels = false }: Props) => (
	<div className="w-full flex flex-row gap-3 items-center justify-center h-full">
		{showLabels && <div className="font-bold text-green-300">Intermediate</div>}
		<div className="flex flex-1 flex-row items-center">
			<div className="w-3 h-3 rounded-full bg-green-300" />
			<div className="flex flex-1 w-full h-[2px] bg-gradient-to-r from-green-300 via-yellow-200 to-red-300" />
			<div className="w-3 h-3 rounded-full bg-red-300" />
		</div>
		{showLabels && <div className="font-bold text-red-300">Elite</div>}
	</div>
)
