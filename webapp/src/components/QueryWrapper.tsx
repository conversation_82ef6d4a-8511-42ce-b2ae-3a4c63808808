import type { UseQueryResult } from "@tanstack/react-query"
import _ from "lodash"

import { Alert } from "@/components/Alert"
import { isAxiosNotFoundError } from "@/lib/apiClient"
import { Loader2, X } from "lucide-react"
import type { JSX } from "react"

type Props<TData, TError> = {
	query: UseQueryResult<TData, TError>
	returnEmptyResults?: boolean
	disabledQueryMessage?: string
	children: (props: { data: TData }) => JSX.Element | null
}

export const QueryWrapper = <TData, TError>({
	query,
	returnEmptyResults,
	disabledQueryMessage,
	children,
}: Props<TData, TError>) => {
	const { data, isLoading, isPending, isError, error } = query

	if (isAxiosNotFoundError(error)) {
		return (
			<div className="p-20 max-w-3xl mx-auto flex flex-col mt-10">
				<Alert
					icon={X}
					title="Page non trouvée"
					description="La page demandée n'existe pas, un mauvais lien peut-être ?"
				/>
			</div>
		)
	}
	if (isError) {
		return (
			<div className="p-20 max-w-3xl mx-auto flex flex-col mt-10">
				<Alert
					icon={X}
					variant="destructive"
					title="Une erreur est survenue"
					description="Une erreur est survenue lors du chargement des données"
				/>
			</div>
		)
	}

	if (isLoading)
		return (
			<div className="w-full h-full flex flex-col items-center justify-center">
				<Loader2 size={48} className="mb-20 animate-spin stroke-muted" />
			</div>
		)

	if (isPending) {
		return (
			<div className="p-20 max-w-3xl mx-auto flex flex-col mt-10">
				<Alert
					icon={X}
					description={disabledQueryMessage ?? "Cette requête est désactivée"}
				/>
			</div>
		)
	}

	if ((!data || _.isEmpty(data)) && !returnEmptyResults) {
		return (
			<div className="p-20 max-w-3xl mx-auto flex flex-col mt-10">
				<Alert
					icon={X}
					title="Aucun résultat"
					description="Nous n'avons trouvé aucun résultat pour votre requête"
				/>
			</div>
		)
	}

	return children({ data })
}
