import { Button } from "@/components/ui/button"
import { useTimer } from "@/contexts/TimerContext"
import { cn } from "@/lib/tailwind"
import { CirclePause, CirclePlay, CircleStop } from "lucide-react"

const formatTime = (time: number) => {
	const minutes = Math.floor(time / 60)
	const seconds = time % 60
	return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`
}

type Props = {
	direction?: "vertical" | "horizontal"
}

export const Timer = ({ direction = "horizontal" }: Props) => {
	const { time, isRunning, startTimer, pauseTimer, resetTimer } = useTimer()

	return (
		<div
			className={cn(
				"w-full h-fit flex px-4 py-3 border-border border-1 rounded-sm bg-primary-foreground",
				{
					"flex-row gap-3 justify-between": direction === "horizontal",
					"flex-col gap-1 items-center": direction === "vertical",
				},
			)}
		>
			<h6 className="w-[70px] text-2xl font-bold">{formatTime(time)}</h6>
			<div className="w-fit flex flex-row gap-1 bg-muted px-1 rounded-full items-center">
				{isRunning ? (
					<Button
						variant="ghost"
						size="icon"
						className="w-5 h-5 p-1 rounded-full hover:bg-primary-foreground/30"
						onClick={pauseTimer}
					>
						<CirclePause />
					</Button>
				) : (
					<Button
						variant="ghost"
						size="icon"
						className="w-5 h-5 p-1 rounded-full hover:bg-primary-foreground/30"
						onClick={startTimer}
					>
						<CirclePlay />
					</Button>
				)}
				<Button
					variant="ghost"
					size="icon"
					className="w-5 h-5 p-1 rounded-full hover:bg-primary-foreground/30"
					onClick={resetTimer}
				>
					<CircleStop />
				</Button>
			</div>
		</div>
	)
}
