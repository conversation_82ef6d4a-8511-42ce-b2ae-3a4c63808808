import { Card } from "@/components/ui/card"
import { motion } from "framer-motion"
import { Children, type ReactNode, useState } from "react"

type Props = {
	children: ReactNode[]
}

export const CyclingCardStack = ({ children }: Props) => {
	const [orderedCards, setOrderedCards] = useState<ReactNode[]>(
		Children.toArray(children),
	)

	// When clicking the top card, move it to the bottom of the stack.
	const handleCardClick = () => {
		setOrderedCards((prevOrder) => {
			if (prevOrder.length <= 1) return prevOrder
			const [top, ...rest] = prevOrder
			return [...rest, top]
		})
	}

	return (
		<div className="relative">
			{orderedCards.map((card, index) => {
				// Calculate offsets: these values place a slight offset for each card in the stack.
				const offsetX = index * 8 // shifts to the right
				const offsetY = -index * 8 // shifts upward
				return (
					<motion.div
						// biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
						key={`card-${index}`}
						layout // enables smooth position reordering animation
						onClick={index === 0 ? handleCardClick : undefined} // only the top card is clickable.
						className="absolute w-full"
						style={{ zIndex: orderedCards.length - index }}
						animate={{ x: offsetX, y: offsetY }}
						transition={{ type: "spring", stiffness: 300, damping: 25 }}
					>
						<Card className="p-4 shadow-xl rounded-xl bg-white cursor-pointer">
							<div className="text-sm">{card}</div>
						</Card>
					</motion.div>
				)
			})}
		</div>
	)
}
