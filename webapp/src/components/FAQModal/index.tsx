import { FAQ } from "@/components/FAQModal/FAQ"
import { QueryWrapper } from "@/components/QueryWrapper"
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog"
import { apiClient } from "@/lib/apiClient"
import type { FAQItem } from "@/types/faq"
import { useQuery } from "@tanstack/react-query"
import { CircleHelp } from "lucide-react"

export const FAQModal = () => {
	const faqQuery = useQuery({
		queryKey: ["faq"],
		queryFn: async () => {
			const response = await apiClient.get<FAQItem[]>("/faq")
			return response.data
		},
	})

	return (
		<Dialog>
			<DialogTrigger className="absolute bottom-5 left-5 z-30">
				<CircleHelp className="w-6 h-6 text-white hover:text-muted" />
			</DialogTrigger>
			<DialogContent className="min-w-full h-dvh bg-white rounded-none overflow-y-scroll flex flex-col items-center justify-start">
				<div className="max-w-2xl flex flex-col gap-6 items-center">
					<div className="text-5xl font-bold">FAQ - Axion</div>
					<div className="text-2xl text-muted-foreground text-center">
						A free tool to practice and improve on math competition problems.
					</div>
					<QueryWrapper query={faqQuery}>
						{({ data: faq }) => <FAQ faq={faq} />}
					</QueryWrapper>
				</div>
			</DialogContent>
		</Dialog>
	)
}
