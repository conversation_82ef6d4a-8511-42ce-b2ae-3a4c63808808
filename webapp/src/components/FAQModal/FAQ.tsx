import { Button } from "@/components/ui/button"
import type { FAQItem } from "@/types/faq"
import _ from "lodash"
import { useState } from "react"
import Markdown from "react-markdown"

import "./FAQ.css"

type Props = {
	faq: FAQItem[]
}

export const FAQ = ({ faq }: Props) => {
	const [categoryIndex, setCategoryIndex] = useState(0)

	const faqByCategory = _.groupBy(faq, (faqItem) => faqItem.category)
	const categories = Object.keys(faqByCategory)
	const faqByCategoryIndex = Object.values(faqByCategory)

	return (
		<div className="w-full flex flex-col gap-6">
			<div className="flex flex-row gap-4 items-center justify-center p-6">
				{categories.map((category, index) => (
					<Button
						key={category}
						variant={index === categoryIndex ? "default" : "outline"}
						onClick={() => setCategoryIndex(index)}
						className="rounded-full"
					>
						{category}
					</Button>
				))}
			</div>
			{faqByCategoryIndex[categoryIndex].map((faqItem) => (
				<div key={faqItem.id} className="faq-item flex flex-col gap-2">
					<div className="text-2xl font-medium">{faqItem.question}</div>
					<div className="text-muted-foreground">
						<Markdown>{faqItem.answer}</Markdown>
					</div>
				</div>
			))}
			<div className="rounded-md bg-background flex flex-col gap-4 p-6 pt-10">
				<div className="text-2xl font-medium">What’s next for Axion</div>
				<div className="text-muted-foreground whitespace-pre-line flex flex-col gap-4">
					<p>We are actively working on new features</p>
					<ul className="list-disc ml-6">
						<li>More curated problems</li>
						<li>Smarter and clearer feedback</li>
						<li>Personalized learning paths</li>
					</ul>
					<p>
						Our goal is to make Axion the most helpful free platform for math
						competitors worldwide.
					</p>
				</div>
			</div>
		</div>
	)
}
