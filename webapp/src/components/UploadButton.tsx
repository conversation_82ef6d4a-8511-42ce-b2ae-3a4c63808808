import { type ChangeEvent, useRef } from "react"
import { Button, type ButtonProps } from "./ui/button"

type Props = ButtonProps & {
	fromCamera?: boolean
	onUpload: (files: File[]) => void
	onError: () => void
}

export const UploadButton = ({
	children,
	onUpload,
	onError,
	fromCamera = false,
	...buttonProps
}: Props) => {
	const inputRef = useRef<HTMLInputElement>(null)

	const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
		try {
			const files = event.target.files
			if (files) {
				onUpload(Array.from(files))
			} else {
				onError()
			}
		} catch (error) {
			onError()
		}
	}

	return (
		<>
			<input
				type="file"
				ref={inputRef}
				accept="image/*"
				capture={fromCamera ? "environment" : undefined}
				multiple={!fromCamera}
				className="hidden"
				onChange={handleChange}
				onError={onError}
			/>
			<Button onClick={() => inputRef.current?.click()} {...buttonProps}>
				{children}
			</Button>
		</>
	)
}
