import { createHighlighter } from "shiki"

import { QueryWrapper } from "@/components/QueryWrapper"
import { useQuery } from "@tanstack/react-query"

import "./Lean4CodeBlock.css"

type Props = {
	lean4Code: string
}

export const Lean4CodeBlock = ({ lean4Code }: Props) => {
	const lean4SyntaxQuery = useQuery({
		queryKey: ["lean4-syntax", lean4Code],
		queryFn: async () => {
			const highlighter = await createHighlighter({
				themes: ["github-light"], // or 'nord', etc.
				langs: ["lean4"],
			})

			return highlighter.codeToHtml(lean4Code, {
				lang: "lean4",
				theme: "github-light",
			})
		},
	})

	return (
		<QueryWrapper query={lean4SyntaxQuery}>
			{({ data }) => (
				<div
					className="-mt-8 w-full overflow-x-auto text-lg"
					dangerouslySetInnerHTML={{ __html: data }}
				/>
			)}
		</QueryWrapper>
	)
}
