import { ReasoningBlock } from "@/components/FeedbackPanel/ReasoningBlock"
import { SubmittedFlag } from "@/components/FeedbackPanel/SubmittedFlag"
import type { ProofSubmission } from "@/pages/Problem/useCheckProof"

type Props = {
	proofSubmission: ProofSubmission
}

export const ProofSubmissionTimeline = ({ proofSubmission }: Props) => {
	return (
		<div className="flex flex-col gap-3">
			<ReasoningBlock reasoning={proofSubmission.reasoning} />
			<SubmittedFlag />
		</div>
	)
}
