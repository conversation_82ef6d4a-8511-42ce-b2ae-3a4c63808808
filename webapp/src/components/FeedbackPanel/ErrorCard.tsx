import { MathStatement } from "@/components/MathStatement"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Lightbulb } from "lucide-react"
import { useState } from "react"

type Props = {
	errorType: string
	explanation: string
	suggestedHint: string
}

export const ErrorCard = ({ errorType, explanation, suggestedHint }: Props) => {
	const [isHintRevealed, setIsHintRevealed] = useState(false)

	return (
		<div className="bg-background w-full h-full p-4 flex flex-col gap-2 rounded-md border-1 border-border">
			<Badge className="bg-red-50 text-red-500 font-medium font-mono">
				{errorType}
			</Badge>
			<div className="text-muted-foreground font-mono flex flex-col gap-2 overflow-x-clip">
				<MathStatement statement={explanation} />
				{isHintRevealed && (
					<div className="flex flex-col gap-2">
						<div className="w-20 h-[1px] bg-black" />
						<MathStatement statement={`**Hint:** ${suggestedHint}`} />
					</div>
				)}
			</div>
			{!isHintRevealed && (
				<div className="flex flex-row justify-end">
					<Button
						variant="ghost"
						className="text-muted-foreground"
						onClick={() => setIsHintRevealed(true)}
					>
						Get Hint
						<Lightbulb />
					</Button>
				</div>
			)}
		</div>
	)
}
