import { cn } from "@/lib/tailwind"
// import { detectOS } from "@/utils/os"
import type { LucideIcon } from "lucide-react"
// import { useMemo } from "react"
// import { useHotkeys } from "react-hotkeys-hook"

type Props = {
	variant: "primary" | "success" | "error"
	label: string
	description?: string
	hotkey: string
	icon: LucideIcon
	onClick: () => void
}

const VARIANTS = {
	primary: {
		bg: "bg-primary",
		hotkey: "text-primary-foreground",
	},
	success: {
		bg: "bg-green-300",
		hotkey: "text-green-700",
	},
	error: {
		bg: "bg-red-300",
		hotkey: "text-red-700",
	},
}

export const ActionButtonWithKeymap = ({
	variant,
	label,
	description,
	// hotkey,
	icon: Icon,
	onClick,
}: Props) => {
	// useHotkeys(hotkey, onClick)
	//
	// const formattedHotkey = useMemo(() => {
	// 	const os = detectOS()
	// 	if (os === "mac") return hotkey.replace("meta+", "⌘")
	// 	return hotkey.replace("meta", "Ctrl")
	// }, [hotkey])

	const variants = VARIANTS[variant]

	return (
		<button
			type="button"
			className={cn(
				"group flex flex-row gap-6 items-center justify-between px-4 py-2 rounded-sm hover:bg-black",
				variants.bg,
			)}
			onClick={onClick}
		>
			<div className="flex flex-row items-center gap-2">
				<Icon className="h-4 w-4 text-sidebar-background group-hover:text-white" />
				<span className="text-sm text-sidebar-background group-hover:text-white font-medium group-hover:font-bold">
					{label}
				</span>
				{description && (
					<span className="text-sm text-muted-foreground group-hover:text-white">
						{description}
					</span>
				)}
			</div>
			{/*<span className={cn("text-sm", variants.hotkey)}>{formattedHotkey}</span>*/}
		</button>
	)
}
