import { ReasoningBlock } from "@/components/FeedbackPanel/ReasoningBlock"
import { ResultsBlock } from "@/components/FeedbackPanel/ResultsBlock"
import { Button } from "@/components/ui/button"
import type { ProofSubmission } from "@/pages/Problem/useCheckProof"
import { motion } from "framer-motion"
import { PanelRightClose } from "lucide-react"

type Props = {
	proofSubmissions: ProofSubmission[]
	isReasoning: boolean
	onClose: () => void
}

export const FeedbackPanel = ({
	proofSubmissions,
	isReasoning,
	onClose,
}: Props) => {
	return (
		<motion.div
			className="md:w-md lg:w-xl h-full p-3"
			initial={{ x: "100%" }}
			animate={{ x: 0 }}
			exit={{ x: "100%" }}
			transition={{
				type: "spring",
				stiffness: 300,
				damping: 30,
			}}
		>
			<div className="h-full rounded-md bg-background border-1 border-border overflow-hidden">
				<div className="border-b-1 border-border px-5 py-1 flex flex-row items-center justify-between">
					<div className="font-bold">Feedback</div>
					<Button size="icon" onClick={onClose} variant="ghost">
						<PanelRightClose className="w-5 h-5 text-muted-foreground" />
					</Button>
				</div>
				<div className="flex flex-col gap-4 px-5 py-3 h-full overflow-y-scroll overflow-x-hidden pb-16">
					<ReasoningBlock
						reasoning={proofSubmissions[0].reasoning}
						submittedAt={proofSubmissions[0].submittedAt}
						isReasoning={isReasoning}
					/>
					<ResultsBlock proofSubmission={proofSubmissions[0]} />
				</div>
			</div>
		</motion.div>
	)
}
