import { ProofSubmissionTimeline } from "@/components/FeedbackPanel/ProofSubmissionTimeline"
import { Button } from "@/components/ui/button"
import type { ProofSubmission } from "@/pages/Problem/useCheckProof"
import { motion } from "framer-motion"
import { PanelRightClose } from "lucide-react"

type Props = {
	proofSubmissions: ProofSubmission[]
	isReasoning: boolean
	onClose: () => void
}

export const FeedbackPanel = ({
	proofSubmissions,
	isReasoning,
	onClose,
}: Props) => {
	return (
		<motion.div
			className="md:max-w-md lg:max-w-xl w-full h-full z-100 m-3 rounded-md bg-background border-1 border-border"
			initial={{ x: "100%" }}
			animate={{ x: 0 }}
			exit={{ x: "100%" }}
			transition={{ type: "spring", stiffness: 300, damping: 30 }}
		>
			<div className="border-b-1 border-border px-5 py-1 flex flex-row items-center justify-between">
				<div className="font-bold">Feedback</div>
				<Button size="icon" onClick={onClose} variant="ghost">
					<PanelRightClose className="w-5 h-5 text-muted-foreground" />
				</Button>
			</div>
			<div className="flex flex-col gap-4 px-5 py-3 overflow-y-scroll">
				{proofSubmissions.map((proofSubmission) => (
					<ProofSubmissionTimeline
						key={proofSubmission.submittedAt.toISOString()}
						proofSubmission={proofSubmission}
					/>
				))}
			</div>
		</motion.div>
	)
}
