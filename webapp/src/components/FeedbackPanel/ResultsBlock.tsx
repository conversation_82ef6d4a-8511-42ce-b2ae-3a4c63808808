import { MathStatement } from "@/components/MathStatement/index"
import type { ProofSubmission } from "@/pages/Problem/useCheckProof"
import { Info } from "lucide-react"

import { ErrorCard } from "./ErrorCard"
import { ProblemStat } from "./ProblemStat"
import { ResultsWrapper } from "./ResultsWrapper"

type Props = {
	proofSubmission: ProofSubmission
}

export const ResultsBlock = ({ proofSubmission }: Props) => {
	switch (proofSubmission.status) {
		case "error":
			return (
				<div className="flex flew-row items-center gap-3 bg-red-600 p-4 rounded-md text-sm text-white">
					<Info className="w-5 h-5" />
					{proofSubmission.explanation}
				</div>
			)
		case "success":
			return (
				<ResultsWrapper variant="success" problemUuid="abc">
					<div className="flex flex-row gap-12">
						<ProblemStat label="your time:" value="12:00" />
						<ProblemStat label="hints used:" value="3" />
					</div>
				</ResultsWrapper>
			)
		case "failure":
			return (
				<div className="flex flex-col gap-3">
					<ResultsWrapper variant="error" problemUuid="abc">
						<div className="text-sm text-red-700">
							<MathStatement statement={proofSubmission.explanation} />
						</div>
					</ResultsWrapper>
					{proofSubmission.mathematicalErrors.map(
						({ errorType, explanation, suggestedHint }, index) => (
							<ErrorCard
								// biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
								key={`${errorType}-${index}`}
								errorType={errorType}
								explanation={explanation}
								suggestedHint={suggestedHint}
							/>
						),
					)}
				</div>
			)
		case "reasoning":
			return null
	}
}
