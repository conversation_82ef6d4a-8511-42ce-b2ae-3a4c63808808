import { MathStatement } from "@/components/MathStatement"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/tailwind"
import dayjs from "dayjs"
import { ChevronDown } from "lucide-react"
import { useEffect, useState } from "react"

type Props = {
	reasoning: string
	submittedAt: Date
	isReasoning?: boolean
}

const computeSubmittedSince = (submittedAt: Date) => {
	const now = dayjs()
	return now.diff(submittedAt, "second")
}

export const ReasoningBlock = ({
	reasoning,
	submittedAt,
	isReasoning = false,
}: Props) => {
	const [showReasoning, setShowReasoning] = useState(isReasoning)

	const [submittedSince, setSubmittedSince] = useState(
		computeSubmittedSince(submittedAt),
	)

	useEffect(() => {
		if (isReasoning) {
			const interval = setInterval(() => {
				setSubmittedSince((prev) => prev + 1)
			}, 1000)
			return () => clearInterval(interval)
		}
		setShowReasoning(false)
	}, [isReasoning])

	return (
		<div className="w-full rounded-md px-5 py-1 bg-muted text-sm text-muted-foreground">
			<div className="flex flex-row items-center justify-between">
				<div className="flex flex-row gap-3 text-sm">
					<span className="font-medium">Reasoning</span>
					{isReasoning && (
						<span className="text-muted-foreground">
							Thinking... {submittedSince}s
						</span>
					)}
				</div>
				<Button
					size="icon"
					variant="ghost"
					onClick={() => setShowReasoning((prev) => !prev)}
				>
					<ChevronDown
						className={cn("transition-transform duration-500", {
							"transform rotate-180": !showReasoning,
						})}
					/>
				</Button>
			</div>
			{showReasoning && (
				<div className="pb-2">
					<MathStatement statement={reasoning} />
				</div>
			)}
		</div>
	)
}
