import { MathStatement } from "@/components/MathStatement"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/tailwind"
import { ChevronDown } from "lucide-react"
import { useState } from "react"

type Props = {
	reasoning: string
	defaultOpen?: boolean
}

export const ReasoningBlock = ({ reasoning, defaultOpen = false }: Props) => {
	const [showReasoning, setShowReasoning] = useState(defaultOpen)

	return (
		<div className="w-full h-fit rounded-md p-4 bg-background text-sm text-muted-foreground">
			<div className="flex flex-row items-center justify-between">
				<span className="font-medium">Reasoning</span>
				<Button
					size="icon"
					variant="ghost"
					onClick={() => setShowReasoning((prev) => !prev)}
				>
					<ChevronDown
						className={cn("transition-transform duration-500", {
							"transform rotate-180": !showReasoning,
						})}
					/>
				</Button>
			</div>
			{showReasoning && <MathStatement statement={reasoning} />}
		</div>
	)
}
