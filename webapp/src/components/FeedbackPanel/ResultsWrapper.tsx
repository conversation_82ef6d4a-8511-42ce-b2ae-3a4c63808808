import { BugReportDialog } from "@/components/BugReportDialog"
import { useProblemEditor } from "@/components/ProblemEditor/index"
import { ChevronsRight, FlagTriangleLeftIcon } from "lucide-react"
import { type ReactNode, useMemo, useState } from "react"
import { useLocation } from "wouter"

import { ActionButtonWithKeymap } from "./ActionButtonWithKeymap"

type Props = {
	children: ReactNode
	variant: "success" | "error"
	problemUuid: string
}

const variants = {
	error: {
		title: "You're almost there!",
		description: "Proof needs clarity",
		style: {
			container: "bg-red-200 text-red-700",
			button: "hover:bg-red-300",
		},
	},
	success: {
		title: "Proof verified!",
		description: "Here's how you did.",
		style: {
			container: "bg-green-200 text-green-800",
			button: "hover:bg-green-300",
		},
	},
}

export const ResultsWrapper = ({ variant, problemUuid, children }: Props) => {
	const [, navigate] = useLocation()

	const variantProps = useMemo(() => variants[variant], [variant])

	const [isReportDialogOpen, setIsReportDialogOpen] = useState(false)
	const [currentStatement, setCurrentStatement] = useState<string>("")

	const { editor } = useProblemEditor()

	const handleOpenReportDialog = () => {
		// TODO: editor.getText() doesn't return text formatting. Use editor.getJSON() if you want a rich text format
		setCurrentStatement(editor.getText())
		setIsReportDialogOpen(true)
	}

	return (
		<div
			className={`w-full rounded-md flex flex-col gap-3 px-4 py-3 ${variantProps.style.container}`}
		>
			<div className="flex flex-row items-baseline gap-3">
				<p className="font-medium">{variantProps.title}</p>
				<p>{variantProps.description}</p>
			</div>
			<div className="w-full h-full flex flex-col gap-4 mt-3 justify-between text-sm">
				<div>{children}</div>
				<div className="flex flex-col gap-2">
					<ActionButtonWithKeymap
						variant={variant}
						icon={ChevronsRight}
						label="Next challenge"
						hotkey="meta+U"
						onClick={() => navigate("/session/next")}
					/>
					<ActionButtonWithKeymap
						variant={variant}
						icon={FlagTriangleLeftIcon}
						label="Something seems off?"
						hotkey="meta+J"
						onClick={handleOpenReportDialog}
					/>
					<BugReportDialog
						title="Request Review"
						description="If you think this evaluation is incorrect or incomplete, request a humain review."
						statementLabel="Your submitted proof (Summary)"
						isOpen={isReportDialogOpen}
						statement={currentStatement}
						problemUuid={problemUuid}
						onClose={() => setIsReportDialogOpen(false)}
					/>
				</div>
			</div>
		</div>
	)
}
