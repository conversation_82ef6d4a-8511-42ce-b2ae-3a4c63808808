import { Button } from "@/components/ui/button"
import { PanelLeftClose } from "lucide-react"

type Props = {
	onClick: () => void
}

export const FeedbackPanelButton = ({ onClick }: Props) => {
	return (
		<Button
			onClick={onClick}
			variant="outline"
			className="bg-white justify-between rounded-sm"
			size="lg"
		>
			<div className="text-accent-foreground">Feedback</div>
			<PanelLeftClose className="text-muted-foreground" />
		</Button>
	)
}
