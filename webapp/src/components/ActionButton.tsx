import { cn } from "@/lib/tailwind"
import type { LucideIcon } from "lucide-react"

type ButtonColor = "primary" | "secondary"

type Props = {
	color: ButtonColor // TODO: use variants?
	icon: LucideIcon
	label: string
	disabled?: boolean
	onClick: () => void
}

const colorVariants: Record<ButtonColor, string> = {
	primary: "bg-primary hover:bg-orange-800",
	secondary: "bg-secondary hover:bg-teal-800",
}

export const ActionButton = ({
	color,
	icon: Icon,
	label,
	disabled = false,
	onClick,
}: Props) => {
	return (
		<button
			type="button"
			disabled={disabled}
			className={cn(
				`flex flex-col gap-1 items-start px-2 py-1 rounded-sm text-white ${colorVariants[color]}`,
				{ "opacity-60 hover:bg-initial": disabled },
			)}
			onClick={onClick}
		>
			<Icon className="h-4 w-4" />
			<span className="text-sm font-medium">{label}</span>
		</button>
	)
}
