import { Badge } from "./ui/badge"

type Props = {
	level: number
}

const DIFFICULTY_PROPS = [
	{ label: "Intermediate", color: "bg-green-300" },
	{ label: "Advanced", color: "bg-yellow-200" },
	{ label: "Elite", color: "bg-red-300" },
]

const getDifficultyBin = (level: number) => {
	if (level < 4) return 0
	if (level < 7) return 1
	return 2
}

export const DifficultyBadge = ({ level = 1 }: Props) => {
	const difficultyProps = DIFFICULTY_PROPS[getDifficultyBin(level)]
	return (
		<Badge
			className={`rounded-xs text-muted-foreground text-xs ${difficultyProps.color}`}
		>
			{difficultyProps.label}
		</Badge>
	)
}
