import { DIFFICULTY_PROPS, getDifficultyBin } from "@/utils/difficulty"
import { Badge } from "./ui/badge"

type Props = {
	level: number
}

export const DifficultyBadge = ({ level = 1 }: Props) => {
	const difficultyProps = DIFFICULTY_PROPS[getDifficultyBin(level)]
	return (
		<Badge
			className={`rounded-xs text-muted-foreground text-xs ${difficultyProps.color}`}
		>
			{difficultyProps.label}
		</Badge>
	)
}
