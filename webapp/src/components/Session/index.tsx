import { But<PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import {
	Sheet,
	SheetClose,
	Sheet<PERSON>ontent,
	She<PERSON><PERSON><PERSON>er,
	She<PERSON><PERSON><PERSON>le,
	Sheet<PERSON>rigger,
} from "@/components/ui/sheet"
import { useSession } from "@/utils/session"
import { PanelLeft, PanelLeftClose } from "lucide-react"

import { Achievements } from "@/components/Session/Achievements"
import { useTimer } from "@/contexts/TimerContext"
import { Link } from "wouter"
import { SessionGoal } from "./SessionGoal"
import { SessionStat } from "./SessionStat"
import { StatusBar } from "./StatusBar"

export const SessionDrawer = () => {
	const { session, stats } = useSession()
	const { time, formatTime } = useTimer()

	return (
		<Sheet>
			<SheetTrigger asChild>
				<Button
					variant="outline"
					className="flex flex-row gap-4 h-[42px] bg-white"
				>
					<img src="/logo.png" alt="Axion logo" className="w-5 h-5" />
					<PanelLeft style={{ width: 20, height: 20 }} />
				</Button>
			</SheetTrigger>
			<SheetContent side="left" className="p-4 bg-sidebar">
				<SheetHeader>
					<SheetClose className="self-end text-white">
						<PanelLeftClose className="w-4 h-4" />
					</SheetClose>
					<SheetTitle className="text-white text-2xl">
						Session status
					</SheetTitle>
				</SheetHeader>
				<div className="flex flex-col gap-3 text-white">
					<SessionGoal goal={session.goal} />
					<Separator className="bg-muted-foreground" />
					<div className="flex flex-row justify-between gap-2">
						<SessionStat label="Problems solved" value={stats.problemsSolved} />
						<SessionStat label="Hints used" value={stats.totalHintsUsed} />
						<SessionStat
							label="Time spent"
							value={formatTime(time + stats.totalTimeSpent)}
						/>
					</div>
					<Separator className="bg-muted-foreground" />
					<StatusBar
						problems={session.problems}
						problemsSolved={stats.problemsSolved}
					/>
					<Separator className="bg-muted-foreground" />
					<Achievements stats={stats} />
					<Separator className="bg-muted-foreground" />
					<Link to="/session/summary" asChild>
						<Button>Wrap up session</Button>
					</Link>
				</div>
			</SheetContent>
		</Sheet>
	)
}
