import type { ProblemStatus, SessionProblem } from "@/utils/session"
import { Check, Ellipsis, X } from "lucide-react"

type Props = {
	problems: SessionProblem[]
	problemsSolved: number
}

const ProblemStatusIcon = (props: { status: ProblemStatus }) => {
	switch (props.status) {
		case "in-progress":
			return <Ellipsis className="w-4 h-4" />
		case "solved":
			return <Check className="w-4 h-4" />
		case "failed":
			return <X className="w-4 h-4" />
	}
}

export const StatusBar = ({ problems, problemsSolved }: Props) => {
	return (
		<div className="flex flex-col gap-3 py-2">
			<div className="w-full h-8 rounded-full bg-sidebar-foreground">
				<div className="w-fit h-full bg-primary rounded-full p-1 flex flex-row items-center gap-6">
					{problems.map(({ problem, status }) => (
						<div
							key={problem.uuid}
							className="w-6 h-6 flex items-center justify-center bg-white/30 rounded-full"
						>
							<ProblemStatusIcon status={status} />
						</div>
					))}
				</div>
			</div>
			<div className="text-white">
				You've tackled {problemsSolved} tailored problem
				{problemsSolved > 1 ? "s" : ""} so far
			</div>
		</div>
	)
}
