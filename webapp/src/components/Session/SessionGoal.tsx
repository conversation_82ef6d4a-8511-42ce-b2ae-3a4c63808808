type Props = {
	goal: string
}

export const SessionGoal = ({ goal }: Props) => (
	<div className="w-full flex flex-col gap-1 bg-sidebar-foreground p-3 rounded-md">
		<div className="text-sidebar-accent-foreground text-sm">
			Your Goal This Session
		</div>
		<div className="text-lg font-medium">{goal}</div>
		{/*<Button className="bg-slate-700 hover:bg-slate-800 self-end">*/}
		{/*	Refine this*/}
		{/*</Button>*/}
	</div>
)
