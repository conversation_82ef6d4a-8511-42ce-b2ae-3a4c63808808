import { Progress } from "@/components/ui/progress"
import type { SessionStats } from "@/utils/session"

type Achievement = {
	name: string
	description: string
	pictureUrl: string
	condition: (stats: SessionStats) => boolean
	progress: (stats: SessionStats) => number
}

const AVAILABLE_ACHIEVEMENTS: Achievement[] = [
	{
		name: "First Steps",
		description: "Solve your first problem",
		pictureUrl: "/achievements/first-step.png",
		condition: (stats) => stats.problemsSolved === 1,
		progress: (stats) => Math.min(1, stats.problemsSolved),
	},
	{
		name: "Streak Master",
		description: "Solve 3 problems in a row",
		pictureUrl: "/achievements/streak.png",
		condition: (stats) => stats.problemsSolved >= 3,
		progress: (stats) => stats.problemsSolved / 3,
	},
	{
		name: "Proof Seeker",
		description: "Solve 10 problems",
		pictureUrl: "/achievements/proof-seeker.png",
		condition: (stats) => stats.problemsSolved >= 10,
		progress: (stats) => stats.problemsSolved / 10,
	},
]

type CardProps = {
	name: string
	description: string
	pictureUrl: string
	progress: number
}

const AchievementCard = ({
	name,
	description,
	pictureUrl,
	progress,
}: CardProps) => {
	return (
		<div className="flex flex-row gap-5 p-3 items-center bg-sidebar-foreground rounded-md">
			<img className="w-13 h-13" src={pictureUrl} alt={name} />
			<div className="flex flex-col gap-2 text-white w-full">
				<div className="text-sm font-bold">{name}</div>
				<div className="text-xs">{description}</div>
				<Progress value={progress * 100} className="mt-1 bg-slate-100" />
			</div>
		</div>
	)
}

type Props = {
	stats: SessionStats
}

export const Achievements = ({ stats }: Props) => {
	return (
		<div className="w-full flex flex-col gap-3">
			{AVAILABLE_ACHIEVEMENTS.map((achievement) => (
				<AchievementCard
					key={achievement.name}
					name={achievement.name}
					description={achievement.description}
					pictureUrl={achievement.pictureUrl}
					progress={achievement.progress(stats)}
				/>
			))}
		</div>
	)
}
