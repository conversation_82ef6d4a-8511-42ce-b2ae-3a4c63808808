import Mathematics, {
	defaultShouldRender,
} from "@tiptap-pro/extension-mathematics"
import type { CommandProps } from "@tiptap/core"

// See: https://tiptap.dev/docs/guides/typescript#command-type
declare module "@tiptap/core" {
	interface Commands<ReturnType> {
		mathExtension: {
			toggleMath: () => ReturnType
		}
	}
}

export const MathExtension = Mathematics.configure({
	// Custom regex to detect $$...$$ blocks
	regex: /\$\$*([^$]+)\$\$*/g,
	shouldRender: defaultShouldRender,
}).extend({
	addCommands() {
		return {
			toggleMath:
				() =>
				({ state, dispatch }: CommandProps) => {
					const { from, to } = state.selection
					const selectedText = state.doc.textBetween(from, to).trim()

					// Check if already wrapped with $
					const isWrapped =
						selectedText.startsWith("$") && selectedText.endsWith("$")

					const newText = selectedText
						? isWrapped
							? selectedText.slice(1, -1) // remove $...$
							: `$${selectedText}$` // wrap in $...$, fallback to 'x'
						: "$x_1$" // create empty expression if no text is selected

					if (dispatch) {
						dispatch(state.tr.insertText(newText, from, to).scrollIntoView())
					}

					return true
				},
		}
	},
})
