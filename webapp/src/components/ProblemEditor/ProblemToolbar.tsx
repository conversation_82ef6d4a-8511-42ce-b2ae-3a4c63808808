import { ProblemEditorToolbar } from "@/components/ProblemEditor/Toolbar"
import { useProblemEditor } from "@/components/ProblemEditor/index"
import { Button } from "@/components/ui/button"
import {
	Tooltip,
	TooltipContent,
	TooltipTrigger,
} from "@/components/ui/tooltip"
import { Circle<PERSON>heck, Loader2 } from "lucide-react"
import { useLocation } from "wouter"
import { ImageToProof } from "./ImageToProof"

type Props = {
	onCheckProof: () => void
	onAbortCheckProof: () => void
	isProofShown: boolean
	isReasoning: boolean
}

export const ProblemToolbar = ({
	onCheckProof,
	isProofShown,
	isReasoning,
}: Props) => {
	const { editor, showUploadZone } = useProblemEditor()
	const isEditorPristine = !editor.getText()

	const [, navigate] = useLocation()
	const handleSkip = () => {
		navigate("/session/next")
	}

	return (
		<div>
			{showUploadZone && <ImageToProof />}
			<div className="w-full flex flex-row items-center justify-between gap-8 p-3 border-t-1 border-border">
				<ProblemEditorToolbar />
				<div className="flex flex-row gap-3">
					<Button variant="outline" onClick={handleSkip}>
						Skip Problem
					</Button>
					<Tooltip open={isProofShown ? undefined : false}>
						<TooltipTrigger asChild>
							<div>
								<Button
									variant="secondary"
									className="hover:bg-teal-800"
									disabled={isEditorPristine || isProofShown || isReasoning}
									onClick={onCheckProof}
								>
									{isReasoning ? (
										<Loader2 className="animate-spin w-5 h-5" />
									) : (
										<CircleCheck />
									)}
									Check Proof
								</Button>
							</div>
						</TooltipTrigger>
						<TooltipContent>
							Submission is not allowed once proof is revealed.
						</TooltipContent>
					</Tooltip>
				</div>
			</div>
		</div>
	)
}
