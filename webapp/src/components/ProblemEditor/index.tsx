import "katex/dist/katex.min.css"

import { TiptapEditor } from "@/components/TiptapEditor"
import { Color } from "@tiptap/extension-color"
import Highlight from "@tiptap/extension-highlight"
import Placeholder from "@tiptap/extension-placeholder"
import { TextAlign } from "@tiptap/extension-text-align"
import TextStyle from "@tiptap/extension-text-style"
import { type Editor, useEditor } from "@tiptap/react"
import StarterKit from "@tiptap/starter-kit"
import {
	type Dispatch,
	type PropsWithChildren,
	type SetStateAction,
	createContext,
	useContext,
	useState,
} from "react"
import { MathExtension } from "./MathExtension"

type ProblemEditorContextType = {
	editor: Editor
	showUploadZone: boolean
	setShowUploadZone: Dispatch<SetStateAction<boolean>>
}

// @ts-expect-error Forcing type so the editor is always defined
export const ProblemEditorContext = createContext<ProblemEditorContextType>()

export const useProblemEditor = () => useContext(ProblemEditorContext)

export const ProblemEditorContextProvider = ({
	children,
}: PropsWithChildren) => {
	const [showUploadZone, setShowUploadZone] = useState(true)

	const editor = useEditor({
		extensions: [
			StarterKit,
			TextStyle,
			Color,
			Highlight.configure({ multicolor: true }),
			Placeholder.configure({ placeholder: "Start typing your proof here..." }),
			TextAlign.configure({
				types: ["heading", "paragraph"],
			}),
			MathExtension,
		],
		editorProps: {
			attributes: {
				class: "h-full py-1 focus:outline-none",
			},
		},
	})

	if (!editor) return null

	editor.on("update", ({ editor }) => {
		if (editor.getText()) {
			setShowUploadZone(false)
		}
	})

	return (
		<ProblemEditorContext.Provider
			value={{ editor, showUploadZone, setShowUploadZone }}
		>
			{children}
		</ProblemEditorContext.Provider>
	)
}

export const ProblemEditor = () => {
	const { editor } = useProblemEditor()
	return <TiptapEditor editor={editor} />
}
