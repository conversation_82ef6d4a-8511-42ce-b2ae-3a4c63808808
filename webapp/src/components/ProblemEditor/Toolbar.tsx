import { useProblemEditor } from "@/components/ProblemEditor/index"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { useEditorState } from "@tiptap/react"
import {
	AlignCenter,
	AlignLeft,
	Baseline,
	Bold,
	CloudUpload,
	Heading1,
	Heading2,
	Italic,
	List,
	ListOrdered,
	Sigma,
} from "lucide-react"

export const ProblemEditorToolbar = () => {
	const { editor, showUploadZone, setShowUploadZone } = useProblemEditor()

	const editorState = useEditorState({
		editor,
		selector: ({ editor }) => ({
			isBold: editor.isActive("bold"),
			isItalic: editor.isActive("italic"),
			isH1: editor.isActive("heading", { level: 1 }),
			isH2: editor.isActive("heading", { level: 2 }),
			isBulletList: editor.isActive("bulletList"),
			isOrderedList: editor.isActive("orderedList"),
			isAlignCenter: editor.isActive({ textAlign: "center" }),
			isAlignLeft: editor.isActive({ textAlign: "left" }),
		}),
	})

	// TODO: can use Toggle component instead of Button
	return (
		<div className="flex flex-row gap-1 h-9">
			<Button
				size="icon"
				variant={editorState.isBold ? "default" : "ghost"}
				onClick={() => editor.chain().focus().toggleBold().run()}
			>
				<Bold />
			</Button>
			<Button
				size="icon"
				variant={editorState.isItalic ? "default" : "ghost"}
				onClick={() => editor.chain().focus().toggleItalic().run()}
			>
				<Italic />
			</Button>
			<Separator orientation="vertical" />
			<Button
				size="icon"
				variant={editorState.isH1 ? "default" : "ghost"}
				onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
			>
				<Heading1 />
			</Button>
			<Button
				size="icon"
				variant={editorState.isH2 ? "default" : "ghost"}
				onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
			>
				<Heading2 />
			</Button>
			<Button
				size="icon"
				variant="ghost"
				onClick={() => editor.chain().focus().setParagraph().run()}
			>
				<Baseline />
			</Button>
			<Separator orientation="vertical" />
			<Button
				size="icon"
				variant={editorState.isBulletList ? "default" : "ghost"}
				onClick={() => editor.chain().focus().toggleBulletList().run()}
			>
				<List />
			</Button>
			<Button
				size="icon"
				variant={editorState.isOrderedList ? "default" : "ghost"}
				onClick={() => editor.chain().focus().toggleOrderedList().run()}
			>
				<ListOrdered />
			</Button>
			<Separator orientation="vertical" />
			<Button
				size="icon"
				variant={editorState.isAlignCenter ? "default" : "ghost"}
				onClick={() => editor.chain().focus().toggleTextAlign("center").run()}
			>
				<AlignCenter />
			</Button>
			<Button
				size="icon"
				variant={editorState.isAlignLeft ? "default" : "ghost"}
				onClick={() => editor.chain().focus().toggleTextAlign("left").run()}
			>
				<AlignLeft />
			</Button>
			<Separator orientation="vertical" />
			<Button
				size="icon"
				variant="ghost"
				onClick={() => editor.chain().focus().toggleMath().run()}
			>
				<Sigma />
			</Button>
			<Separator orientation="vertical" />
			<Button
				size="icon"
				variant={showUploadZone ? "default" : "ghost"}
				onClick={() => setShowUploadZone((prev) => !prev)}
			>
				<CloudUpload />
			</Button>
		</div>
	)
}
