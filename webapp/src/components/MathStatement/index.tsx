import Markdown from "react-markdown"
import rehypeKatex from "rehype-katex"
import remarkMath from "remark-math"

import "katex/dist/katex.min.css"
import "./MathStatement.css"

type Props = {
	statement: string
}

export const MathStatement = ({ statement }: Props) => {
	return (
		<div className="math-statement">
			<Markdown remarkPlugins={[remarkMath]} rehypePlugins={[rehypeKatex]}>
				{statement}
			</Markdown>
		</div>
	)
}
