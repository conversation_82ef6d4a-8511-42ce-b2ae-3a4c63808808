import { Assistant<PERSON><PERSON><PERSON> } from "@/components/Assistant/index"
import { FeedbackPanelButton } from "@/components/FeedbackPanel/FeedbackPanelButton"
import { FeedbackPanel } from "@/components/FeedbackPanel/index"
import { Header } from "@/components/Header"
import { Lean4CodeBlock } from "@/components/Lean4CodeBlock/index"
import { MathStatement } from "@/components/MathStatement"
import { ProblemToolbar } from "@/components/ProblemActions/ProblemToolbar"
import {
	ProblemEditor,
	ProblemEditorContextProvider,
} from "@/components/ProblemEditor"
import { QueryWrapper } from "@/components/QueryWrapper"
import { Timer } from "@/components/Timer"
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { TimerProvider } from "@/contexts/TimerContext"
import { apiClient } from "@/lib/apiClient"
import { cn } from "@/lib/tailwind"
import { ShowProofWrapper } from "@/pages/Problem/ShowProofWrapper"
import { useCheckProof } from "@/pages/Problem/useCheckProof"
import type { Problem } from "@/types/problems"
import { useSession } from "@/utils/session"
import { useQuery } from "@tanstack/react-query"
import { AnimatePresence, motion } from "framer-motion"
import _ from "lodash"
import { useEffect, useState } from "react"
import { useParams } from "wouter"

type Props = {
	problem: Problem
}

const _ProblemPage = ({ problem }: Props) => {
	const { session, addProblemToSession } = useSession()

	useEffect(() => {
		if (!session.goal) {
			addProblemToSession(problem.title, problem)
		}
	}, [session.goal, problem, addProblemToSession])

	const [isAssistantOpen, setIsAssistantOpen] = useState(false)
	const problemHints = _.compact([
		problem.hintNudge,
		problem.hintConceptual,
		problem.hintDetailed,
	])

	const [showProof, setShowProof] = useState(false)
	const [currenTab, setCurrentTab] = useState("editor")

	const handleShowProof = () => {
		setShowProof(true)
		if (currenTab === "editor") {
			setCurrentTab("referenceProof")
		}
	}

	const [isFeedbackPanelOpen, setIsFeedbackPanelOpen] = useState(false)

	const { proofSubmissions, isReasoning, handleCheckProof, handleAbort } =
		useCheckProof()

	return (
		<div className="flex flex-col">
			<div className="fixed top-0 left-0 z-100 flex flex-row gap-2">
				<Header variant="dark" />
				{/*<SessionDrawer />*/}
				{/*<AskAxion />*/}
			</div>
			<div
				className={cn("w-full flex flex-row justify-center gap-20", {
					"justify-end": isFeedbackPanelOpen,
				})}
			>
				<motion.div
					className="md:max-w-xl lg:max-w-3xl w-full flex flex-col justify-between h-dvh max-h-dv"
					animate={{
						marginRight: isFeedbackPanelOpen ? "0" : "auto",
						marginLeft: isFeedbackPanelOpen ? "0" : "auto",
					}}
				>
					<div className="flex flex-col gap-2 py-2 mt-8">
						<div className="font-medium">{problem.title}</div>
						<MathStatement statement={problem.informalStatement} />
					</div>
					<Tabs
						value={currenTab}
						onValueChange={setCurrentTab}
						className="w-full h-full mt-4 overflow-hidden"
					>
						<TabsList>
							<TabsTrigger value="editor">Your Proof</TabsTrigger>
							<TabsTrigger value="referenceProof">Reference Proof</TabsTrigger>
							<TabsTrigger value="leanProof">Lean Proof</TabsTrigger>
						</TabsList>
						<TabsContent
							value="editor"
							className="h-full flex flex-col justify-between overflow-y-scroll"
						>
							<div className="overflow-y-scroll mb-2">
								<ProblemEditor />
							</div>
							<ProblemToolbar
								onCheckProof={() => handleCheckProof(problem.informalStatement)}
								onAbortCheckProof={handleAbort}
								isProofShown={showProof}
							/>
						</TabsContent>
						<TabsContent value="referenceProof" className="overflow-y-scroll">
							<ShowProofWrapper
								isProofShown={showProof}
								onShowProof={handleShowProof}
							>
								<MathStatement statement={problem.informalProof!} />
							</ShowProofWrapper>
						</TabsContent>
						<TabsContent value="leanProof" className="overflow-y-scroll">
							<ShowProofWrapper
								isProofShown={showProof}
								onShowProof={handleShowProof}
							>
								<Lean4CodeBlock lean4Code={problem.formalProof!} />
							</ShowProofWrapper>
						</TabsContent>
					</Tabs>
				</motion.div>
				<AnimatePresence>
					{isFeedbackPanelOpen && (
						<FeedbackPanel
							proofSubmissions={proofSubmissions}
							isReasoning={isReasoning}
							onClose={() => setIsFeedbackPanelOpen(false)}
						/>
					)}
				</AnimatePresence>
			</div>
			<motion.div
				animate={{ opacity: isAssistantOpen ? 0 : 1 }}
				className="fixed top-0 right-0 h-full w-fit flex flex-col p-4 gap-3"
			>
				<FeedbackPanelButton onClick={() => setIsFeedbackPanelOpen(true)} />
				<Timer />
				<AssistantDrawer
					problemUuid={problem.uuid}
					hints={problemHints}
					isOpen={isAssistantOpen}
					onOpenChange={setIsAssistantOpen}
					isProofShown={showProof}
				/>
			</motion.div>
		</div>
	)
}

export const ProblemPage = () => {
	const params = useParams<{ id: string }>()

	const problemQuery = useQuery({
		queryKey: ["problems", params.id],
		queryFn: async () => {
			const response = await apiClient.get<Problem>(
				`/problems/uuid/${params.id}`,
			)
			return response.data
		},
	})

	return (
		<div className="w-full h-dvh">
			<QueryWrapper query={problemQuery}>
				{({ data: problem }) => (
					<TimerProvider>
						<ProblemEditorContextProvider>
							<_ProblemPage problem={problem} />
						</ProblemEditorContextProvider>
					</TimerProvider>
				)}
			</QueryWrapper>
		</div>
	)
}
