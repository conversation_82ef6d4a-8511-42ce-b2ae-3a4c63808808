import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
	AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Button } from "@/components/ui/button"
import { EyeOff } from "lucide-react"
import type { ReactNode } from "react"

type Props = {
	isProofShown: boolean
	onShowProof: () => void
	children: ReactNode
}

export const ShowProofWrapper = ({
	children,
	isProofShown,
	onShowProof,
}: Props) => {
	if (isProofShown) return children
	return (
		<AlertDialog>
			<AlertDialogTrigger asChild>
				<div className="w-full h-full flex flex-col gap-1 text-sm items-center my-20">
					<EyeOff className="w-8 h-8" />
					<div className="font-bold">See the solution?</div>
					<div className="text-muted-foreground mb-2">
						Once revealed, you won't be able to submit your own proof.
					</div>
					<Button size="lg" variant="secondary">
						Reveal Proof
					</Button>
				</div>
			</AlertDialogTrigger>
			<AlertDialogContent>
				<AlertDialogHeader>
					<AlertDialogTitle>Ready to see the solution?</AlertDialogTitle>
					<AlertDialogDescription>
						{/* TODO: remove first sentence if hints are all used */}
						You can ask for a hint first to keep practicing. Once revealed, you
						won't be able to submit your own proof.
					</AlertDialogDescription>
				</AlertDialogHeader>
				<AlertDialogFooter>
					<AlertDialogCancel>Cancel</AlertDialogCancel>
					<AlertDialogAction onClick={onShowProof}>Continue</AlertDialogAction>
				</AlertDialogFooter>
			</AlertDialogContent>
		</AlertDialog>
	)
}
