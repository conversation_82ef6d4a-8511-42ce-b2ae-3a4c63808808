import { useProblemEditor } from "@/components/ProblemEditor/index"
import { useTimer } from "@/contexts/TimerContext"
import { apiClient } from "@/lib/apiClient"
import camelcaseKeys from "camelcase-keys"
import { useRef, useState } from "react"

export type MathematicalError = {
	locationInProof: string
	severity: string
	errorType: string
	explanation: string
	suggestedHint: string
}

export type SubmissionResult = {
	explanation: string
	mathematicalErrors: MathematicalError[]
}

/**
 * Statuses:
 * - reasoning: the AI is reasoning
 * - error: the submission call encountered a technical error
 * - success: the AI considers the proof valid
 * - failure: the AI considers the proof invalid and provides feedback
 */
type SubmissionStatus = "reasoning" | "error" | "success" | "failure"

export type ProofSubmission = SubmissionResult & {
	proofText: string
	submittedAt: Date
	status: SubmissionStatus
	reasoning: string
}

type ProofAttemptCheck = {
	isValid: boolean
	reason: string
}

const useProofSubmissions = () => {
	const [proofSubmissions, setProofSubmissions] = useState<ProofSubmission[]>(
		[],
	)

	const startNewSubmission = (proofText: string) => {
		setProofSubmissions([
			{
				proofText,
				submittedAt: new Date(),
				status: "reasoning",
				reasoning: "",
				explanation: "",
				mathematicalErrors: [],
			},
			...proofSubmissions,
		])
	}

	const updateSubmissionReasoning = (reasoning: string) => {
		setProofSubmissions((prev) => {
			const latestSubmission = prev[0]
			if (!latestSubmission) return prev
			return [
				{
					...latestSubmission,
					reasoning,
				},
				...prev.slice(1),
			]
		})
	}

	const updateSubmissionResult = (
		status: SubmissionStatus,
		explanation: string,
		mathematicalErrors: MathematicalError[] = [],
	) => {
		setProofSubmissions((prev) => {
			const latestSubmission = prev[0]
			if (!latestSubmission) return prev
			return [
				{
					...latestSubmission,
					status,
					explanation,
					mathematicalErrors,
				},
				...prev.slice(1),
			]
		})
	}

	return {
		proofSubmissions,
		startNewSubmission,
		updateSubmissionReasoning,
		updateSubmissionResult,
	}
}

const formatSubmissionResult = (result: string): SubmissionResult => {
	// Match everything before the ```json block and capture the JSON inside
	const match = result.match(/([\s\S]*?)```json\s*([\s\S]*?)\s*```/)

	if (match) {
		const explanation = match[1].trim()
		let mathematicalErrors: MathematicalError[]

		try {
			const parsedMathematicalErrors = camelcaseKeys(
				JSON.parse(match[2].replace(/\n/g, "").replace(/\\/g, "$")),
				{ deep: true },
			)
			mathematicalErrors = parsedMathematicalErrors.mathematicalErrors
		} catch (err) {
			mathematicalErrors = []
		}

		return {
			explanation,
			mathematicalErrors,
		}
	}

	return {
		explanation: result.trim(),
		mathematicalErrors: [],
	}
}

export const useCheckProof = () => {
	const { pauseTimer } = useTimer()
	const { editor } = useProblemEditor()

	const {
		proofSubmissions,
		startNewSubmission,
		updateSubmissionReasoning,
		updateSubmissionResult,
	} = useProofSubmissions()

	const abortController = useRef(new AbortController())
	const handleAbort = () => {
		abortController.current.abort()
		abortController.current = new AbortController()
	}

	const handleCheckProof = async (problemStatement: string) => {
		pauseTimer()

		const proofText = editor.getText()
		const attemptPayload = {
			attempt: {
				problem_statement: problemStatement,
				proof_text: proofText,
			},
		}

		startNewSubmission(proofText)

		try {
			const checkResponse = await apiClient.post<ProofAttemptCheck>(
				"/check-proof-attempt",
				attemptPayload,
			)

			if (!checkResponse.data.isValid) {
				updateSubmissionResult("error", checkResponse.data.reason)
				return
			}

			let submissionReasoning = ""
			let submissionResult = ""

			await apiClient.post("/analyze-proof", attemptPayload, {
				responseType: "text",
				signal: abortController.current.signal,
				headers: { Accept: "text/event-stream" },
				onDownloadProgress: (progressEvent) => {
					const chunk = progressEvent.event.target.responseText
					// Process the chunk here
					const lines = chunk.split("\n")

					for (const line of lines) {
						if (line.startsWith("data: ")) {
							const data = JSON.parse(line.slice(6))

							switch (data.type) {
								case "thinking":
									if (data.content) {
										submissionReasoning += data.content
											.replaceAll("<think>", "")
											.replaceAll("</think>", "")
										updateSubmissionReasoning(submissionReasoning)
									}
									break
								case "thinking_complete":
									console.log("Thinking complete:", submissionReasoning)
									break
								case "result":
									submissionResult += data.content
									break
								case "error":
									throw new Error(data.message || "Analysis failed")
							}
						}
					}
				},
			})

			if (submissionResult.trim()) {
				const { explanation, mathematicalErrors } =
					formatSubmissionResult(submissionResult)

				// Highlight errors in editor
				for (const error of mathematicalErrors) {
					const charIndexInProof = proofText.indexOf(error.locationInProof)
					if (charIndexInProof !== -1) {
						editor.commands.insertContentAt(
							{
								from: charIndexInProof,
								to: charIndexInProof + error.locationInProof.length + 1,
							},
							`<mark style="background-color: #FEE2E2;"><span style="color: #EF4444;">${error.locationInProof}</span></mark>`,
						)
					}
				}

				updateSubmissionResult(
					mathematicalErrors.length ? "failure" : "success",
					explanation,
					mathematicalErrors,
				)
			} else {
				throw new Error("No result received from analysis")
			}
		} catch (error) {
			console.error("Submission error:", error)

			if (error instanceof Error && error.name === "AbortError") {
				updateSubmissionResult("error", "Submission aborted.")
			} else if (error instanceof Error) {
				updateSubmissionResult("error", error.message)
			} else {
				updateSubmissionResult(
					"error",
					"An unexpected error occurred during verification.",
				)
			}
		}
	}

	return {
		isReasoning: proofSubmissions[0]?.status === "reasoning",
		proofSubmissions,
		handleCheckProof,
		handleAbort,
	}
}
