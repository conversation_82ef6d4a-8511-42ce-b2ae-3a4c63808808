import { Achievements } from "@/components/Session/Achievements"
import { SessionStat } from "@/components/Session/SessionStat"
import { Separator } from "@/components/ui/separator"
import { useSession } from "@/utils/session"
import J<PERSON>onfetti from "js-confetti"
import { ChevronRight, ChevronsRight } from "lucide-react"
import { useEffect } from "react"

import { ActionButtonWithKeymap } from "@/components/FeedbackPanel/ActionButtonWithKeymap"
import { useTimer } from "@/contexts/TimerContext"
import { FocusMeter } from "./FocusMeter"

const jsConfetti = new JSConfetti()

const sendConfetti = () => {
	jsConfetti.addConfetti({
		emojiSize: 50,
		emojis: ["🟰", "➕", "➗", "×", "🏆", "🚀", "👍", "⚡"],
		confettiNumber: 150,
	})
	jsConfetti.addConfetti({
		confettiNumber: 50,
	})
}

export const SessionSummaryPage = () => {
	const { stats } = useSession()
	const { formatTime } = useTimer()

	useEffect(() => {
		sendConfetti()
		const interval = setInterval(sendConfetti, 4000)
		return () => clearInterval(interval)
	}, [])

	return (
		<div className="relative w-full h-full flex flex-col items-center">
			<canvas id="confetti" className="absolute" />
			<div className="z-10000 bg-sidebar w-full max-w-2xl h-full overflow-y-scroll px-6 py-20 flex flex-col gap-6 text-white">
				<div className="flex flex-col gap-3">
					<h1 className="text-white text-5xl">Session Summary</h1>
					<div className="text-xl">
						Great work! You've completed your session.
					</div>
				</div>
				<div className="flex flex-row justify-between gap-2">
					<SessionStat label="Problems solved" value={stats.problemsSolved} />
					<SessionStat label="Hints used" value={stats.totalHintsUsed} />
					<SessionStat
						label="Time spent"
						value={formatTime(stats.totalTimeSpent)}
					/>
				</div>
				<div className="flex flex-col gap-5">
					<div>
						<h2 className="text-3xl text-green-200">Session Focus Meter</h2>
						<div className="font-medium text-lg">
							Here’s where you trained today.
						</div>
					</div>
					<FocusMeter />
					<ul className="mt-0 mb-0">
						<li>You focused mostly on Logic and Algebra</li>
						<li>Geometry was also explored, lightly</li>
					</ul>
					<Separator className="bg-muted-foreground" />
				</div>
				<div className="flex flex-col gap-5">
					<div>
						<h2 className="text-3xl text-green-200">Achievements</h2>
						<div className="font-medium text-lg">
							Nice work keeping a 3-problem streak while exploring formal proof!
						</div>
					</div>
					<Achievements stats={stats} />
					<Separator className="bg-muted-foreground" />
				</div>
				<div className="flex flex-col gap-5">
					<div>
						<h2 className="text-3xl text-green-200">What next?</h2>
						<div className="font-medium text-lg">
							You’re mastering easy problems, but still relying on hints. Try
							complexe..
						</div>
					</div>
					<div className="flex flex-col gap-1">
						<ActionButtonWithKeymap
							variant="primary"
							label="Start again"
							hotkey="meta+R"
							icon={ChevronRight}
							onClick={() => alert("START AGAIN")}
						/>
						<ActionButtonWithKeymap
							variant="primary"
							label="Start a new session with this goal"
							hotkey="meta+G"
							icon={ChevronsRight}
							onClick={() => alert("START NEW GOAL")}
						/>
					</div>
				</div>
			</div>
		</div>
	)
}
