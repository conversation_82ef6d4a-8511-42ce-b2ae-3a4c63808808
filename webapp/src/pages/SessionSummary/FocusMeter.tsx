import { useSession } from "@/utils/session"
import { useMemo } from "react"
import ApexChart, { type Props as Chart<PERSON><PERSON> } from "react-apexcharts"

const CHART_COLORS = ["#B499E6", "#71B67F", "#D46B3B", "#4B88AA"]

export const FocusMeter = () => {
	const { computeFocusMeter } = useSession()

	const focusMeter = useMemo(computeFocusMeter, [])
	const chartState = useMemo<ChartProps>(() => {
		return {
			series: Object.values(focusMeter),
			options: {
				colors: CHART_COLORS,
				chart: { type: "radialBar" },
				plotOptions: {
					radialBar: {
						startAngle: -135,
						endAngle: 225,
						track: {
							background: "#14222A",
						},
					},
				},
				labels: Object.keys(focusMeter),
			},
		}
	}, [focusMeter])

	return (
		<div className="bg-sidebar-foreground rounded-md flex flex-row justify-center items-center w-full h-[300px]">
			<ApexChart
				options={chartState.options}
				series={chartState.series}
				type="radialBar"
				height={300}
			/>
			<div className="flex flex-col gap-6">
				{Object.keys(focusMeter).map((domain, index) => (
					<div key={domain} className="flex flex-row gap-2 items-center">
						<div
							className="w-3 h-3 rounded-full"
							style={{ backgroundColor: CHART_COLORS[index] }}
						/>
						<div className="text-sm text-sidebar-accent-foreground">
							{domain}
						</div>
					</div>
				))}
			</div>
		</div>
	)
}
