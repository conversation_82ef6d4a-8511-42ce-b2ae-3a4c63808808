import { CameraScanner } from "@/components/ProofScanner/CameraScanner"
import { GalleryScanner } from "@/components/ProofScanner/GalleryScanner"
import { UploadButton } from "@/components/UploadButton"
import { Camera, CloudUpload } from "lucide-react"
import { useState } from "react"

type PreviewMode = "init" | "camera" | "files"

// TODO: use sonner toasts for notifications? error/success
export const ScanProofPage = () => {
	const [previewMode, setPreviewMode] = useState<PreviewMode>("init")
	const [selectedFiles, setSelectedFiles] = useState<File[]>([])

	const handleSubmit = () => {
		console.log("FILES", selectedFiles)
	}

	const handleError = () => {
		console.log("ERROR")
	}

	const handleReset = () => {
		setSelectedFiles([])
		setPreviewMode("init")
	}

	const handleRemove = (index: number) => {
		setSelectedFiles(selectedFiles.filter((_, i) => i !== index))
	}

	const handleTakePicture = (files: File[]) => {
		setPreviewMode("camera")
		setSelectedFiles(files)
	}

	const handleSelectPicturesFromGallery = (files: File[]) => {
		setPreviewMode("files")
		setSelectedFiles((prev) => [...prev, ...files])
	}

	return (
		<div className="flex flex-col items-center w-screen h-screen">
			<div className="flex flex-col items-center w-full py-4 bg-slate-800">
				<span className="text-white text-2xl leading-none font-bold">
					Axion
				</span>
			</div>
			<div className="flex flex-col gap-4 w-full h-full p-4">
				<h1 className="text-3xl text-center">Import Your Math Proof</h1>
				{previewMode === "camera" ? (
					<CameraScanner
						file={selectedFiles[0]}
						onRetake={handleTakePicture}
						onError={handleError}
						onSubmit={handleSubmit}
						onCancel={handleReset}
					/>
				) : previewMode === "files" ? (
					<GalleryScanner
						files={selectedFiles}
						onAddPictures={handleSelectPicturesFromGallery}
						onError={handleError}
						onRemove={handleRemove}
						onSubmit={handleSubmit}
						onCancel={handleReset}
					/>
				) : (
					<div className="flex flex-col gap-4 items-center mt-10">
						<UploadButton
							size="lg"
							onUpload={handleSelectPicturesFromGallery}
							onError={handleError}
						>
							<CloudUpload />
							Import Photo
						</UploadButton>
						<div className="flex flex-col gap-1">- OR -</div>
						<UploadButton
							size="lg"
							onUpload={handleTakePicture}
							onError={handleError}
						>
							<Camera />
							Take Photo
						</UploadButton>
					</div>
				)}
			</div>
		</div>
	)
}
