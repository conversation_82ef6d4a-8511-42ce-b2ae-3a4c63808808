import { DifficultyScale } from "@/components/DifficultyScale"
import { EventProblemCard } from "@/components/EventProblemCard"
import { Header } from "@/components/Header"
import { QueryWrapper } from "@/components/QueryWrapper"
import { apiClient } from "@/lib/apiClient"
import type { Event } from "@/types/events"
import { useSession } from "@/utils/session"
import { useQuery } from "@tanstack/react-query"
import dayjs from "dayjs"
import _ from "lodash"
import { Link, useParams } from "wouter"

type Props = {
	event: Event
}

const _EventPage = ({ event }: Props) => {
	const { addProblemToSession } = useSession()

	const problems = Object.entries(
		_.groupBy(
			event.problems.filter((p) => dayjs(p.revealedAt).isBefore(dayjs())),
			(p: (typeof event.problems)[number]) => p.revealedAt,
		),
	).flatMap(([revealedAt, problems], index) => {
		return problems.map((problem) => ({
			dayIndex: index,
			revealedAt: new Date(revealedAt),
			// TODO: missing data in the backend
			problem: {
				...problem.problem,
				title: "Stability in GCD of Polynomial Expressions",
				description: problem.problem.informalStatement,
				difficulty: 2,
				duration: 10,
			},
		}))
	})

	return (
		<div className="flex flex-col w-full h-dvh bg-sidebar">
			<Header withAxion withNoLoginCta />
			<div className="bg-sidebar w-full h-full flex flex-col items-center">
				<div className="flex flex-col w-5xl items-center">
					<img
						alt="Event Logo"
						src={event.logoUrl}
						className="h-[120px] object-contain"
					/>

					<h1 className="text-3xl text-center text-white">{event.title}</h1>
				</div>
				<div className="w-full flex flex-col flex-nowrap gap-4 mt-8 items-center">
					<div className="w-full px-10 overflow-y-scroll">
						<div className="flex flex-col gap-4 w-fit">
							<DifficultyScale />
							<div className="w-auto mt-2 flex flex-row gap-6">
								{problems.map(({ dayIndex, revealedAt, problem }, index) => (
									<Link
										key={problem.uuid}
										to={`/problems/${problem.uuid}`}
										onClick={() =>
											addProblemToSession(`event:${event.slug}`, problem)
										}
									>
										<EventProblemCard
											dayIndex={dayIndex}
											problemIndex={index}
											revealedAt={revealedAt}
											problem={problem}
										/>
									</Link>
								))}
							</div>
						</div>
					</div>
				</div>
				<div className="w-full flex justify-center mt-auto mb-8">
					<Link to="/">
						<div className="text-white text-sm underline">
							Or choose your challenge
						</div>
					</Link>
				</div>
			</div>
		</div>
	)
}

export const EventPage = () => {
	const params = useParams<{ slug: string }>()

	const eventQuery = useQuery({
		queryKey: ["events", params.slug],
		queryFn: async () => {
			const response = await apiClient.get<Event>(`/events/${params.slug}`)
			return response.data
		},
	})

	return (
		<div className="flex flex-col w-full h-dvh bg-sidebar">
			<QueryWrapper query={eventQuery}>
				{({ data: event }) => <_EventPage event={event} />}
			</QueryWrapper>
		</div>
	)
}
