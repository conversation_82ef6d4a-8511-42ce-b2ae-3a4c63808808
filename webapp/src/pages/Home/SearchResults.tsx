import { DifficultyScale } from "@/components/DifficultyScale"
import { ProblemCard } from "@/components/ProblemCard"
import { Badge } from "@/components/ui/badge"
import type { Problem } from "@/types/problems"
import { useSession } from "@/utils/session"
import _ from "lodash"
import { X } from "lucide-react"
import { Link } from "wouter"

type Props = {
	query: string
	problems: Problem[]
	tags: string[]
}

export const SearchResults = ({ query, tags, problems }: Props) => {
	const { addProblemToSession } = useSession()

	return (
		<div className="w-full flex flex-col py-2 px-25 items-center">
			<div className="w-2xl flex flex-row gap-2 items-start">
				{tags.map((tag) => (
					<Badge key={tag}>
						{tag}
						<X />
					</Badge>
				))}
			</div>
			<div className="max-w-7xl w-full flex flex-col gap-4 mt-20 items-center">
				<div className="text-white text-2xl font-bold">
					These problems were selected for you.
				</div>
				<div className="w-full px-20">
					<DifficultyScale showLabels />
				</div>
				<div className="mt-2 w-full grid grid-cols-4 gap-6">
					{_.sortBy(problems, (p) => p.difficultyLevel).map((problem) => (
						<Link
							key={problem.uuid}
							to={`/problems/${problem.uuid}`}
							onClick={() => addProblemToSession(query, problem)}
						>
							<ProblemCard problem={problem} />
						</Link>
					))}
				</div>
			</div>
		</div>
	)
}
