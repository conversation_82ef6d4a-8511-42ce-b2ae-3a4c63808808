import { RedGlare } from "@/components/RedGlare"
import { Badge } from "@/components/ui/badge"
import { apiClient } from "@/lib/apiClient"
import type { QuickPick } from "@/types/search"
import { useQuery } from "@tanstack/react-query"
import { AnimatePresence, motion } from "framer-motion"
import { Loader2 } from "lucide-react"
import { type KeyboardEvent, useEffect } from "react"
import { useLocation, useSearchParams } from "wouter"

type Props = {
	input: string
	onChange: (input: string) => void
	view: "home" | "results"
	onSubmit: (query: string) => void
	isLoading: boolean
}

export const SearchBar = ({
	input,
	onChange,
	view,
	onSubmit,
	isLoading,
}: Props) => {
	const [, navigate] = useLocation()
	const [, setSearchParams] = useSearchParams()

	const handleSubmit = (query: string) => {
		if (query) {
			setSearchParams({ q: query })
			onSubmit(query)
		}
	}

	const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
		if (e.key === "Enter") {
			handleSubmit(input)
		}
	}

	// biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
	useEffect(() => {
		if (input) handleSubmit(input)
	}, [])

	const { data: quickPicks } = useQuery({
		queryKey: ["quick-picks"],
		queryFn: async () => {
			const response = await apiClient.get<QuickPick[]>("/quick-picks")
			return response.data
		},
	})

	const handleQuickPickClick = (quickPick: QuickPick) => {
		if (quickPick.event) {
			navigate(`/events/${quickPick.event.slug}`)
			return
		}
		if (quickPick.searchQuery) {
			onChange(quickPick.searchQuery)
			handleSubmit(quickPick.searchQuery)
		} else {
			console.error("Quick pick has no search query or event")
		}
	}

	const showQuickPicks = !isLoading && (view === "home" || !input)

	return (
		<div className="w-full flex flex-col gap-5 bg-white rounded-md p-3">
			<div className="flex flex-row gap-2 w-full items-center">
				<RedGlare />
				<input
					type="search"
					value={input}
					onChange={(e) => onChange(e.target.value)}
					onKeyDown={handleKeyDown}
					className="w-full focus:outline-0"
					placeholder="Give me a refresher on convex inequalities"
				/>
			</div>
			<AnimatePresence mode="wait">
				{showQuickPicks && (
					<motion.div
						key="quick-picks"
						className="flex flex-row gap-2 items-center justify-end"
						animate={{ height: "auto " }}
						exit={{ height: 0, opacity: 0 }}
						transition={{ ease: "easeInOut", duration: 0.3 }}
					>
						<div className="text-gray text-xs">Quick Picks:</div>
						{!quickPicks && <Loader2 size={14} className="animate-spin" />}
						{quickPicks?.map((quickPick) => (
							<Badge
								key={quickPick.id}
								variant="outline"
								className="rounded-xl text-sm font-medium text-gray-600 hover:bg-gray-50 cursor-pointer"
								onClick={() => handleQuickPickClick(quickPick)}
							>
								{quickPick.label}
							</Badge>
						))}
					</motion.div>
				)}
			</AnimatePresence>
		</div>
	)
}
