import { Skeleton } from "@/components/ui/skeleton"

const TagSkeleton = () => <Skeleton className="w-30 h-6 rounded-sm" />

const ProblemSkeleton = () => <Skeleton className="h-100 rounded-sm" />

export const ResultsSkeleton = () => (
	<div className="w-full flex flex-col py-2 px-25 items-center">
		<div className="w-2xl flex flex-row gap-2 items-start">
			<TagSkeleton />
			<TagSkeleton />
			<TagSkeleton />
		</div>
		<div className="max-w-7xl w-full flex flex-col gap-4 mt-20 items-center">
			<Skeleton className="w-110 h-8 rounded-sm" />
			<Skeleton className="w-full h-4 rounded-sm px-20" />
			<div className="mt-2 w-full grid grid-cols-4 gap-6">
				<ProblemSkeleton />
				<ProblemSkeleton />
				<ProblemSkeleton />
				<ProblemSkeleton />
			</div>
		</div>
	</div>
)
