import { Header } from "@/components/Header"
import { apiClient } from "@/lib/apiClient"
import { cn } from "@/lib/tailwind"
import { ResultsSkeleton } from "@/pages/Home/ResultsSkeleton"
import { SearchBar } from "@/pages/Home/SearchBar"
import { SearchResults } from "@/pages/Home/SearchResults"
import type { ProblemSearchResults } from "@/types/search"
import { useMutation } from "@tanstack/react-query"
import { AnimatePresence, motion } from "framer-motion"
import { useEffect, useState } from "react"
import { useSearchParams } from "wouter"

export const HomePage = () => {
	const [searchParams] = useSearchParams()
	const querySearchParam = searchParams.get("q")

	const [searchInput, setSearchInput] = useState(querySearchParam ?? "")
	const [view, setView] = useState<"home" | "results">("home")

	useEffect(() => {
		if (!querySearchParam) {
			setView("home")
			setSearchInput("")
		}
	}, [querySearchParam])

	const {
		data: results,
		isPending,
		mutateAsync,
	} = useMutation({
		mutationFn: async (query: string): Promise<ProblemSearchResults> => {
			const response = await apiClient.get(`/problems?query=${query}&limit=4`)
			return response.data
		},
		onMutate: () => {
			setView("results")
		},
	})

	return (
		<div className="flex flex-col w-full h-dvh bg-sidebar">
			<Header withNoLoginCta />
			<div className="w-full h-full flex flex-col items-center">
				<motion.div
					className="flex flex-col w-2xl relative"
					style={{ marginTop: view === "home" ? "20vh" : 0 }}
					animate={{ marginTop: view === "home" ? "20vh" : 0 }}
					transition={{ duration: 0.5, delay: 0.3 }}
				>
					<AnimatePresence mode="wait">
						{view === "home" && (
							<motion.div
								className="flex flex-col gap-2 mb-4 absolute w-full"
								initial={{ opacity: 1 }}
								exit={{ opacity: 0 }}
								transition={{ duration: 0.3 }}
							>
								<div className="font-mono text-lg text-white">
									A smarter way to master math.
								</div>
								<h1 className="text-5xl text-white">Explore. Prove. Grow.</h1>
								<div className="mt-2 text-white">
									What do you want to work on today?
								</div>
							</motion.div>
						)}
					</AnimatePresence>
					<div
						className={cn("transition-all duration-300 mt-0", {
							"mt-32": view === "home",
						})}
					>
						<SearchBar
							input={searchInput}
							onChange={setSearchInput}
							view={view}
							isLoading={isPending}
							onSubmit={mutateAsync}
						/>
					</div>
				</motion.div>
				{view === "results" && (
					<motion.div
						initial={{ opacity: 0 }}
						animate={{ opacity: 1 }}
						exit={{ opacity: 0 }}
						transition={{ delay: 0.8, duration: 0.3 }}
						className="w-full"
					>
						{results ? (
							<SearchResults
								query={searchInput}
								problems={results.problemMatches}
								tags={[]}
							/>
						) : (
							<ResultsSkeleton />
						)}
					</motion.div>
				)}
			</div>
		</div>
	)
}
