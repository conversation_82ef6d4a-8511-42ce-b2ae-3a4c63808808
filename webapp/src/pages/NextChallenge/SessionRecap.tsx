import { useSession } from "@/utils/session"
import { CircleCheckBig, CircleX } from "lucide-react"

export const SessionRecap = () => {
	const { session } = useSession()

	const completedProblems = session.problems.filter((p) =>
		["solved", "failed"].includes(p.status),
	)
	if (completedProblems.length === 0) return null
	return (
		<div className="flex flex-row gap-4 p-1 rounded-full border-1 border-border">
			{completedProblems.map((p) => {
				switch (p.status) {
					case "solved":
						return <CircleCheckBig className="w-6 h-6 stroke-green-400" />
					case "failed":
						return <CircleX className="w-6 h-6 stroke-red-400" />
					default:
						return null
				}
			})}
		</div>
	)
}
