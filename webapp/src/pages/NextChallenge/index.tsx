import { Header } from "@/components/Header"
import { ProblemCard } from "@/components/ProblemCard"
import { QueryWrapper } from "@/components/QueryWrapper"
import { apiClient } from "@/lib/apiClient"
import { NextProblemsHeader } from "@/pages/NextChallenge/NextProblemsHeader"
import { SessionRecap } from "@/pages/NextChallenge/SessionRecap"
import type { Problem } from "@/types/problems"
import type { ProblemSearchResults } from "@/types/search"
import { useSession } from "@/utils/session"
import { useQuery } from "@tanstack/react-query"
import _ from "lodash"
import { Link } from "wouter"

type Props = {
	problems: Problem[]
}

const _NextChallengePage = ({ problems }: Props) => {
	const { session, addProblemToSession } = useSession()

	return (
		<div className="flex flex-col w-full h-dvh bg-sidebar">
			<Header withAxion />
			<div className="p-10 bg-sidebar w-full h-full flex flex-col items-center">
				<div className="max-w-7xl w-full h-full flex flex-col items-center justify-center">
					<div className="flex flex-col gap-2 w-6xl items-center">
						<SessionRecap />
						<h1 className="text-3xl text-center text-white">
							You're on track - keep going!
						</h1>
						<NextProblemsHeader />
					</div>
					<div className="mt-14 w-full grid grid-cols-4 gap-6">
						{_.sortBy(problems, (p) => p.difficultyLevel).map((problem) => (
							<Link
								key={problem.uuid}
								to={`/problems/${problem.uuid}`}
								onClick={() => addProblemToSession(session.goal, problem)}
							>
								<ProblemCard problem={problem} />
							</Link>
						))}
					</div>
				</div>
			</div>
		</div>
	)
}

export const NextChallengePage = () => {
	const { session } = useSession()

	const alreadyTackledProblemsUuids = session.problems.map(
		(p) => p.problem.uuid,
	)

	const nextChallengeQuery = useQuery({
		queryKey: ["next-challenge", session.goal, alreadyTackledProblemsUuids],
		queryFn: async () => {
			const response = await apiClient.get<ProblemSearchResults>(
				`/problems?query=${session.goal}`,
			)
			const unTackledProblems = response.data.problemMatches.filter(
				(p) => !alreadyTackledProblemsUuids.includes(p.uuid),
			)
			return _.take(unTackledProblems, 4)
		},
		staleTime: 0,
	})

	return (
		<QueryWrapper query={nextChallengeQuery}>
			{({ data: problems }) => <_NextChallengePage problems={problems} />}
		</QueryWrapper>
	)
}
