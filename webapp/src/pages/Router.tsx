import { Route, Switch } from "wouter"

import { EventPage } from "./Event"
import { HomePage } from "./Home"
import { NextChallengePage } from "./NextChallenge"
import { ProblemPage } from "./Problem"
import { ScanProofPage } from "./ScanProof"
import { SessionSummaryPage } from "./SessionSummary"

export const Router = () => (
	<Switch>
		<Route path="/" component={HomePage} />
		<Route path="/events/:slug" component={EventPage} />
		<Route path="/problems/:id" component={ProblemPage} />
		<Route path="/session/next" component={NextChallengePage} />
		<Route path="/session/summary" component={SessionSummaryPage} />
		<Route path="/scan-proof/:id" component={ScanProofPage} />
	</Switch>
)
