{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false, "ignore": ["./src/types/prisma"]}, "formatter": {"enabled": true, "indentStyle": "tab"}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "style": {"noNonNullAssertion": "warn", "useShorthandArrayType": "error"}, "nursery": {"useConsistentCurlyBraces": "error"}, "correctness": {"noUnusedImports": "error"}, "suspicious": {"noExplicitAny": "warn"}, "security": {"noDangerouslySetInnerHtml": "warn"}, "a11y": {"useFocusableInteractive": "warn", "useSemanticElements": "warn"}}}, "javascript": {"formatter": {"quoteStyle": "double", "semicolons": "asNeeded"}}}