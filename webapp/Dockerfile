FROM node:22 AS build-stage

ARG HTTP_PROXY
ARG HTTPS_PROXY

ENV NODE_OPTIONS="--max-old-space-size=4096"

WORKDIR /app

ARG TIPTAP_PRO_TOKEN
ENV TIPTAP_PRO_TOKEN=$TIPTAP_PRO_TOKEN

RUN echo "@tiptap-pro:registry=https://registry.tiptap.dev/" > .npmrc && \
    echo "//registry.tiptap.dev/:_authToken=${TIPTAP_PRO_TOKEN}" >> .npmrc

ENV VITE_API_URL="/api"

COPY package*.json ./
RUN npm install

COPY . .

RUN echo "Checking TypeScript version in CI environment..." && \
    npx tsc --version && \
    echo "TypeScript version check complete."

RUN npm run build

FROM nginx:stable-alpine AS production-stage

COPY --from=build-stage /app/dist /usr/share/nginx/html

EXPOSE 5173

COPY frontend-nginx.conf /etc/nginx/conf.d/default.conf

CMD ["nginx", "-g", "daemon off;"]
