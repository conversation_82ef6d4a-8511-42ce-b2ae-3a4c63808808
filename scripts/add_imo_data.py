import ast
import uuid

import numpy as np
import pandas as pd
from datasets import load_dataset

hf_dataset = load_dataset("AI-MO/aops_wiki_imo", split="train")
hf_df = hf_dataset.to_pandas()


def safe_parse_list(s):
    try:
        parsed = ast.literal_eval(s)
        if isinstance(parsed, list):
            return parsed
        else:
            return []
    except Exception:
        return []


def format_solutions(solutions):
    if isinstance(solutions, np.ndarray):
        solutions_list = solutions.tolist()
    elif isinstance(solutions, str):
        solutions_list = [solutions]
    else:
        return ""
    return "\n".join(f"Solution {i+1}: {sol}" for i, sol in enumerate(solutions_list))


hf_df["informal_solution"] = hf_df["solutions"].apply(format_solutions)
hf_df["problem_number"] = hf_df["problem_label"]
hf_df["exam"] = "IMO"
hf_df["exam_category"] = [["International Contests"]] * len(hf_df)


def generate_uuid():
    return str(uuid.uuid4())


missing_mask = hf_df["uuid"].isnull()
new_uuids = [generate_uuid() for _ in range(missing_mask.sum())]
hf_df.loc[missing_mask, "uuid"] = new_uuidsmissing_mask = hf_df["uuid"].isnull()
new_uuids = [generate_uuid() for _ in range(missing_mask.sum())]
hf_df.loc[missing_mask, "uuid"] = new_uuids

hf_df = hf_df[
    [
        "uuid",
        "problem",
        "informal_solution",
        "exam",
        "exam_category",
        "year",
        "problem_number",
    ]
]
hf_df.to_parquet("/data/imo_data.parquet")

# python data_processing/infer_question_and_problem_type/infer_question_and_problem_type.py
imo_classified = pd.read_parquet("/data/imo_data_classified.parquet")
# python data_processing/run_pipeline.py --merged_data_file data/imo_data.parquet --final_data_file data/final_imo_data.parquet --filter_valid no
final_imo = pd.read_parquet("/data/final_imo_data.parquet")
# merge and concat everything back !
merged_df = final_imo.merge(imo_classified, on="uuid", how="left")

# Add to existing dedup data
dedup_data = pd.read_parquet("/data/dedup_data.parquet")
new_rows = merged_df[~merged_df["uuid"].isin(dedup_data["uuid"])]
dedup_data_updated = pd.concat([dedup_data, new_rows], ignore_index=True)
