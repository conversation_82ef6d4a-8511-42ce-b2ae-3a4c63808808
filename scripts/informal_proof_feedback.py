from loguru import logger

from mathlete.feedback.informal.informal_proof_feedback_models import (
    MathematicalAnalysisResult,
)
from mathlete.llms import llms
from mathlete.utils.constants import UserProofAttempt
from scripts.informal_proof_feedback_prompts import InformalProofFeedbackPromptGenerator


class InformalProofAnalyzer:
    def __init__(self):
        pass

    def analyze_informal_proof(
        self, attempt: UserProofAttempt
    ) -> MathematicalAnalysisResult:
        """Analyze only the mathematical reasoning of the informal proof"""

        prompt_generator = InformalProofFeedbackPromptGenerator()
        prompt = prompt_generator.make_prompt_informal_proof_feedback(attempt)

        try:
            return llms.execute_prompt(
                prompt=prompt, output_class=MathematicalAnalysisResult
            )

        except Exception as e:
            logger.error(f"Error analyzing mathematical proof: {str(e)}")
            raise RuntimeError(f"Mathematical proof analysis failed: {str(e)}")
