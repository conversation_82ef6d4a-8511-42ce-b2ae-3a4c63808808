from pathlib import Path

import pandas as pd


def analyze_test_results(test_data_path, merged_data_path, output_path=None):
    """
    Analyzes test results by merging with the main dataset and extracting relevant details.

    Parameters:
        test_data_path (str): Path to the test results CSV file.
        merged_data_path (str): Path to the main dataset Parquet file.
        output_path (str, optional): Path to save the processed results as a CSV.

    Returns:
        pd.DataFrame: The processed and merged dataframe for analysis.
    """
    dtest = pd.read_csv(test_data_path)
    df = pd.read_parquet(merged_data_path)

    dtest["results"] = dtest["results"].apply(eval)

    # Merge test data with main dataset
    dtest = dtest.merge(df, on="uuid", how="left")[
        ["uuid", "query", "results", "problem_position", "problem", "informal_solution"]
    ]

    # Explode 'results' column to have one result per row
    exploded_df = dtest.explode("results")

    # Merge exploded results with main dataset for additional details
    joined = exploded_df.merge(df, left_on="results", right_on="uuid", how="left")

    joined.rename(
        columns={
            "uuid_x": "uuid",
            "uuid_y": "results_uuid",
            "problem_y": "results_problem",
            "informal_solution_y": "results_informal_solution",
            "problem_x": "problem",
            "informal_solution_x": "informal_solution",
        },
        inplace=True,
    )

    final_df = joined[
        [
            "uuid",
            "problem_position",
            "problem",
            "informal_solution",
            "query",
            "results_problem",
            "results_informal_solution",
            "results_uuid",
        ]
    ]

    if output_path:
        final_df.to_csv(output_path, index=False)
        print(f"Analysis results saved to {output_path}")

    return final_df


if __name__ == "__main__":
    repo_root = Path(__file__).resolve().parent.parent

    test_data_path = repo_root / "mathlete" / "tests" / "data" / "search_results.csv"
    merged_data_path = repo_root / "mathlete" / "data" / "merged_dataset.parquet"
    output_path = repo_root / "mathlete" / "analysis" / "test_results_analysis.csv"

    df_analysis = analyze_test_results(test_data_path, merged_data_path, output_path)
