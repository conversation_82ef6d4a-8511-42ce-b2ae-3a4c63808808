import asyncio
import json
import logging
import os
import textwrap
import uuid
from typing import Async<PERSON><PERSON>ator, Dict, List, Literal, Optional

from fastapi import APIRouter, File, HTTPException, Query, UploadFile, status
from fastapi.responses import StreamingResponse
from models import (
    AttemptCheckRequest,
    AttemptCheckResult,
    BugReportBody,
    EventWithProblems,
    MathematicalAnalysisResult,
    Problem,
    ProofAnalysisRequest,
    QuickPick,
    RefinementRequest,
)
from openai import AsyncOpenAI
from prisma.types import problemsWhereInput
from prisma_client import prisma
from prompts.analyze_proof import ANALYZE_SYSTEM_PROMPT
from prompts.attempt_check import ATTEMPT_CHECK_SYSTEM_PROMPT
from together import AsyncTogether
from utils.difficulty import (
    DifficultyBias,
    calculate_difficulty_distribution,
    difficulty_bias_to_difficulty_level,
)
from utils.ocr import OpenaiOCR
from utils.search import KeywordSearch, SemanticSearch

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s %(name)s [%(filename)s:%(lineno)d] %(levelname)s: %(message)s",
)

router = APIRouter(prefix="/api")
logger = logging.getLogger(__name__)


@router.get("/", response_model=dict)
async def root():
    """
    Root endpoint for the Math Problems API.
    """
    return {"message": "Math Problems API is running"}


async def get_problems_by_difficulty_bias(
    skip: int,
    limit: int,
    query_where: problemsWhereInput,
    difficulty_bias: DifficultyBias,
):
    all_problems = []
    difficulty_distribution = calculate_difficulty_distribution(
        limit, difficulty_bias, skip
    )

    remaining_limit = 0
    accumulated_skip_deficit = 0

    all_problem_levels_total = {}

    # query all difficulty levels
    for current_bias, count, initial_skip in difficulty_distribution:
        query_where["difficulty_level"] = difficulty_bias_to_difficulty_level[
            current_bias
        ]

        problem_total = await prisma.problems.count(where=query_where)
        all_problem_levels_total[current_bias] = problem_total

    difficulty_distribution.sort(
        key=lambda x: (
            x[2] + x[1] > all_problem_levels_total[x[0]],
            x[0],
        ),
        reverse=True,
    )

    for current_bias, count, initial_skip in difficulty_distribution:
        if len(all_problems) >= limit:
            break

        query_where["difficulty_level"] = difficulty_bias_to_difficulty_level[
            current_bias
        ]

        effective_skip = initial_skip + accumulated_skip_deficit

        problems = await prisma.problems.find_many(
            where=query_where,
            skip=effective_skip,
            take=count + remaining_limit,
            order={"id": "asc"},
        )

        if len(problems) != count:
            problem_total = all_problem_levels_total[current_bias]

            if effective_skip > problem_total:
                accumulated_skip_deficit = effective_skip - problem_total
            else:
                accumulated_skip_deficit = 0

        if len(problems) <= count:
            remaining_limit += count - len(problems)
        else:
            remaining_limit -= len(problems) - count

        all_problems.extend(problems)

    return all_problems


@router.get("/problems", response_model=dict, description="Search for math problems")
async def get_problems(
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    query: Optional[str] = Query(None),
    mode: Literal["keyword", "semantic"] = Query(default="keyword"),
    difficulty_bias: Optional[DifficultyBias] = Query(default=None),
):
    """
    Get a list of math problems with optional filtering.

    Parameters:
    - `skip`: The number of problems to skip.
    - `limit`: The number of problems to return.
    - `query`: The query to search for.
    - `mode`: The mode to use for searching.
    - `difficulty_bias`: The difficulty bias to use for searching.
    """
    if not query:
        raise HTTPException(status_code=400, detail="Query is required")

    searcher = None

    if mode == "keyword":
        searcher = KeywordSearch(query=query)
    else:
        searcher = SemanticSearch(query=query)

    query_decomposition = searcher.query_decomposition
    filters = query_decomposition.filters

    where: problemsWhereInput = {}

    if problem_type := filters.get("problem_type", None):
        where["domain"] = problem_type

    if difficulty := filters.get("difficulty", None) and not difficulty_bias:
        where["difficulty_level"] = {
            "gte": difficulty[0],
            "lte": difficulty[1],
        }

    if exam_category := filters.get("exam_category", None):
        where["exam"] = exam_category

    try:
        total_problems = await prisma.problems.count(where=where)

        rec_where = where.copy()
        if difficulty_bias:
            recommended_problems = await get_problems_by_difficulty_bias(
                skip=skip,
                limit=4,
                query_where=rec_where,
                difficulty_bias=difficulty_bias,
            )
        else:
            recommended_problems = await prisma.problems.find_many(
                where=rec_where,
                skip=skip,
                take=4,
                order={"id": "asc"},
            )

        recommended_problems_ids = [p.id for p in recommended_problems]

        problems = await prisma.problems.find_many(
            where=where,
            skip=skip,
            take=limit,
            order={"id": "asc"},
        )

        problems.sort(key=lambda p: (p.difficulty_level, p.id))

        return {
            "metadata_matches": {
                "problem_type": problem_type,
                "difficulty": difficulty,
                "exam_category": exam_category,
            },
            "skip": skip,
            "limit": limit,
            "total": total_problems,
            "valid_query": query_decomposition.valid_query,
            "reason": query_decomposition.reason,
            "recommended_problems_ids": recommended_problems_ids,
            "problem_matches": problems,
        }

    except Exception:
        logger.exception("Error retrieving problems")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/problems/{problem_id}", response_model=Problem)
async def get_problem_by_id(problem_id: int):
    """
    Get a math problem by ID.
    """
    try:
        problem = await prisma.problems.find_unique(where={"id": problem_id})
        if not problem:
            raise HTTPException(status_code=404, detail="Problem not found")
        return problem
    except Exception as e:
        logger.error("Error retrieving problem: %s", e)
        raise HTTPException(status_code=500, detail=str(e)) from e


@router.get("/problems/uuid/{uuid}", response_model=Problem)
async def get_problem_by_uuid(uuid: str):
    """
    Get a math problem by UUID.
    """
    try:
        problem = await prisma.problems.find_unique(where={"uuid": uuid})
        if not problem:
            raise HTTPException(status_code=404, detail="Problem not found")
        return problem
    except Exception as e:
        logger.error("Error retrieving problem: %s", e)
        raise HTTPException(status_code=500, detail=str(e)) from e


@router.get("/quick-picks", response_model=List[QuickPick])
async def list_quick_picks():
    """
    List the first 3 quick picks
    """
    try:
        quick_picks = await prisma.quick_picks.find_many(
            where={"enabled": True},
            include={"event": True},
            take=3,
            order={"updated_at": "desc"},
        )
        return quick_picks
    except Exception as e:
        logger.error("Error retrieving quick picks: %s", e)
        raise HTTPException(status_code=500, detail=str(e)) from e


@router.get("/events/{slug}", response_model=EventWithProblems)
async def get_event_by_slug(slug: str):
    """
    Get an event by its URL slug
    """
    try:
        event = await prisma.events.find_unique(
            where={"slug": slug},
            include={
                "problems": {
                    "include": {
                        "problem": True,
                    }
                }
            },
        )
        if not event:
            raise HTTPException(status_code=404, detail="Event not found")
        return event
    except Exception as e:
        logger.error("Error retrieving event: %s", e)
        raise HTTPException(status_code=500, detail=str(e)) from e


ocr_sessions: Dict[str, asyncio.Queue] = {}


@router.get("/sse/ocr-mobile")
async def ocr_sse():
    session_id = str(uuid.uuid4())
    ocr_sessions[session_id] = asyncio.Queue()

    async def event_generator():
        try:
            # First connection to sent session id
            yield f"data: {session_id}\n\n"

            queue = ocr_sessions[session_id]
            while True:
                data = await queue.get()
                yield f"data: {data}\n\n"
        except asyncio.CancelledError:
            logger.debug(f"SSE connection closed: {session_id}")
        finally:
            # Make sure to delete queue from sessions
            ocr_sessions.pop(session_id, None)

    return StreamingResponse(event_generator(), media_type="text/event-stream")


@router.post("/ocr-upload")
async def upload_ocr_images(
    session_id: Optional[str] = Query(None), images: List[UploadFile] = File(...)
):
    if (session_id is not None) and (session_id not in ocr_sessions):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid session ID",
        )

    if not all(img.content_type.startswith("image/") for img in images):
        raise HTTPException(
            status_code=status.HTTP_415_UNSUPPORTED_MEDIA_TYPE,
            detail="Uploaded file must be an image",
        )

    if len(images) > 10:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Too many images uploaded, maximum is 10",
        )

    ocr_client = OpenaiOCR()
    image_contents = await asyncio.gather(*(img.read() for img in images))

    async def process_image(content):
        try:
            result = (await ocr_client.extract(content)).text
        except Exception as e:
            result = f"Error extracting text from image: {e}"
            logger.error(result)
        return result

    # Concurrent processing OCR
    results = await asyncio.gather(
        *(process_image(content) for content in image_contents)
    )

    # Push the result to the queue
    if session_id is not None:
        for result in results:
            await ocr_sessions[session_id].put(result)

    return {"results": results}


@router.post("/bug-report")
async def log_bug_report(body: BugReportBody):
    await prisma.bug_report.create(
        data={
            "statement": body.statement,
            "feedback": body.feedback,
            "problem": {
                "connect": {"uuid": body.problem_uuid},
            },
        }
    )
    return {"message": "Bug report logged"}


@router.post("/check-proof-attempt", response_model=AttemptCheckResult)
async def check_proof_attempt(request: AttemptCheckRequest):
    print(request)
    try:
        user_message = textwrap.dedent(
            f"""###Problem Statement###:
            {request.attempt.problem_statement}

            ###User Proof###:
            {request.attempt.proof_text}
            """
        )
        print(user_message)

        oai_client = AsyncOpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        response = await oai_client.chat.completions.create(
            messages=[
                {"role": "system", "content": ATTEMPT_CHECK_SYSTEM_PROMPT},
                {"role": "user", "content": user_message},
            ],
            model="gpt-4.1-mini-2025-04-14",
            response_format={"type": "json_object"},
        )
        print(response)

        return AttemptCheckResult.model_validate_json(
            response.choices[0].message.content
        )
    except Exception as e:
        logger.error(f"Error in proof attempt check: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Attempt check failed: {str(e)}")


@router.post("/analyze-proof")
async def analyze_proof(request: ProofAnalysisRequest):
    """
    First endpoint: Stream only the thinking process from Deepseek
    """

    async def thinking_generator() -> AsyncGenerator[str, None]:
        try:
            system_message = ANALYZE_SYSTEM_PROMPT
            user_message = textwrap.dedent(
                f"""###Problem Statement###:
                {request.attempt.problem_statement}

                ###User Proof###:
                {request.attempt.proof_text}
                """
            )

            thinking_complete = False
            result_content = ""
            client = AsyncTogether(api_key=os.getenv("TOGETHER_API_KEY"))
            stream = await client.chat.completions.create(
                model="deepseek-ai/DeepSeek-R1",
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": user_message},
                ],
                temperature=0.6,
                top_p=0.95,
                max_tokens=32_768,
                stream=True,
            )
            async for chunk in stream:
                if chunk.choices[0].delta.content:
                    if "<think>" in chunk.choices[0].delta.content or (
                        not thinking_complete
                        and "</think>" not in chunk.choices[0].delta.content
                    ):
                        yield f"data: {json.dumps({'type': 'thinking', 'content': chunk.choices[0].delta.content})}\n\n"
                    elif "</think>" in chunk.choices[0].delta.content:
                        yield f"data: {json.dumps({'type': 'thinking', 'content': chunk.choices[0].delta.content})}\n\n"
                        yield f"data: {json.dumps({'type': 'thinking_complete'})}\n\n"
                        thinking_complete = True
                    else:
                        result_content += chunk.choices[0].delta.content

            yield f"data: {json.dumps({'type': 'result', 'content': result_content})}\n\n"

        except Exception as e:
            logger.error(f"Error in thinking analysis: {str(e)}")
            yield f"data: {json.dumps({'type': 'error', 'message': str(e)})}\n\n"

    return StreamingResponse(
        thinking_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
        },
    )


@router.post("/refine-proof", response_model=MathematicalAnalysisResult)
async def analyze_proof_refine(request: RefinementRequest):
    """
    Second endpoint: Process the initial analysis and optionally refine it
    """
    try:
        initial_result = MathematicalAnalysisResult.model_validate_json(
            request.initial_analysis
        )

        if len(initial_result.mathematical_errors) > 0:
            logger.info("Refining Extraction of Proof Analysis...")
            user_message = """Original Proof Tentative:
                {request.attempt.proof_text}

                Current Extraction:
                {initial_result}

                Validate and correct the extraction, ensuring:
                1. Exact verbatim proof location
                2. Precise error classification
                3. Mathematically rigorous explanation
                """

            oai_client = AsyncOpenAI(api_key=os.getenv("OPENAI_API_KEY"))
            response = await oai_client.chat.completions.create(
                messages=[
                    {"role": "system", "content": ANALYZE_SYSTEM_PROMPT},
                    {"role": "user", "content": user_message},
                ],
                model="gpt-4o-2024-08-06",
            )
            return response.choices[0].message.content

        else:
            return initial_result

    except Exception as e:
        logger.error(f"Error in refinement: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Refinement failed: {str(e)}")
