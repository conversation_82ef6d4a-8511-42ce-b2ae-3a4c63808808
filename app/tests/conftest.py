"""Shared test configuration and fixtures"""

from fastapi.testclient import TestClient
import pytest
import pytest_asyncio
import os
import asyncio
from prisma import Prisma

import main


@pytest.fixture(scope="session", autouse=True)
def setup_test_environment():
    """Setup test environment before running any tests"""
    # Ensure we're in the right directory for database operations
    original_dir = os.getcwd()

    try:
        # Change to app directory if needed
        if not os.path.exists("prisma") and os.path.exists("app"):
            os.chdir("app")

        # Only setup database for integration tests
        # Unit tests don't need database
        yield

    finally:
        # Restore original directory
        os.chdir(original_dir)


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    policy = asyncio.get_event_loop_policy()
    loop = policy.new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture(scope="function")
async def prisma_client():
    """Create and connect a separate Prisma client for tests"""
    client = Prisma()
    await client.connect()
    yield client
    await client.disconnect()


@pytest.fixture(scope="function")
def test_client():
    """Create test client with database connection"""
    # Use the TestClient directly with the app
    with TestClient(main.app) as client:
        yield client
