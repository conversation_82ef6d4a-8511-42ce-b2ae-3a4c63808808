import pytest

from utils.difficulty import calculate_difficulty_distribution, DifficultyBias


class TestDifficultyCalculation:
    """Test difficulty calculation functions"""

    def test_calculate_difficulty_distribution_easy_bias_basic(self):
        """Test calculate_difficulty_distribution with easy bias and basic inputs"""
        # Test case: limit=4, difficulty_bias=EASY, skip=0
        # Pattern: [EASY, EASY, MEDIUM, HARD]
        result = calculate_difficulty_distribution(4, DifficultyBias.EASY, 0)

        expected = [
            (DifficultyBias.EASY, 2, 0),  # Gets 2 EASY (positions 0,1), skips 0
            (DifficultyBias.MEDIUM, 1, 0),  # Gets 1 MEDIUM (position 2), skips 0
            (DifficultyBias.HARD, 1, 0),  # Gets 1 HARD (position 3), skips 0
        ]

        assert result == expected

    def test_calculate_difficulty_distribution_easy_bias_with_skip(self):
        """Test calculate_difficulty_distribution with easy bias and skip"""
        # Test case: limit=4, difficulty_bias=EASY, skip=4
        # Pattern: [EASY, EASY, MEDIUM, HARD] repeating
        # Skip first 4: EASY(0), EASY(1), MEDIUM(2), HARD(3)
        # Take next 4: EASY(4), EASY(5), MEDIUM(6), HARD(7)
        result = calculate_difficulty_distribution(4, DifficultyBias.EASY, 4)

        expected = [
            (DifficultyBias.EASY, 2, 2),  # Takes 2, skipped 2
            (DifficultyBias.MEDIUM, 1, 1),  # Takes 1, skipped 1
            (DifficultyBias.HARD, 1, 1),  # Takes 1, skipped 1
        ]

        assert result == expected

    def test_calculate_difficulty_distribution_medium_bias(self):
        """Test calculate_difficulty_distribution with medium bias"""
        # Test case: limit=4, difficulty_bias=MEDIUM, skip=0
        # Pattern: [EASY, MEDIUM, MEDIUM, HARD]
        result = calculate_difficulty_distribution(4, DifficultyBias.MEDIUM, 0)

        expected = [
            (DifficultyBias.EASY, 1, 0),  # Gets 1 EASY (position 0)
            (DifficultyBias.MEDIUM, 2, 0),  # Gets 2 MEDIUM (positions 1,2)
            (DifficultyBias.HARD, 1, 0),  # Gets 1 HARD (position 3)
        ]

        assert result == expected

    def test_calculate_difficulty_distribution_hard_bias(self):
        """Test calculate_difficulty_distribution with hard bias"""
        # Test case: limit=4, difficulty_bias=HARD, skip=0
        # Pattern: [EASY, MEDIUM, HARD, HARD]
        result = calculate_difficulty_distribution(4, DifficultyBias.HARD, 0)

        expected = [
            (DifficultyBias.EASY, 1, 0),  # Gets 1 EASY (position 0)
            (DifficultyBias.MEDIUM, 1, 0),  # Gets 1 MEDIUM (position 1)
            (DifficultyBias.HARD, 2, 0),  # Gets 2 HARD (positions 2,3)
        ]

        assert result == expected

    def test_calculate_difficulty_distribution_large_skip(self):
        """Test calculate_difficulty_distribution with large skip that cycles through pattern"""
        # Test case: limit=4, difficulty_bias=EASY, skip=10
        # Pattern: [EASY, EASY, MEDIUM, HARD] (length 4)
        # Skip 10: 2 full cycles + 2 more = 2*2 EASY + 2*1 MEDIUM + 2*1 HARD + 1 EASY + 1 EASY
        # Total skipped: 6 EASY, 2 MEDIUM, 2 HARD
        result = calculate_difficulty_distribution(4, DifficultyBias.EASY, 10)

        expected = [
            (DifficultyBias.EASY, 2, 6),  # Takes 2 more EASY
            (DifficultyBias.MEDIUM, 1, 2),  # Takes 1 more MEDIUM
            (DifficultyBias.HARD, 1, 2),  # Takes 1 more HARD
        ]

        assert result == expected

    def test_calculate_difficulty_distribution_zero_limit(self):
        """Test calculate_difficulty_distribution with zero limit"""
        result = calculate_difficulty_distribution(0, DifficultyBias.EASY, 0)

        expected = [
            (DifficultyBias.EASY, 0, 0),
            (DifficultyBias.MEDIUM, 0, 0),
            (DifficultyBias.HARD, 0, 0),
        ]

        assert result == expected

    def test_calculate_difficulty_distribution_large_limit(self):
        """Test calculate_difficulty_distribution with large limit"""
        # Test case: limit=12, difficulty_bias=EASY, skip=0
        # Pattern: [EASY, EASY, MEDIUM, HARD] repeating 3 times
        result = calculate_difficulty_distribution(12, DifficultyBias.EASY, 0)

        expected = [
            (DifficultyBias.EASY, 6, 0),  # 2 * 3 = 6 EASY problems
            (DifficultyBias.MEDIUM, 3, 0),  # 1 * 3 = 3 MEDIUM problems
            (DifficultyBias.HARD, 3, 0),  # 1 * 3 = 3 HARD problems
        ]

        assert result == expected

    def test_calculate_difficulty_distribution_uneven_limit(self):
        """Test calculate_difficulty_distribution with limit that doesn't divide evenly"""
        # Test case: limit=5, difficulty_bias=EASY, skip=0
        # Pattern: [EASY, EASY, MEDIUM, HARD, EASY] (first 5 elements)
        result = calculate_difficulty_distribution(5, DifficultyBias.EASY, 0)

        expected = [
            (DifficultyBias.EASY, 3, 0),  # Positions 0, 1, 4
            (DifficultyBias.MEDIUM, 1, 0),  # Position 2
            (DifficultyBias.HARD, 1, 0),  # Position 3
        ]

        assert result == expected

    def test_calculate_difficulty_distribution_skip_without_limit(self):
        """Test calculate_difficulty_distribution with skip but zero limit"""
        # Test case: limit=0, difficulty_bias=EASY, skip=6
        # Should skip 6 but take 0
        result = calculate_difficulty_distribution(0, DifficultyBias.EASY, 6)

        expected = [
            (DifficultyBias.EASY, 0, 4),  # Skipped 4 EASY (positions 0,1,4,5)
            (DifficultyBias.MEDIUM, 0, 1),  # Skipped 1 MEDIUM (position 2)
            (DifficultyBias.HARD, 0, 1),  # Skipped 1 HARD (position 3)
        ]

        assert result == expected

    @pytest.mark.parametrize(
        "bias,expected_ratios",
        [
            (DifficultyBias.EASY, (2, 1, 1)),
            (DifficultyBias.MEDIUM, (1, 2, 1)),
            (DifficultyBias.HARD, (1, 1, 2)),
        ],
    )
    def test_calculate_difficulty_distribution_ratios(self, bias, expected_ratios):
        """Test that different bias values produce correct ratios"""
        limit = 8  # Use multiple of 4 for clean division
        result = calculate_difficulty_distribution(limit, bias, 0)

        r_e, r_m, r_h = expected_ratios
        total_ratio = r_e + r_m + r_h
        multiplier = limit // total_ratio

        expected = [
            (DifficultyBias.EASY, r_e * multiplier, 0),
            (DifficultyBias.MEDIUM, r_m * multiplier, 0),
            (DifficultyBias.HARD, r_h * multiplier, 0),
        ]

        assert result == expected
