class TestAPI:
    def test_specific_problem_endpoint(self, test_client):
        """Test API endpoint for specific problem - expects 200"""
        problem_uuid = "73fa1073-4367-51e5-8103-d2c91f075761"
        response = test_client.get(f"/api/problems/uuid/{problem_uuid}")
        print(response.json())
        assert response.status_code == 200, f"Expected 200, got {response.status_code}"
        print("Specific problem endpoint returned 200 OK")

    def test_empty_query_endpoint(self, test_client):
        """Test API endpoint with empty query - expects 400"""
        response = test_client.get("/api/problems", params={"query": ""})
        assert response.status_code == 400, f"Expected 400, got {response.status_code}"
        print("Empty query test returned 400 Bad Request as expected")

    def test_valid_query_endpoint(self, test_client):
        """Test API endpoint with valid query - expects 200 and checks total"""
        response = test_client.get("/api/problems", params={"query": "inequalities"})
        assert response.status_code == 200, f"Expected 200, got {response.status_code}"
        print("Valid query test returned 200 OK as expected")

        response_data = response.json()
        actual_total = response_data.get("total")
        expected_total = 1351

        print(f"Actual total: {actual_total}")
        print(f"Expected total: {expected_total}")

        assert (
            actual_total == expected_total
        ), f"Total count is {actual_total}, but expected {expected_total}"
        print(f"Total count is correct: {actual_total}")

    def test_query_with_skip_and_limit_consecutive_overlap(self, test_client):
        """
        Test API endpoint with query, limit, and skip parameters.
        Verifies that consecutive skip results (skip N vs skip N+1) have
        at least 3 common IDs, assuming unordered results.

        In our logic, we construct a queue based on **limit** and **difficulty_bias**.

        If `limit = 4` and `difficulty_bias = easy`, the queue will look like this, with rounds of 4 items.
            Skipping will then proceed sequentially down this queue:

        [
         {id: 1, difficulty_level: 1}
         {id: 2, difficulty_level: 2}
         {id: 3, difficulty_level: 6}
         {id: 4, difficulty_level: 8}
         --- 1 round
         {id: 5, difficulty_level: 1}
         {id: 6, difficulty_level: 1}
         {id: 7, difficulty_level: 5}
         {id: 8, difficulty_level: 9}
          --- 2 round
         {id: 9, difficulty_level: 2}
         {id: 10, difficulty_level: 1}
         {id: 11, difficulty_level: 5}
         ...
        ]
        """
        query = "inequalities"
        limit = 4  # The limit is 4, so for '3 common IDs', it means 3 out of 4 should overlap.
        min_common_ids = limit - 1

        previous_problem_ids = None

        for skip_val in range(0, 11):
            print(f"\nTesting with query='{query}', limit={limit}, skip={skip_val}")
            response = test_client.get(
                "/api/problems",
                params={
                    "query": query,
                    "limit": limit,
                    "skip": skip_val,
                    "difficulty_bias": "easy",
                },
            )
            assert (
                response.status_code == 200
            ), f"Expected 200 for skip={skip_val}, got {response.status_code}"

            current_problems_data = response.json().get("problem_matches", [])
            assert (
                len(current_problems_data) == limit
            ), f"Expected {limit} problems for skip={skip_val}, got {len(current_problems_data)}"

            current_problem_ids = {p["id"] for p in current_problems_data if "id" in p}

            if previous_problem_ids:
                # Calculate the intersection of the current and previous sets of IDs
                common_ids = previous_problem_ids.intersection(current_problem_ids)
                num_common_ids = len(common_ids)

                print(
                    f"  Common IDs between skip={skip_val-1} and skip={skip_val}: {num_common_ids}"
                )
                print(f"  Previous IDs: {sorted(list(previous_problem_ids))}")
                print(f"  Current IDs:  {sorted(list(current_problem_ids))}")
                print(f"  Common IDs found: {sorted(list(common_ids))}")

                assert num_common_ids == min_common_ids, (
                    f"Expected at least {min_common_ids} common IDs between skip={skip_val-1} and skip={skip_val}, "
                    f"but found {num_common_ids}.\n"
                    f"  Previous IDs: {sorted(list(previous_problem_ids))}\n"
                    f"  Current IDs:  {sorted(list(current_problem_ids))}\n"
                    f"  Common IDs:   {sorted(list(common_ids))}"
                )
                print(
                    f"Skip {skip_val} comparison passed: at least {min_common_ids} common IDs found."
                )

            # Store current IDs for the next iteration's comparison
            previous_problem_ids = current_problem_ids

        print("\nAll consecutive skip overlap tests completed successfully.")

    def test_query_pagination_beyond_total(self, test_client):
        """
        Test API endpoint with query, limit, and skip parameters where (limit + skip) >= total.
        Verifies that:
        1. The 'total' field in the response remains consistent with the actual total.
        2. The 'problem_matches' count is correct based on (total - skip) or 0.
        """
        query = "inequalities"
        difficulty_bias = "easy"
        base_limit = 4  # Use a small limit for clarity

        print(
            f"\n--- Testing pagination beyond total with query='{query}', difficulty_bias='{difficulty_bias}' ---"
        )

        # Step 1: Get the actual total count for the given parameters
        # Use a very large limit to ensure we get the full total
        response_total_fetch = test_client.get(
            "/api/problems",
            params={
                "query": query,
                "limit": 1,  # Sufficiently large to get the true total
                "skip": 0,
                "difficulty_bias": difficulty_bias,
            },
        )
        assert (
            response_total_fetch.status_code == 200
        ), f"Expected 200 for total fetch, got {response_total_fetch.status_code}"

        actual_total = response_total_fetch.json().get("total")
        assert (
            actual_total is not None and actual_total >= 0
        ), f"Total count not found or invalid: {actual_total}"
        print(
            f"Initial fetch: Actual total problems for '{query}' with '{difficulty_bias}': {actual_total}"
        )

        # Scenario 1: skip + limit > total, but skip < total
        # We want to request more items than are left after skipping
        test_skip_1 = actual_total - (
            base_limit - 1
        )  # This leaves (base_limit - 1) items if total is large enough
        if test_skip_1 < 0:  # Handle case where actual_total is very small
            test_skip_1 = 0
        test_limit_1 = base_limit  # Request 'base_limit' items

        # Expected number of problems to be returned in this scenario
        expected_returned_count_1 = max(0, actual_total - test_skip_1)
        # Ensure we are testing the edge case where returned count is less than limit
        if (
            actual_total > 0 and test_skip_1 < actual_total
        ):  # Only meaningful if there are problems to begin with and skip isn't past total
            assert (
                expected_returned_count_1 < test_limit_1
            ), "Calculation for test_skip_1 did not result in expected returned count < limit. Adjust test_skip_1."
        else:  # For very small totals, it might be 0, so adjust
            expected_returned_count_1 = 0

        print(
            f"\n--- Scenario 1: skip={test_skip_1}, limit={test_limit_1} (skip < total, but skip+limit > total) ---"
        )
        print(f"  Expected returned problems: {expected_returned_count_1}")

        response_scenario_1 = test_client.get(
            "/api/problems",
            params={
                "query": query,
                "limit": test_limit_1,
                "skip": test_skip_1,
                "difficulty_bias": difficulty_bias,
            },
        )
        assert (
            response_scenario_1.status_code == 200
        ), f"Expected 200 for Scenario 1, got {response_scenario_1.status_code}"

        response_data_1 = response_scenario_1.json()
        returned_total_1 = response_data_1.get("total")
        returned_problems_1 = response_data_1.get("problem_matches", [])

        assert (
            returned_total_1 == actual_total
        ), f"Scenario 1: Total mismatch. Expected {actual_total}, got {returned_total_1}"
        assert (
            len(returned_problems_1) == expected_returned_count_1
        ), f"Scenario 1: Problem count mismatch. Expected {expected_returned_count_1}, got {len(returned_problems_1)}"
        print(
            "  Scenario 1 Passed: Total is correct and problem count matches (total - skip)."
        )

        # Scenario 2: skip >= total (requesting problems entirely past the total)
        test_skip_2 = actual_total  # Comfortably beyond the total
        test_limit_2 = base_limit  # Request 'base_limit' items
        expected_returned_count_2 = (
            0  # When skip >= total, no problems should be returned
        )

        print(
            f"\n--- Scenario 2: skip={test_skip_2}, limit={test_limit_2} (skip >= total) ---"
        )
        print(f"  Expected returned problems: {expected_returned_count_2}")

        response_scenario_2 = test_client.get(
            "/api/problems",
            params={
                "query": query,
                "limit": test_limit_2,
                "skip": test_skip_2,
                "difficulty_bias": difficulty_bias,
            },
        )
        assert (
            response_scenario_2.status_code == 200
        ), f"Expected 200 for Scenario 2, got {response_scenario_2.status_code}"

        response_data_2 = response_scenario_2.json()
        returned_total_2 = response_data_2.get("total")
        returned_problems_2 = response_data_2.get("problem_matches", [])

        assert (
            returned_total_2 == actual_total
        ), f"Scenario 2: Total mismatch. Expected {actual_total}, got {returned_total_2}"
        assert (
            len(returned_problems_2) == expected_returned_count_2
        ), f"Scenario 2: Problem count mismatch. Expected {expected_returned_count_2}, got {len(returned_problems_2)}"

        print("\nAll pagination beyond total tests completed successfully.")
