import pytest


class TestAPIWithRealDatabase:
    """Tests that actually connect to the database"""

    @pytest.mark.asyncio
    async def test_database_connection(self, prisma_client):
        """Test that we can connect to the database"""
        assert prisma_client.is_connected()

        # Test a simple query
        try:
            problems = await prisma_client.problems.find_many(take=1)
            assert isinstance(problems, list)
            print(f"Found {len(problems)} problems in database")
        except Exception as e:
            pytest.fail(f"Database query failed: {e}")

    @pytest.mark.asyncio
    async def test_specific_problem_exists(self, prisma_client):
        """Test that the specific problem exists in database"""
        problem_uuid = "73fa1073-4367-51e5-8103-d2c91f075761"

        problem = await prisma_client.problems.find_unique(where={"uuid": problem_uuid})

        assert problem is not None, f"Problem with UUID {problem_uuid} not found"
        print(
            f"Found problem: {problem.title if hasattr(problem, 'title') else 'Unknown title'}"
        )

    @pytest.mark.asyncio
    async def test_inequalities_query(self, prisma_client):
        """Test the inequalities query returns expected results"""
        # This is a simplified version of the search logic
        problems = await prisma_client.problems.find_many(
            where={
                "OR": [{"title": {"contains": "inequalities", "mode": "insensitive"}}]
            }
        )

        assert len(problems) > 0, "No problems found for 'inequalities' query"
        print(f"Found {len(problems)} problems matching 'inequalities'")
