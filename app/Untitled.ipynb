{"cells": [{"cell_type": "code", "execution_count": 7, "id": "e97b6e58-460c-4b95-8d78-736db820ab84", "metadata": {}, "outputs": [], "source": ["import requests\n", "url = \"https://openrouter.ai/api/v1/models\"\n", "response = requests.get(url)"]}, {"cell_type": "code", "execution_count": 29, "id": "4aaa15d0-de9e-4055-8542-a754997c0cc4", "metadata": {}, "outputs": [], "source": ["from openai import AsyncOpenAI\n", "openai_client = AsyncOpenAI(\n", "  base_url=\"https://openrouter.ai/api/v1\",\n", "  api_key=\"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\",\n", ")\n", "stream = await openai_client.chat.completions.create(\n", "    model=\"deepseek/deepseek-r1:free\",\n", "    stream=True,\n", "    messages=[\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"What is 1 + 1 ? \"\n", "        }\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": 30, "id": "4a7d6cd2-92bb-4729-8901-37e8a05219e0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Okay\n", "\n", ", so I need\n", "\n", " to find\n", "\n", " out\n", "\n", " what \n", "\n", "1 plus\n", "\n", " 1 is\n", "\n", ". Let\n", "\n", " me think\n", "\n", ". H\n", "\n", "mm, addition\n", "\n", " of\n", "\n", " two numbers. Basic\n", "\n", " arithmetic\n", "\n", ", right? Starting\n", "\n", " from the number\n", "\n", " 1,\n", "\n", " if I add another\n", "\n", " \n", "\n", "1,\n", "\n", " how\n", "\n", " much\n", "\n", " do\n", "\n", " I have? Let\n", "\n", " me\n", "\n", " count\n", "\n", " on my\n", "\n", " fingers. Hold\n", "\n", " up\n", "\n", " one finger on\n", "\n", " my\n", "\n", " left hand—\n", "\n", "that's \n", "\n", "1. Then\n", "\n", " put\n", "\n", " up another finger on\n", "\n", " my right\n", "\n", " hand—\n", "\n", "that's also\n", "\n", " 1. If\n", "\n", " I put\n", "\n", " them together, how\n", "\n", " many fingers do\n", "\n", " I have up\n", "\n", "? Let\n", "\n", "'s\n", "\n", " see:\n", "\n", " one, two\n", "\n", ". Oh\n", "\n", ", so\n", "\n", " maybe\n", "\n", " it\n", "\n", "'s \n", "\n", "2?\n", "\n", " \n", "\n", "\n", "\n", "Wait, but maybe\n", "\n", " there\n", "\n", "'s a\n", "\n", " trick question\n", "\n", " here. Sometimes\n", "\n", " people\n", "\n", " ask simple\n", "\n", " math\n", "\n", " questions to test\n", "\n", " if you're paying\n", "\n", " attention. Like\n", "\n", ", maybe\n", "\n", " in a\n", "\n", " different base\n", "\n", " system? Normally\n", "\n", ", we\n", "\n", " use base\n", "\n", " 10.\n", "\n", " If\n", "\n", " it\n", "\n", "'s base\n", "\n", " 2\n", "\n", ", which is binary\n", "\n", ", then\n", "\n", " 1 +\n", "\n", " 1 would\n", "\n", " be \n", "\n", "10.\n", "\n", " But the\n", "\n", " problem\n", "\n", " doesn't specify\n", "\n", " the\n", "\n", " base,\n", "\n", " so I\n", "\n", " probably\n", "\n", " should assume\n", "\n", " base\n", "\n", " 10.\n", "\n", "\n", "\n", "Alternatively, maybe\n", "\n", " there\n", "\n", "'s some\n", "\n", " other context.\n", "\n", " In\n", "\n", " Boolean\n", "\n", " algebra\n", "\n", ", \n", "\n", "1 +\n", "\n", " 1 is\n", "\n", " 1 because\n", "\n", " it\n", "\n", " represents\n", "\n", " logical OR,\n", "\n", " but that\n", "\n", "'s probably not\n", "\n", " what\n", "\n", " they're asking here\n", "\n", ". Since\n", "\n", " the\n", "\n", " question isn\n", "\n", "'t specifying\n", "\n", " any different\n", "\n", " contexts\n", "\n", ", I\n", "\n", " think\n", "\n", " the answer is\n", "\n", " straightforward.\n", "\n", "\n", "\n", "So\n", "\n", ", verifying\n", "\n", " again\n", "\n", ": One\n", "\n", " plus\n", "\n", " one. If\n", "\n", " I have one\n", "\n", " apple and\n", "\n", " someone gives\n", "\n", " me another apple\n", "\n", ", how many apples\n", "\n", " do I have?\n", "\n", " Two apples. Yes\n", "\n", ", that\n", "\n", " makes sense. So\n", "\n", " mathematically\n", "\n", ", \n", "\n", "1 +\n", "\n", " 1 equals\n", "\n", " 2. I\n", "\n", " guess\n", "\n", " that's the answer\n", "\n", ". There\n", "\n", "'s a\n", "\n", " famous book\n", "\n", " called \"Princ\n", "\n", "ipia\n", "\n", " Mathematica\" where\n", "\n", " they take\n", "\n", " hundreds\n", "\n", " of pages to\n", "\n", " prove that 1\n", "\n", "+\n", "\n", "1=\n", "\n", "2, but\n", "\n", " in everyday\n", "\n", " terms, it\n", "\n", "'s just \n", "\n", "2. Yeah\n", "\n", ", I think that\n", "\n", "'s right\n", "\n", ". No\n", "\n", " need to over\n", "\n", "complicate it\n", "\n", " unless stated\n", "\n", " otherwise. So\n", "\n", " the answer is\n", "\n", " 2.\n", "\n", "\n", "None\n", "\n", "None\n", "\n", "None\n", "\n", "None\n", "\n", "None\n", "\n", "None\n", "\n", "None\n", "\n", "None\n", "\n", "None\n", "\n", "None\n", "\n", "None\n", "\n", "None\n", "\n", "None\n", "\n", "None\n", "\n", "None\n", "\n", "None\n", "\n", "None\n", "\n", "None\n", "\n", "None\n", "\n", "None\n", "\n", "None\n", "\n", "None\n", "\n", "None\n", "\n", "None\n", "\n", "None\n", "\n", "None\n", "\n", "None\n", "\n", "None\n", "\n", "None\n", "\n", "None\n", "\n"]}, {"ename": "AttributeError", "evalue": "'ChoiceDelta' object has no attribute 'reasoning'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[30], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mfor\u001b[39;00m chunk \u001b[38;5;129;01min\u001b[39;00m stream:\n\u001b[0;32m----> 2\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m chunk\u001b[38;5;241m.\u001b[39mchoices[\u001b[38;5;241m0\u001b[39m]\u001b[38;5;241m.\u001b[39mdelta\u001b[38;5;241m.\u001b[39mreasoning:\n\u001b[1;32m      3\u001b[0m         \u001b[38;5;28mprint\u001b[39m(chunk\u001b[38;5;241m.\u001b[39mchoices[\u001b[38;5;241m0\u001b[39m]\u001b[38;5;241m.\u001b[39mdelta\u001b[38;5;241m.\u001b[39mreasoning)\n\u001b[1;32m      4\u001b[0m         \u001b[38;5;28mprint\u001b[39m()\n", "File \u001b[0;32m~/miniconda3/envs/mathlete/lib/python3.13/site-packages/pydantic/main.py:994\u001b[0m, in \u001b[0;36mBaseModel.__getattr__\u001b[0;34m(self, item)\u001b[0m\n\u001b[1;32m    991\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28msuper\u001b[39m()\u001b[38;5;241m.\u001b[39m\u001b[38;5;21m__getattribute__\u001b[39m(item)  \u001b[38;5;66;03m# Raises AttributeError if appropriate\u001b[39;00m\n\u001b[1;32m    992\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    993\u001b[0m     \u001b[38;5;66;03m# this is the current error\u001b[39;00m\n\u001b[0;32m--> 994\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mAttributeError\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mtype\u001b[39m(\u001b[38;5;28mself\u001b[39m)\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m\u001b[38;5;132;01m!r}\u001b[39;00m\u001b[38;5;124m object has no attribute \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mitem\u001b[38;5;132;01m!r}\u001b[39;00m\u001b[38;5;124m'\u001b[39m)\n", "\u001b[0;31mAttributeError\u001b[0m: 'ChoiceDelta' object has no attribute 'reasoning'"]}], "source": ["async for chunk in stream:\n", "    if chunk.choices[0].delta.reasoning:\n", "        print(chunk.choices[0].delta.reasoning)\n", "        print()\n", "    if chunk.choices[0].delta.content:\n", "        print(chunk.choices[0].delta.content)\n", "        print()"]}, {"cell_type": "code", "execution_count": 2, "id": "d0185be5-487e-42b5-8304-31cf9a8add44", "metadata": {}, "outputs": [], "source": ["ANALYZE_SYSTEM_PROMPT = \"\"\"\n", "You are an expert in mathematical proof verification. Your task is to analyze a given proof step by step, ensuring mathematical correctness while allowing for reasonable informal reasoning when clearly motivated. Your evaluation must be grounded in the exact structure of the user's proof and provide actionable, mathematically sound feedback.\n", "\n", "### **Core Analytical Process**\n", "\n", "1. **Step-by-Step Logical Review**\n", "   - Evaluate each step **individually and in sequence**\n", "   - Identify **incorrect logical inferences, invalid steps, or missing justifications**\n", "   - Allow concise or heuristic steps if they are mathematically valid and contextually appropriate\n", "\n", "2. **Mathematical Principle Verification**\n", "   - Ensure theorems and results are **applied correctly and within their valid scope**\n", "   - Accept standard informal invocations of results (e.g., AM-GM, mean inequalities, symmetry arguments) if their application is justified and leads to correct results\n", "   - Encourage clarity of reasoning, rather than rigid stylistic adherence\n", "\n", "3. **Computation and Algebraic Checking**\n", "   - Confirm numerical calculations, algebraic manipulations, and simplifications\n", "   - Flag arithmetic, sign, or structural errors in symbolic derivations\n", "\n", "4. **Structural Soundness & Completeness**\n", "   - Ensure all cases, special conditions, and boundary cases are addressed\n", "   - Identify any missing, implicit, or unjustified claims but do not penalize concise or elegant reasoning that can be easily verified.\n", "\n", "### **Error Identification Criteria**\n", "\n", "- **Errors must be explicitly verifiable** within the proof’s actual steps\n", "- **Error locations must refer to the proof’s exact wording** and notation\n", "- **No speculative, interpretative, or non-mathematical errors** should be reported\n", "- **It is possible that no errors exist in the proof.** If the proof is fully correct, return an empty list:\n", "\n", "```json\n", "{\n", "  \"mathematical_errors\": []\n", "}\n", "```\n", "\n", "---\n", "\n", "### **Error Reporting Format (JSON Output)**\n", "\n", "For each error found, return:\n", "\n", "```json\n", "{\n", "  \"mathematical_errors\": [\n", "    {\n", "      \"location_in_proof\": \"Exact step in user’s proof (quoted verbatim)\",\n", "      \"severity\": \"minor\" | \"significant\" | \"critical\",\n", "      \"error_type\": \"logical_reasoning\" | \"theorem_application\" | \"computational\" | \"case_analysis\",\n", "      \"explanation\": \"Mathematically rigorous explanation grounded in proof’s exact wording and structure\",\n", "      \"suggested_hint\": \"Precise mathematical guidance on correcting the error\"\n", "    }\n", "  ]\n", "}\n", "```\n", "\n", "---\n", "\n", "### **STRICT REQUIREMENTS**\n", "\n", "🚫 **DO NOT fabricate or infer errors where none exist**\n", "🚫 **DO NOT provide vague locations (e.g., 'entire proof' or 'general step')**\n", "🚫 **DO NOT report ambiguous or debatable issues unless clearly incorrect**\n", "\n", "✅ **Always reference the user’s proof directly**\n", "✅ **Always provide mathematically sound reasoning and correction paths**\n", "✅ **If no error exists, return an empty error list**\n", "\n", "---\n", "\n", "### **Final Reminder**\n", "\n", "- **If no errors exist, return an empty list.** Do NOT assume that an error must be found.\n", "- **All feedback must be grounded in the user's proof and reference their exact statements.**\"\"\"\n", "\n", "system_message = ANALYZE_SYSTEM_PROMPT"]}, {"cell_type": "code", "execution_count": 5, "id": "55b02b1d-ee22-4417-83e6-48fc9e399936", "metadata": {}, "outputs": [], "source": ["import textwrap\n", "from together import AsyncTogether\n", "\n", "user_message = textwrap.dedent(\n", "    f\"\"\"Problem Statement:\n", "    If 2x - 10 = 0, how much is x?\n", "\n", "    User Proof Tentative:\n", "    If 2x - 10 = 0, then 2x = 8, and dividing both sides, we get x = 8/2 = 4.\n", "    \"\"\"\n", ")\n", "message_content = f\"{system_message}\\n{user_message}\"\n", "\n", "thinking_complete = False\n", "result_content = \"\"\n", "client = AsyncTogether(api_key=\"5b559ecc82f0044fcd8089c436f2ff7ba40299d06ab4aee4eb7506ac5794ba3e\")\n", "\n", "completion = await client.chat.completions.create(\n", "    model=\"deepseek-ai/DeepSeek-R1\",\n", "    messages=[{\"role\": \"user\", \"content\": message_content}],\n", "    temperature=0.6,\n", "    top_p=0.95,\n", "    stream=,\n", ")"]}, {"cell_type": "code", "execution_count": 6, "id": "eb4b0b2b-c3fb-4fd2-8064-365d2a6c24b8", "metadata": {}, "outputs": [{"data": {"text/plain": ["'<think>\\nFirst, I need to analyze the user\\'s proof step by step. The problem is: If \\\\(2x - 10 = 0\\\\), how much is \\\\(x\\\\)?\\n\\nThe user\\'s proof is: \"If 2x - 10 = 0, then 2x = 8, and dividing both sides, we get x = 8/2 = 4.\"\\n\\nNow, I\\'ll break down the proof into steps:\\n\\n1. Start with \\\\(2x - 10 = 0\\\\).\\n\\n2. Then, they say \"then 2x = 8\". This seems to be an error because if \\\\(2x - 10 = 0\\\\), adding 10 to both sides should give \\\\(2x = 10\\\\), not \\\\(2x = 8\\\\). But in the proof, they wrote \\\\(2x = 8\\\\).\\n\\n3. Then, \"dividing both sides, we get x = 8/2 = 4\". If \\\\(2x = 8\\\\) were correct, then x = 4 would follow, but since the first step is wrong, this is incorrect.\\n\\nLet me verify the algebra.\\n\\nGiven: \\\\(2x - 10 = 0\\\\)\\n\\nAdd 10 to both sides: \\\\(2x = 10\\\\)\\n\\nThen divide both sides by 2: \\\\(x = 10 / 2 = 5\\\\)\\n\\nBut in the proof, they said \\\\(2x = 8\\\\), which is not right. So there\\'s a computational error here.\\n\\nNow, I need to check if this is an error according to the criteria.\\n\\n- **Location in proof**: The exact step is \"then 2x = 8\". I should quote verbatim.\\n\\n- **Severity**: This is a computational error that leads to the wrong answer, so it\\'s probably significant or critical. Since it changes the result, I\\'d say significant.\\n\\n- **Error type**: Computational, as it\\'s a mistake in arithmetic or algebra.\\n\\n- **Explanation**: The equation \\\\(2x - 10 = 0\\\\) implies that adding 10 to both sides yields \\\\(2x = 10\\\\), not \\\\(2x = 8\\\\). This is a misstep in solving the equation.\\n\\n- **Suggested hint**: Correct the step to: from \\\\(2x - 10 = 0\\\\), add 10 to both sides to get \\\\(2x = 10\\\\), then divide by 2 to find \\\\(x = 5\\\\).\\n\\nAlso, ensure that the proof is evaluated based on what\\'s written, not what might be intended.\\n\\nThe proof says \"then 2x = 8\", which is clearly wrong because 10 - 10 is 0, but here it\\'s like they subtracted 2 or something. Actually, 10 minus 2 is 8, but that doesn\\'t make sense. It might be a simple arithmetic mistake.\\n\\nPerhaps they meant to write something else, but based on the text, it\\'s incorrect.\\n\\nNow, is there any other part? The division part is correct if 2x=8, but since the premise is wrong, it\\'s invalid.\\n\\nBut I should only report the error where it occurs, which is in \"then 2x = 8\".\\n\\nFinally, if no other errors, that\\'s it.\\n\\nSo, for the JSON output:\\n\\n{\\n  \"mathematical_errors\": [\\n    {\\n      \"location_in_proof\": \"then 2x = 8\",\\n      \"severity\": \"significant\",\\n      \"error_type\": \"computational\",\\n      \"explanation\": \"From the equation \\\\(2x - 10 = 0\\\\), adding 10 to both sides correctly gives \\\\(2x = 10\\\\), not \\\\(2x = 8\\\\). The value 8 is incorrect as it does not satisfy the original equation.\",\\n      \"suggested_hint\": \"After \\\\(2x - 10 = 0\\\\), add 10 to both sides to obtain \\\\(2x = 10\\\\). Then divide both sides by 2 to find \\\\(x = 5\\\\).\"\\n    }\\n  ]\\n}\\n\\nI should confirm: does 2x=8 satisfy the original? 2*4 -10 = 8-10= -2 ≠0, so no. Whereas 2*5-10=10-10=0, correct.\\n\\nAlso, in the proof, they say \"dividing both sides, we get x=8/2=4\", which is correct algebra for 2x=8, but since 2x=8 is wrong, the error is in that step.\\n\\nBut the location is \"then 2x=8\", as that\\'s where the mistake is made.\\n\\nI think that\\'s the only error.\\n</think>\\n### **Analysis of User\\'s Proof**\\n\\nThe user\\'s proof attempts to solve the equation \\\\(2x - 10 = 0\\\\) for \\\\(x\\\\). The proof is concise and follows a logical sequence, but it contains a computational error in the algebraic manipulation. Below is the step-by-step evaluation based on the core analytical process.\\n\\n#### **Step-by-Step Logical Review**\\n1. **Step 1: \"If 2x - 10 = 0\"**  \\n   - This is the given equation, correctly stated. No error.\\n\\n2. **Step 2: \"then 2x = 8\"**  \\n   - This step is incorrect. Starting from \\\\(2x - 10 = 0\\\\), adding 10 to both sides should yield \\\\(2x = 10\\\\), not \\\\(2x = 8\\\\). This is a computational error, as the value 8 is not derived correctly from the equation.  \\n   - **Consequence**: The error propagates to the next step, leading to an incorrect value for \\\\(x\\\\).\\n\\n3. **Step 3: \"dividing both sides, we get x = 8/2 = 4\"**  \\n   - This step is algebraically correct if \\\\(2x = 8\\\\) were true (as dividing both sides by 2 gives \\\\(x = 4\\\\)). However, since Step 2 is invalid, this result is also invalid. No additional error is introduced here; the issue stems solely from Step 2.\\n\\n#### **Mathematical Principle Verification**\\n- The proof uses basic algebraic principles (isolating the variable \\\\(x\\\\) by inverse operations), which are appropriate for solving linear equations.\\n- The error is purely computational and does not involve misapplication of theorems or principles.\\n\\n#### **Computation and Algebraic Checking**\\n- Original equation: \\\\(2x - 10 = 0\\\\)  \\n  - Correct manipulation: Add 10 to both sides → \\\\(2x = 10\\\\)  \\n  - User\\'s manipulation: Incorrectly states \\\\(2x = 8\\\\) (likely due to an arithmetic mistake, such as miscomputing \\\\(0 + 10\\\\) as 8 or subtracting 2 instead of adding 10).  \\n- Verification: Substituting \\\\(x = 4\\\\) into the original equation gives \\\\(2(4) - 10 = 8 - 10 = -2 \\\\neq 0\\\\), confirming the error. The correct solution \\\\(x = 5\\\\) satisfies \\\\(2(5) - 10 = 10 - 10 = 0\\\\).\\n\\n#### **Structural Soundness & Completeness**\\n- The proof structure is logically sequential and addresses the core equation. No missing cases or boundary conditions are relevant here, as this is a simple linear equation with a unique solution.\\n\\n### **Error Identification**\\n- Only one error is present, located in Step 2. It is explicitly verifiable through algebraic verification and substitution.\\n\\n```json\\n{\\n  \"mathematical_errors\": [\\n    {\\n      \"location_in_proof\": \"then 2x = 8\",\\n      \"severity\": \"significant\",\\n      \"error_type\": \"computational\",\\n      \"explanation\": \"From the equation \\\\(2x - 10 = 0\\\\), adding 10 to both sides must yield \\\\(2x = 10\\\\) to maintain equality. The user incorrectly wrote \\\\(2x = 8\\\\), which is a computational error as \\\\(0 + 10 = 10\\\\), not 8. This error invalidates the subsequent solution.\",\\n      \"suggested_hint\": \"Correct the step by adding 10 to both sides of \\\\(2x - 10 = 0\\\\) to get \\\\(2x = 10\\\\). Then divide both sides by 2 to find \\\\(x = 5\\\\).\"\\n    }\\n  ]\\n}\\n```'"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["completion.choices[0].message.content"]}, {"cell_type": "code", "execution_count": null, "id": "50a5e5de-2c93-4ed5-9f17-81bcce819cd9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}