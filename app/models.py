from datetime import datetime
from typing import Any, List, Optional

from prisma import Prisma
from pydantic import BaseModel, ConfigDict


class ProblemBase(BaseModel):
    # v4 UUID unique to each problem
    uuid: str
    title: str

    # Problem statement informal (english, Markdown/LaTeX)
    informal_statement: str

    # A proof in english
    informal_proof: Optional[str] = None

    # Lean 4 statement formalized ending with `:= by sorry`
    formal_statement: Optional[str] = None

    # Full Lean 4 proof with imports / statement and no sorry's
    formal_proof: Optional[str] = None

    # Domain of mathematics: algebra, geometry, number theory, etc.
    domain: Optional[str] = None

    exam: Optional[str] = None
    exam_category: Optional[List[str]] = None
    year: Optional[str] = None
    level: Optional[str] = None
    difficulty_level: Optional[float] = None
    hint_nudge: Optional[str] = None
    hint_conceptual: Optional[str] = None
    hint_detailed: Optional[str] = None

    @staticmethod
    async def fetch_unique_field_values(
        prisma_client: Prisma, field_name: str
    ) -> List[Any]:
        """
        Fetches a list of unique, non-null values for a specified field
        from the 'problems' table.

        Args:
            prisma_client: An active Prisma client instance.
            field_name: The name of the field in the 'problems' table
                        for which to fetch unique values.

        Returns:
            A list of unique, non-null values found for the specified field.
            Returns an empty list if the field does not exist, all distinct
            values are null, or no records are found.
        """
        if not field_name:
            return []

        # Fetch records with only the specified field, ensuring distinct values
        records_with_distinct_field = await prisma_client.query_raw(
            query=f"select distinct {field_name} from problems"
        )

        unique_values = []
        for record in records_with_distinct_field:
            if field_name in record:
                value = record[field_name]
                if value is not None:
                    unique_values.append(value)

        if not unique_values and records_with_distinct_field:
            print(
                f"Note: For the field '{field_name}', all distinct values found were None."
            )

        return unique_values


class Problem(ProblemBase):
    id: int
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class EventProblem(BaseModel):
    event_id: int
    problem_id: int
    revealed_at: Optional[datetime] = None
    problem: Problem


class Event(BaseModel):
    id: int
    slug: str
    title: str
    logo_url: str


class EventWithProblems(Event):
    problems: List[EventProblem]


class QuickPick(BaseModel):
    id: int
    created_at: datetime
    updated_at: datetime
    label: str
    enabled: bool
    search_query: Optional[str] = None
    event_id: Optional[int] = None
    event: Optional[Event] = None


class BugReportBody(BaseModel):
    statement: str
    feedback: Optional[str] = None
    problem_uuid: Optional[str] = None


class UserProofAttempt(BaseModel):
    problem_statement: str
    proof_text: str


class ProofAnalysisRequest(BaseModel):
    attempt: UserProofAttempt


class MathematicalAnalysisResult(BaseModel):
    mathematical_errors: List[str]


class RefinementRequest(BaseModel):
    attempt: UserProofAttempt
    initial_analysis: str


class AttemptCheckRequest(BaseModel):
    attempt: UserProofAttempt


class AttemptCheckResult(BaseModel):
    is_valid: bool
    reason: str
