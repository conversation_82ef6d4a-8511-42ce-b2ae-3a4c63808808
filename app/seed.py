import asyncio
import uuid
from typing import Any, Dict, List

import numpy as np
import pandas as pd
from prisma import Prisma


async def process_record(record: Dict[Any, Any]) -> Dict[Any, Any]:

    if "uuid" not in record or not record["uuid"]:
        record["uuid"] = str(uuid.uuid4())

    for array_field in [
        "exam_category",
        "languages",
        "mathematical_techniques",
        "mathematical_results",
        "non_mathematical_text",
    ]:
        if array_field in record:
            if isinstance(record[array_field], str):
                try:
                    value = (
                        record[array_field]
                        .strip("[]")
                        .replace("'", "")
                        .replace('"', "")
                    )
                    record[array_field] = [
                        item.strip() for item in value.split(",") if item.strip()
                    ]
                except:
                    record[array_field] = []
            elif record[array_field] is None:
                record[array_field] = []
            elif not isinstance(record[array_field], list):
                record[array_field] = [str(record[array_field])]
        else:
            record[array_field] = []

    for bool_field in ["is_valid_no_sorry", "is_valid_with_sorry"]:
        if bool_field in record:
            if isinstance(record[bool_field], str):
                record[bool_field] = record[bool_field].lower() in [
                    "true",
                    "yes",
                    "1",
                    "t",
                    "y",
                ]

    for field in [
        "id",
        "created_at",
        "updated_at",
        "multiple_solutions",
        "natural_language",
        "source",
        "question_type",
        "problem_number",
        "exam_category",
        "is_valid_no_sorry",
        "is_valid_with_sorry",
        "languages",
        "mathematical_techniques",
        "mathematical_results",
        "non_mathematical_text",
        "difficulty_explanation",
    ]:
        if field in record:
            del record[field]

    return record


async def insert_batch(prisma: Prisma, batch: List[Dict[Any, Any]]) -> None:
    """Insert a batch of records into the database."""
    tasks = []
    for record in batch:
        if "informal_statement" not in record or not record["informal_statement"]:
            print(
                f"Skipping record with uuid {record.get('uuid', 'unknown')}: missing required 'problem' field"
            )
            continue

        task = prisma.problems.create(data=record)  # type: ignore
        tasks.append(task)

    results = await asyncio.gather(*tasks, return_exceptions=True)

    for i, result in enumerate(results):
        if isinstance(result, Exception):
            print(
                f"Error creating problem with uuid {batch[i].get('uuid', 'unknown')}: {str(result)}"
            )


async def seed_events_and_quick_picks(prisma: Prisma) -> int | None:
    """Seed events and quick_picks data."""
    try:
        # Create a sample event
        event = await prisma.events.create(
            data={
                "slug": "imo-2006",
                "title": "🏆 2006 IMO - International Mathematical Olympiad (fake)",
                "logo_url": "https://www.imo-official.org/IMOLogo.gif",
            }
        )
        print(f"Created event: {event.title}")

        # Create quick_picks entries
        quick_picks_data = [
            {
                "label": "Try Inequalities",
                "search_query": "I want to try myself on inequalities problems",
                "enabled": True,
            },
            {
                "label": "Geometry Challenges",
                "search_query": "Show me geometry problems to practice",
                "enabled": True,
            },
            {
                "label": "Number Theory Fun",
                "search_query": "I want to explore number theory problems",
                "enabled": True,
            },
            {
                "label": "Algebra Practice",
                "search_query": "Give me algebra problems to solve",
                "enabled": True,
            },
            {"label": "🏆 2006 IMO Challenge", "event_id": event.id, "enabled": True},
        ]

        for pick_data in quick_picks_data:
            quick_pick = await prisma.quick_picks.create(data=pick_data)
            print(f"Created quick pick: {quick_pick.label}")

        return event.id
    except Exception as e:
        print(f"Error seeding events and quick_picks: {str(e)}")


async def seed_database(batch_size: int = 100, max_concurrent_batches: int = 5):
    """Seed the database with data from a parquet file using parallel batching."""
    prisma = Prisma()
    await prisma.connect()

    try:
        # First, seed events and quick_picks
        event_id = await seed_events_and_quick_picks(prisma)

        df = pd.read_parquet("../mathlete/data/final_data.parquet")
        df = df.rename(columns={"problem_name": "title"})
        df = df.rename(columns={"problem": "informal_statement"})
        df = df.rename(columns={"informal_solution": "informal_proof"})
        df = df.rename(columns={"lean_code": "formal_statement"})
        df = df.rename(columns={"lean4_solution": "formal_proof"})
        df = df.rename(columns={"problem_type": "domain"})

        def convert_empty_arrays(value):
            if isinstance(value, np.ndarray):
                return value.tolist()
            return value

        for col in df.columns:
            df[col] = df[col].apply(convert_empty_arrays)

        records = df.replace({pd.NA: None}).to_dict("records")

        processed_records = []
        for record in records:
            for key, value in record.items():
                if isinstance(value, str):
                    record[key] = value.replace("\x00", "")

            processed_record = await process_record(record)
            if (
                "informal_statement" in processed_record
                and processed_record["informal_statement"]
            ):
                processed_records.append(processed_record)

        batches = [
            processed_records[i : i + batch_size]
            for i in range(0, len(processed_records), batch_size)
        ]
        print(f"Processing {len(processed_records)} records in {len(batches)} batches")

        semaphore = asyncio.Semaphore(max_concurrent_batches)

        async def process_batch(batch):
            async with semaphore:
                await insert_batch(prisma, batch)

        batch_tasks = [process_batch(batch) for batch in batches]
        await asyncio.gather(*batch_tasks)

        if event_id:
            problem_ids = [2071, 2072, 2077, 2078]

            for problem_id in problem_ids:
                try:
                    await prisma.event_problems.create(
                        data={
                            "event_id": event_id,
                            "problem_id": problem_id,
                            "revealed_at": "2006-07-12T00:00:00Z",  # IMO 2006 date
                        }
                    )
                    print(f"Linked problem {problem_id} to event {event_id}")
                except Exception as e:
                    print(f"Error linking problem {problem_id} to event: {str(e)}")

        print(
            f"Seed completed successfully: {len(processed_records)} records processed"
        )

    except Exception as e:
        print(f"Error during seed process: {str(e)}")

    finally:
        await prisma.disconnect()


if __name__ == "__main__":
    asyncio.run(seed_database())
