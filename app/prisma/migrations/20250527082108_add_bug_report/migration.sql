-- CreateTable
CREATE TABLE "bug_report" (
    "id" SERIAL NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "problem_id" INTEGER,
    "statement" TEXT NOT NULL,
    "feedback" TEXT,

    CONSTRAINT "bug_report_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "bug_report" ADD CONSTRAINT "bug_report_problem_id_fkey" FOREIGN KEY ("problem_id") REFERENCES "problems"("id") ON DELETE SET NULL ON UPDATE CASCADE;
