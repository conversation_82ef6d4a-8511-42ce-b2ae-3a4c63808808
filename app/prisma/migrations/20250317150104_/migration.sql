-- CreateTable
CREATE TABLE "problems" (
    "id" SERIAL NOT NULL,
    "uuid" TEXT NOT NULL,
    "problem" TEXT NOT NULL,
    "informal_solution" TEXT,
    "lean_code" TEXT,
    "lean4_solution" TEXT,
    "natural_language" TEXT,
    "source" TEXT,
    "question_type" TEXT,
    "problem_type" TEXT,
    "problem_number" INTEGER,
    "exam" TEXT,
    "exam_category" TEXT,
    "year" INTEGER,
    "level" TEXT,
    "is_valid_no_sorry" BOOLEAN,
    "is_valid_with_sorry" BOOLEAN,
    "languages" TEXT[],
    "mathematical_techniques" TEXT[],
    "mathematical_results" TEXT[],
    "non_mathematical_text" TEXT,
    "difficulty_level" INTEGER,
    "difficulty_explanation" TEXT,
    "hint_subtle" TEXT,
    "hint_conceptual" TEXT,
    "hint_strategic" TEXT,
    "hint_detailed" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "problems_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "problems_uuid_key" ON "problems"("uuid");
