/*
  Warnings:

  - You are about to drop the column `level_tier` on the `problems` table. All the data in the column will be lost.

*/
-- CreateTable
CREATE TABLE "quick_picks" (
    "id" SERIAL NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "label" TEXT NOT NULL,
    "enabled" BOOLEAN NOT NULL DEFAULT true,
    "search_query" TEXT,
    "event_id" INTEGER,

    CONSTRAINT "quick_picks_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "quick_picks" ADD CONSTRAINT "quick_picks_event_id_fkey" FOREIGN KEY ("event_id") REFERENCES "events"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Check
ALTER TABLE "quick_picks" ADD CONSTRAINT "quick_picks_check" CHECK (
    (search_query IS NOT NULL AND event_id IS NULL) OR
    (search_query IS NULL AND event_id IS NOT NULL)
);
