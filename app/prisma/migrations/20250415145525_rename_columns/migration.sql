/*
  Warnings:

  - You are about to drop the column `difficulty_explanation` on the `problems` table. All the data in the column will be lost.
  - You are about to drop the column `exam_category` on the `problems` table. All the data in the column will be lost.
  - You are about to drop the column `hint_strategic` on the `problems` table. All the data in the column will be lost.
  - You are about to drop the column `hint_subtle` on the `problems` table. All the data in the column will be lost.
  - You are about to drop the column `informal_solution` on the `problems` table. All the data in the column will be lost.
  - You are about to drop the column `is_valid_no_sorry` on the `problems` table. All the data in the column will be lost.
  - You are about to drop the column `is_valid_with_sorry` on the `problems` table. All the data in the column will be lost.
  - You are about to drop the column `languages` on the `problems` table. All the data in the column will be lost.
  - You are about to drop the column `lean4_solution` on the `problems` table. All the data in the column will be lost.
  - You are about to drop the column `lean_code` on the `problems` table. All the data in the column will be lost.
  - You are about to drop the column `mathematical_results` on the `problems` table. All the data in the column will be lost.
  - You are about to drop the column `mathematical_techniques` on the `problems` table. All the data in the column will be lost.
  - You are about to drop the column `natural_language` on the `problems` table. All the data in the column will be lost.
  - You are about to drop the column `non_mathematical_text` on the `problems` table. All the data in the column will be lost.
  - You are about to drop the column `problem` on the `problems` table. All the data in the column will be lost.
  - You are about to drop the column `problem_number` on the `problems` table. All the data in the column will be lost.
  - You are about to drop the column `problem_type` on the `problems` table. All the data in the column will be lost.
  - You are about to drop the column `question_type` on the `problems` table. All the data in the column will be lost.
  - You are about to drop the column `source` on the `problems` table. All the data in the column will be lost.
  - Added the required column `informal_statement` to the `problems` table without a default value. This is not possible if the table is not empty.
  - Added the required column `title` to the `problems` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "problems" DROP COLUMN "difficulty_explanation",
DROP COLUMN "exam_category",
DROP COLUMN "hint_strategic",
DROP COLUMN "hint_subtle",
DROP COLUMN "informal_solution",
DROP COLUMN "is_valid_no_sorry",
DROP COLUMN "is_valid_with_sorry",
DROP COLUMN "languages",
DROP COLUMN "lean4_solution",
DROP COLUMN "lean_code",
DROP COLUMN "mathematical_results",
DROP COLUMN "mathematical_techniques",
DROP COLUMN "natural_language",
DROP COLUMN "non_mathematical_text",
DROP COLUMN "problem",
DROP COLUMN "problem_number",
DROP COLUMN "problem_type",
DROP COLUMN "question_type",
DROP COLUMN "source",
ADD COLUMN     "domain" TEXT,
ADD COLUMN     "formal_proof" TEXT,
ADD COLUMN     "formal_statement" TEXT,
ADD COLUMN     "hint_nudge" TEXT,
ADD COLUMN     "informal_proof" TEXT,
ADD COLUMN     "informal_statement" TEXT NOT NULL,
ADD COLUMN     "title" TEXT NOT NULL,
ALTER COLUMN "difficulty_level" SET DATA TYPE DOUBLE PRECISION;
