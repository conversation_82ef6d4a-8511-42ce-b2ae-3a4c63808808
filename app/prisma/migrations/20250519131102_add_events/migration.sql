-- CreateTable
CREATE TABLE "events" (
    "id" SERIAL NOT NULL,
    "slug" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "logo_url" TEXT NOT NULL,

    CONSTRAINT "events_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "event_problems" (
    "event_id" INTEGER NOT NULL,
    "problem_id" INTEGER NOT NULL,
    "revealed_at" TIMESTAMP(3),

    CONSTRAINT "event_problems_pkey" PRIMARY KEY ("event_id","problem_id")
);

-- CreateIndex
CREATE UNIQUE INDEX "events_slug_key" ON "events"("slug");

-- AddForeign<PERSON>ey
ALTER TABLE "event_problems" ADD CONSTRAINT "event_problems_event_id_fkey" FOREIGN KEY ("event_id") REFERENCES "events"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- Add<PERSON><PERSON><PERSON><PERSON>ey
ALTER TABLE "event_problems" ADD CONSTRAINT "event_problems_problem_id_fkey" FOREIGN KEY ("problem_id") REFERENCES "problems"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
