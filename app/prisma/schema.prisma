generator client {
  provider             = "prisma-client-py"
  recursive_type_depth = 5
}

generator client-js {
  provider = "prisma-client-js"
  output   = "../../webapp/src/types/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model problems {
  id    Int    @id @default(autoincrement())
  uuid  String @unique
  title String

  informal_statement String
  informal_proof     String?
  formal_statement   String?
  formal_proof       String?
  domain             String?
  exam               String?
  year               String?
  level              String?
  difficulty_level   Float?
  hint_nudge         String?
  hint_conceptual    String?
  hint_detailed      String?
  created_at         DateTime @default(now())
  updated_at         DateTime @updatedAt

  events     event_problems[]
  bug_report bug_report[]
}

model events {
  id   Int    @id @default(autoincrement())
  slug String @unique

  title    String
  logo_url String

  problems    event_problems[]
  quick_picks quick_picks[]
}

model event_problems {
  event    events @relation(fields: [event_id], references: [id])
  event_id Int

  problem    problems @relation(fields: [problem_id], references: [id])
  problem_id Int

  revealed_at DateTime

  @@id([event_id, problem_id])
}

model quick_picks {
  id         Int      @id @default(autoincrement())
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  label   String
  enabled Boolean @default(true)

  search_query String?
  event_id     Int?
  event        events? @relation(fields: [event_id], references: [id])
}

model bug_report {
  id         Int      @id @default(autoincrement())
  created_at DateTime @default(now())

  problem_id Int?
  problem    problems? @relation(fields: [problem_id], references: [id])

  statement String
  feedback  String?
}
