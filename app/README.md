# Axion app

## Local development

> [CAUTION!]
> Make sure you are located in the `app` directory.

Copy the environment variables template and adjust to your setup.
Leave as is if you don't have conflicting ports.

```sh
cp .env.template .env
```

Create a Python environment (conda, venv or other) and install requirements:

```sh
# Example with conda
conda create -n axion
conda activate axion
conda install pip

# Install backend requirements
pip install -r requirements.txt
```

Launch a Docker container with a PostgreSQL container with:

```sh
docker compose up -d
```

Create Python types with:

```sh
prisma generate
```

And imprint the Prisma schema to the database:

```sh
prisma migrate deploy
```

To seed the initial data from the Parquet file, run:

```sh
python seed.py
```

To reload the data (re-populate seed in case of an error), first reset the database with (asks for confirmation):

```sh
prisma migrate reset
```

If you modified the Prisma schema, generate the corresponding migrations with:

```sh
prisma migrate dev

```

Then run the FastAPI server with:

```sh
python main.py
```

To run the tests, run (after seeding the database):

```sh
pytest
```
