from enum import Enum
import math
from typing import Dict, List, Tuple


class DifficultyBias(str, Enum):
    EASY = "easy"
    MEDIUM = "medium"
    HARD = "hard"


difficulty_bias_to_difficulty_level = {
    DifficultyBias.EASY: {
        "gte": 1,
        "lt": 4,
    },
    DifficultyBias.MEDIUM: {
        "gte": 4,
        "lt": 7,
    },
    DifficultyBias.HARD: {
        "gte": 7,
        "lt": 10,
    },
}


def calculate_difficulty_distribution(
    limit: int, difficulty_bias: DifficultyBias, skip: int = 0
) -> List[Tuple[DifficultyBias, int, int]]:
    """
    According to the given total limit (limit), difficulty bias (difficulty_bias) and global skip number (skip),
    calculate the number of problems to be obtained and the number of problems to be skipped for each of the three difficulty intervals of easy, medium and hard.

    Args:
    limit: The total number of problems expected to be obtained on the current page.
    difficulty_bias: difficulty bias string, such as "easy", "medium", "hard".
    skip: The total number of global problems to be skipped from the beginning of all eligible problems.

    Returns:
    A tuple of three tuples, each containing a difficulty bias, the number of problems to be obtained, and the number of problems to be skipped.
    [
        (DifficultyBias.EASY, total_easy_problems, total_easy_skip),
        (DifficultyBias.MEDIUM, total_medium_problems, total_medium_skip),
        (DifficultyBias.HARD, total_hard_problems, total_hard_skip)
    ]

    example:
    limit = 4
    difficulty_bias = DifficultyBias.EASY
    skip = 0
    return [
        (DifficultyBias.EASY, 2, 0),
        (DifficultyBias.MEDIUM, 1, 1),
        (DifficultyBias.HARD, 1, 0)
    ]

    limit = 4
    difficulty_bias = DifficultyBias.EASY
    skip = 4
    return [
        (DifficultyBias.EASY, 2, 2),
        (DifficultyBias.MEDIUM, 1, 1),
        (DifficultyBias.HARD, 1, 1)
    ]
    """
    ratios: Dict[str, Tuple[int, int, int]] = {
        "easy": (2, 1, 1),  # [E,E,M,H]
        "medium": (1, 2, 1),  # [E,M,M,H]
        "hard": (1, 1, 2),  # [E,M,H,H]
    }
    r_e, r_m, r_h = ratios[difficulty_bias.value]
    total_ratio_parts = r_e + r_m + r_h

    pattern = (
        [DifficultyBias.EASY] * r_e
        + [DifficultyBias.MEDIUM] * r_m
        + [DifficultyBias.HARD] * r_h
    )

    skip_counts = {
        DifficultyBias.EASY: 0,
        DifficultyBias.MEDIUM: 0,
        DifficultyBias.HARD: 0,
    }
    for i in range(skip):
        difficulty = pattern[i % total_ratio_parts]
        skip_counts[difficulty] += 1

    page_counts = {
        DifficultyBias.EASY: 0,
        DifficultyBias.MEDIUM: 0,
        DifficultyBias.HARD: 0,
    }
    for i in range(skip, skip + limit):
        difficulty = pattern[i % total_ratio_parts]
        page_counts[difficulty] += 1

    return [
        (
            DifficultyBias.EASY,
            page_counts[DifficultyBias.EASY],
            skip_counts[DifficultyBias.EASY],
        ),
        (
            DifficultyBias.MEDIUM,
            page_counts[DifficultyBias.MEDIUM],
            skip_counts[DifficultyBias.MEDIUM],
        ),
        (
            DifficultyBias.HARD,
            page_counts[DifficultyBias.HARD],
            skip_counts[DifficultyBias.HARD],
        ),
    ]
