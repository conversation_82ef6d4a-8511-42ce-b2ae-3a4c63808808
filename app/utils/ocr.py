import base64
import json
import os
from abc import ABC, abstractmethod

import httpx
from openai import AsyncOpenAI
from pydantic import BaseModel, Field

_PROMPT = """\
Extract all visible content from the provided image and output it in clean, structured Markdown format.

- Do not include or infer any content that is not explicitly visible in the image. No assumptions or completions.
- Convert all mathematical formulas into proper LaTeX syntax, enclosed in inline $...$ or block $$...$$ as appropriate.
- And indicate whether the image is handwritten and printed.
- At the end, provide a confidence score (between 0.0 and 1.0) for the accuracy of the extracted content.\
"""


class OCRResult(BaseModel):
    text: str = Field(description="The visible text recognized from the image.")
    is_handwritten: bool = Field(
        description="Indicates whether the text is handwritten."
    )
    is_printed: bool = Field(
        description="Indicates whether the text is printed (e.g., typed or typeset)."
    )
    confidence: float = Field(
        description="Confidence score of the OCR result, ranging from 0.0 to 1.0.",
        ge=0.0,
        le=1.0,
    )


class OCR(ABC):
    @abstractmethod
    async def extract(self, image: bytes) -> OCRResult:
        pass


class OpenaiOCR(OCR):
    def __init__(self):
        self.openai_client = AsyncOpenAI()

    async def extract(
        self, image: bytes, model: str = "gpt-4.1-2025-04-14"
    ) -> OCRResult:
        # Convert image to base64
        base64_image = base64.b64encode(image).decode("utf-8")
        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": _PROMPT},
                    {
                        "type": "image_url",
                        "image_url": {"url": f"data:image/png;base64,{base64_image}"},
                    },
                ],
            }
        ]
        response = await self.openai_client.beta.chat.completions.parse(
            model=model,
            messages=messages,
            response_format=OCRResult,
        )

        return response.choices[0].message.parsed


class MathpixOCR(OCR):
    async def extract(self, image: bytes) -> OCRResult:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "https://api.mathpix.com/v3/text",
                files={"file": (None, image, None)},
                data={
                    "options_json": json.dumps(
                        {
                            "math_inline_delimiters": ["$", "$"],
                            "math_display_delimiters": ["$$", "$$"],
                            "rm_spaces": True,
                        }
                    )
                },
                headers={
                    "app_id": os.environ["MATHPIX_APP_ID"],
                    "app_key": os.environ["MATHPIX_APP_KEY"],
                },
            )
            response.raise_for_status()

        return OCRResult(**response.json())
