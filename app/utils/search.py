from typing import Any, List, Optional, overload
from openai import OpenAI
from pydantic import BaseModel, Field
from utils.openai_prompt import search_system_prompt, difficulty_semantics_map

openai_client = OpenAI()


class Filters(BaseModel):
    problem_type: Optional[str] = Field(default=None, description="The type of problem")
    question_type: Optional[str] = Field(
        default=None, description="The type of question"
    )
    difficulty: Optional[List[int]] = Field(
        default=None, description="The difficulty of the problem"
    )
    exam_category: Optional[str] = Field(default=None, description="The exam category")

    @overload
    def get(self, key: str) -> Any: ...

    @overload
    def get(self, key: str, default: Any) -> Any: ...

    def get(self, key: str, default: Any = ...) -> Any:
        """
        Get a filter value by key.
        """
        if default is ...:
            return getattr(self, key)
        else:
            return getattr(self, key, default)


class QueryDecomposition(BaseModel):
    filters: Optional[Filters] = Field(description="Filters for specific fields")
    valid_query: bool = Field(
        description="Indicates whether the query is considered valid after processing."
    )
    reason: Optional[str] = Field(
        default=None,
        description="Provides a reason for invalidity, if the query is not valid.",
    )


class BaseSearch:
    def __init__(self, query: str):
        self.query = query

    def get_filters(self) -> QueryDecomposition:
        pass


PROBLEM_TYPES = {
    "Algebra": ["algebra", "algebraic"],
    "Inequalities": ["inequalities", "inequality"],
    "Number Theory": ["number theory", "number theoretic", "numerical theory"],
    "Geometry": ["geometry", "geometric", "geometrical"],
    "Calculus": ["calculus"],
    "Combinatorics": ["combinatorics", "combinatorial", "counting"],
    "Logic and Puzzles": ["logic", "puzzle", "logical puzzle", "brain teaser"],
    "Other": ["other"],
}

QUESTION_TYPES = {
    "MCQ": ["mcq", "multiple choice", "multiple choice question", "multiple option"],
    "math-word-problem": [
        "word problem",
        "story problem",
        "textual problem",
        "application problem",
    ],
    "proof": ["proof", "prove that", "demonstrate that", "proof question"],
}

EXAM_CATEGORIES = {
    "International Contests": ["international", "international contests"],
    "Junior Olympiads": ["junior", "junior olympiad"],
    "National And Regional Contests": [
        "national",
        "regional",
        "national contest",
        "regional contest",
    ],
    "Team Selection Test": ["team selection", "selection test"],
    "Undergraduate Contests": ["undergraduate", "college", "university"],
}


class KeywordSearch(BaseSearch):
    def __init__(self, query: str):
        super().__init__(query)

    @property
    def query_decomposition(self) -> QueryDecomposition:
        """
        Get the filters for the keyword search.

        example:
        query: "find me a problem with a difficulty of easy and a problem type of Algebra and an exam category of HMMT"

        filters: Filters(difficulty=[1, 2], problem_type="Algebra", exam_category=None) # HMMT not in the EXAM_CATEGORIES
        """
        filters = Filters()

        lower_query = self.query.lower()

        for official_type, variations in PROBLEM_TYPES.items():
            if any(variation in lower_query for variation in variations):
                setattr(filters, "problem_type", official_type)
                break

        for official_type, variations in QUESTION_TYPES.items():
            if any(variation in lower_query for variation in variations):
                setattr(filters, "question_type", official_type)
                break

        for official_category, variations in EXAM_CATEGORIES.items():
            if any(variation in lower_query for variation in variations):
                setattr(filters, "exam_category", official_category)
                break

        for semantic_difficulty_name, value in difficulty_semantics_map.items():
            if (
                " " in semantic_difficulty_name
                and semantic_difficulty_name in lower_query
            ):
                setattr(filters, "difficulty", value)
                break

            if semantic_difficulty_name in set(lower_query.split(" ")):
                setattr(filters, "difficulty", value)
                break

        return QueryDecomposition(filters=filters, valid_query=True, reason="")


class SemanticSearch(BaseSearch):
    def __init__(self, query: str, model_name: str = "gpt-4.1-mini-2025-04-14"):
        super().__init__(query)
        self.model_name = model_name

    @property
    def query_decomposition(self) -> QueryDecomposition:
        user_prompt = f"""
        Decompose this query into filters for specific fields:
        Query: {self.query}
        """

        system_prompt = search_system_prompt

        response = openai_client.beta.chat.completions.parse(
            model=self.model_name,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt},
            ],
            response_format=QueryDecomposition,
        )

        query_decomposition = response.choices[0].message.parsed

        return query_decomposition
