FROM python:3.13-slim

ARG HTTP_PROXY
ARG HTTPS_PROXY

WORKDIR /app

RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY prisma ./prisma/
RUN pip install prisma
RUN prisma generate

COPY . .

EXPOSE 8000

ENV PYTHONPATH=/app

CMD exec uvicorn main:app --host 0.0.0.0 --port 8000